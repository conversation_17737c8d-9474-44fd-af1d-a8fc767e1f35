{"version": 3, "file": "default-node_modules_ionic_core_components_animation_js.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACuC;AAEvC,IAAIE,eAAe;AACnB,MAAMC,kBAAkB,GAAIC,EAAE,IAAK;EAC/B,IAAIF,eAAe,KAAKG,SAAS,EAAE;IAC/B,MAAMC,kBAAkB,GAAGF,EAAE,CAACG,KAAK,CAACC,aAAa,KAAKH,SAAS;IAC/D,MAAMI,oBAAoB,GAAGL,EAAE,CAACG,KAAK,CAACG,mBAAmB,KAAKL,SAAS;IACvEH,eAAe,GAAG,CAACI,kBAAkB,IAAIG,oBAAoB,GAAG,UAAU,GAAG,EAAE;EACnF;EACA,OAAOP,eAAe;AAC1B,CAAC;AACD,MAAMS,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,YAAY,EAAEC,KAAK,KAAK;EACvD,MAAMC,MAAM,GAAGF,YAAY,CAACG,UAAU,CAAC,WAAW,CAAC,GAAGb,kBAAkB,CAACS,OAAO,CAAC,GAAG,EAAE;EACtFA,OAAO,CAACL,KAAK,CAACU,WAAW,CAACF,MAAM,GAAGF,YAAY,EAAEC,KAAK,CAAC;AAC3D,CAAC;AACD,MAAMI,eAAe,GAAGA,CAACC,OAAO,GAAG,EAAE,EAAEC,SAAS,KAAK;EACjD,IAAIA,SAAS,KAAKf,SAAS,EAAE;IACzB,MAAMgB,iBAAiB,GAAGC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IAC5E,OAAO,CAAC,GAAGD,OAAO,EAAE,GAAGE,iBAAiB,CAAC;EAC7C;EACA,OAAOF,OAAO;AAClB,CAAC;AAED,MAAMK,eAAe,GAAIC,WAAW,IAAK;EACrC,IAAIC,MAAM;EACV,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,WAAW;EACf,IAAIC,KAAK;EACT,IAAIC,UAAU;EACd,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,mBAAmB,GAAG,EAAE;EAC5B,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,eAAe;EACnB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;EAC1B,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,mBAAmB;EACvB,IAAIC,kBAAkB;EACtB,IAAIC,eAAe;EACnB,IAAIC,YAAY,GAAG,IAAI;EACvB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,4BAA4B,GAAG,IAAI;EACvC,IAAIC,GAAG;EACP,IAAIC,MAAM,GAAG,KAAK;EAClB,MAAMC,EAAE,GAAG3B,WAAW;EACtB,MAAM4B,iBAAiB,GAAG,EAAE;EAC5B,MAAMC,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,uBAAuB,GAAG,EAAE;EAClC,MAAMC,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,uBAAuB,GAAG,EAAE;EAClC,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,uBAAuB,GAAG,OAAOC,eAAe,KAAK,UAAU,IAChEhE,yCAAG,KAAKI,SAAS,IAAI,OAAOJ,yCAAG,CAACgE,eAAe,KAAK,UAAW;EACpE;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,qBAAqB,GAAG,OAAOC,OAAO,KAAK,UAAU,IACvD,OAAOA,OAAO,CAACC,SAAS,CAACC,OAAO,KAAK,UAAU,IAC/CL,uBAAuB;EAC3B,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,OAAOP,aAAa;EACxB,CAAC;EACD,MAAMQ,OAAO,GAAIC,gBAAgB,IAAK;IAClCf,eAAe,CAACgB,OAAO,CAAEC,cAAc,IAAK;MACxCA,cAAc,CAACH,OAAO,CAACC,gBAAgB,CAAC;IAC5C,CAAC,CAAC;IACFG,OAAO,CAACH,gBAAgB,CAAC;IACzBhB,QAAQ,CAACoB,MAAM,GAAG,CAAC;IACnBnB,eAAe,CAACmB,MAAM,GAAG,CAAC;IAC1B5C,UAAU,CAAC4C,MAAM,GAAG,CAAC;IACrBC,aAAa,CAAC,CAAC;IACf1C,WAAW,GAAG,KAAK;IACnBc,4BAA4B,GAAG,IAAI;IACnC,OAAOC,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMyB,OAAO,GAAIH,gBAAgB,IAAK;IAClCM,eAAe,CAAC,CAAC;IACjB,IAAIN,gBAAgB,EAAE;MAClBO,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBtC,uBAAuB,GAAG,KAAK;IAC/BC,uBAAuB,GAAG,KAAK;IAC/BM,4BAA4B,GAAG,IAAI;IACnCL,mBAAmB,GAAGvC,SAAS;IAC/BwC,kBAAkB,GAAGxC,SAAS;IAC9ByC,eAAe,GAAGzC,SAAS;IAC3BoC,oBAAoB,GAAG,CAAC;IACxBO,QAAQ,GAAG,KAAK;IAChBD,YAAY,GAAG,IAAI;IACnBI,MAAM,GAAG,KAAK;EAClB,CAAC;EACD,MAAM8B,SAAS,GAAGA,CAAA,KAAM;IACpB,OAAOxC,oBAAoB,KAAK,CAAC,IAAI,CAACU,MAAM;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM+B,aAAa,GAAGA,CAACC,gBAAgB,EAAEC,eAAe,KAAK;IACzD,MAAMC,KAAK,GAAGD,eAAe,CAACE,SAAS,CAAEC,cAAc,IAAKA,cAAc,CAACC,CAAC,KAAKL,gBAAgB,CAAC;IAClG,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;MACZD,eAAe,CAACK,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IACpC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMK,MAAM,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;IAC/BrC,sBAAsB,CAACsC,IAAI,CAAC;MAAEL,CAAC,EAAEG,QAAQ;MAAEG,CAAC,EAAEF;IAAK,CAAC,CAAC;IACrD,OAAO1C,GAAG;EACd,CAAC;EACD,MAAM6C,QAAQ,GAAGA,CAACJ,QAAQ,EAAEC,IAAI,KAAK;IACjC,MAAMI,SAAS,GAAG,CAACJ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACK,eAAe,IAAI3C,wBAAwB,GAAGD,iBAAiB;IACnI2C,SAAS,CAACH,IAAI,CAAC;MAAEL,CAAC,EAAEG,QAAQ;MAAEG,CAAC,EAAEF;IAAK,CAAC,CAAC;IACxC,OAAO1C,GAAG;EACd,CAAC;EACD,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IACxBxB,iBAAiB,CAACuB,MAAM,GAAG,CAAC;IAC5BtB,wBAAwB,CAACsB,MAAM,GAAG,CAAC;IACnC,OAAO1B,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,qBAAqB,EAAE;MACvBH,aAAa,CAACU,OAAO,CAAEyB,SAAS,IAAK;QACjCA,SAAS,CAACC,MAAM,CAAC,CAAC;MACtB,CAAC,CAAC;MACFpC,aAAa,CAACa,MAAM,GAAG,CAAC;IAC5B;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC7BrB,WAAW,CAACe,OAAO,CAAE2B,UAAU,IAAK;MAChC;AACZ;AACA;AACA;AACA;MACY,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,UAAU,EAAE;QAC/ED,UAAU,CAACC,UAAU,CAACC,WAAW,CAACF,UAAU,CAAC;MACjD;IACJ,CAAC,CAAC;IACF1C,WAAW,CAACkB,MAAM,GAAG,CAAC;EAC1B,CAAC;EACD,MAAM2B,aAAa,GAAIC,MAAM,IAAK;IAC9B7C,uBAAuB,CAACkC,IAAI,CAACW,MAAM,CAAC;IACpC,OAAOtD,GAAG;EACd,CAAC;EACD,MAAMuD,cAAc,GAAIC,OAAO,IAAK;IAChC9C,wBAAwB,CAACiC,IAAI,CAACa,OAAO,CAAC;IACtC,OAAOxD,GAAG;EACd,CAAC;EACD,MAAMyD,YAAY,GAAIH,MAAM,IAAK;IAC7B3C,sBAAsB,CAACgC,IAAI,CAACW,MAAM,CAAC;IACnC,OAAOtD,GAAG;EACd,CAAC;EACD,MAAM0D,aAAa,GAAIF,OAAO,IAAK;IAC/B5C,uBAAuB,CAAC+B,IAAI,CAACa,OAAO,CAAC;IACrC,OAAOxD,GAAG;EACd,CAAC;EACD,MAAM2D,cAAc,GAAIzF,SAAS,IAAK;IAClCa,gBAAgB,GAAGf,eAAe,CAACe,gBAAgB,EAAEb,SAAS,CAAC;IAC/D,OAAO8B,GAAG;EACd,CAAC;EACD,MAAM4D,iBAAiB,GAAI1F,SAAS,IAAK;IACrCc,mBAAmB,GAAGhB,eAAe,CAACgB,mBAAmB,EAAEd,SAAS,CAAC;IACrE,OAAO8B,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAM6D,YAAY,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IAClC3E,iBAAiB,GAAG2E,MAAM;IAC1B,OAAO9D,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAM+D,iBAAiB,GAAGA,CAACC,aAAa,GAAG,EAAE,KAAK;IAC9C,KAAK,MAAMC,QAAQ,IAAID,aAAa,EAAE;MAClC7E,iBAAiB,CAAC8E,QAAQ,CAAC,GAAG,EAAE;IACpC;IACA,OAAOjE,GAAG;EACd,CAAC;EACD,MAAMkE,aAAa,GAAIhG,SAAS,IAAK;IACjCkB,eAAe,GAAGpB,eAAe,CAACoB,eAAe,EAAElB,SAAS,CAAC;IAC7D,OAAO8B,GAAG;EACd,CAAC;EACD,MAAMmE,gBAAgB,GAAIjG,SAAS,IAAK;IACpCmB,kBAAkB,GAAGrB,eAAe,CAACqB,kBAAkB,EAAEnB,SAAS,CAAC;IACnE,OAAO8B,GAAG;EACd,CAAC;EACD,MAAMoE,WAAW,GAAGA,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK;IACjCxE,gBAAgB,GAAGwE,MAAM;IACzB,OAAO9D,GAAG;EACd,CAAC;EACD,MAAMqE,gBAAgB,GAAGA,CAACL,aAAa,GAAG,EAAE,KAAK;IAC7C,KAAK,MAAMC,QAAQ,IAAID,aAAa,EAAE;MAClC1E,gBAAgB,CAAC2E,QAAQ,CAAC,GAAG,EAAE;IACnC;IACA,OAAOjE,GAAG;EACd,CAAC;EACD,MAAMsE,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAI1F,KAAK,KAAKzB,SAAS,EAAE;MACrB,OAAOyB,KAAK;IAChB;IACA,IAAIM,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACoF,OAAO,CAAC,CAAC;IACpC;IACA,OAAO,MAAM;EACjB,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAI7E,mBAAmB,KAAKvC,SAAS,EAAE;MACnC,OAAOuC,mBAAmB;IAC9B;IACA,IAAIb,UAAU,KAAK1B,SAAS,EAAE;MAC1B,OAAO0B,UAAU;IACrB;IACA,IAAIK,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACqF,YAAY,CAAC,CAAC;IACzC;IACA,OAAO,QAAQ;EACnB,CAAC;EACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACpB,IAAIhF,uBAAuB,EAAE;MACzB,OAAO,QAAQ;IACnB;IACA,IAAId,OAAO,KAAKvB,SAAS,EAAE;MACvB,OAAOuB,OAAO;IAClB;IACA,IAAIQ,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACsF,SAAS,CAAC,CAAC;IACtC;IACA,OAAO,QAAQ;EACnB,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAIhF,uBAAuB,EAAE;MACzB,OAAO,CAAC;IACZ;IACA,IAAIE,kBAAkB,KAAKxC,SAAS,EAAE;MAClC,OAAOwC,kBAAkB;IAC7B;IACA,IAAIlB,SAAS,KAAKtB,SAAS,EAAE;MACzB,OAAOsB,SAAS;IACpB;IACA,IAAIS,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACuF,WAAW,CAAC,CAAC;IACxC;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAI/F,WAAW,KAAKxB,SAAS,EAAE;MAC3B,OAAOwB,WAAW;IACtB;IACA,IAAIO,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACwF,aAAa,CAAC,CAAC;IAC1C;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnB,IAAI/E,eAAe,KAAKzC,SAAS,EAAE;MAC/B,OAAOyC,eAAe;IAC1B;IACA,IAAIpB,MAAM,KAAKrB,SAAS,EAAE;MACtB,OAAOqB,MAAM;IACjB;IACA,IAAIU,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACyF,QAAQ,CAAC,CAAC;IACrC;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,OAAO9F,UAAU;EACrB,CAAC;EACD,MAAM+F,SAAS,GAAIC,kBAAkB,IAAK;IACtCjG,UAAU,GAAGiG,kBAAkB;IAC/BC,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAMgF,IAAI,GAAIC,aAAa,IAAK;IAC5BrG,KAAK,GAAGqG,aAAa;IACrBF,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAMkF,KAAK,GAAIC,cAAc,IAAK;IAC9B3G,MAAM,GAAG2G,cAAc;IACvBJ,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAMoF,MAAM,GAAIC,eAAe,IAAK;IAChC3G,OAAO,GAAG2G,eAAe;IACzBN,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAMsF,QAAQ,GAAIC,iBAAiB,IAAK;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACvE,qBAAqB,IAAIuE,iBAAiB,KAAK,CAAC,EAAE;MACnDA,iBAAiB,GAAG,CAAC;IACzB;IACA9G,SAAS,GAAG8G,iBAAiB;IAC7BR,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAMwF,UAAU,GAAIC,mBAAmB,IAAK;IACxC9G,WAAW,GAAG8G,mBAAmB;IACjCV,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO/E,GAAG;EACd,CAAC;EACD,MAAM0F,MAAM,GAAI1C,SAAS,IAAK;IAC1B9D,eAAe,GAAG8D,SAAS;IAC3B,OAAOhD,GAAG;EACd,CAAC;EACD,MAAM2F,UAAU,GAAIzI,EAAE,IAAK;IACvB,IAAIA,EAAE,IAAI,IAAI,EAAE;MACZ,IAAIA,EAAE,CAAC0I,QAAQ,KAAK,CAAC,EAAE;QACnBtF,QAAQ,CAACqC,IAAI,CAACzF,EAAE,CAAC;MACrB,CAAC,MACI,IAAIA,EAAE,CAACwE,MAAM,IAAI,CAAC,EAAE;QACrB,KAAK,IAAImE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3I,EAAE,CAACwE,MAAM,EAAEmE,CAAC,EAAE,EAAE;UAChCvF,QAAQ,CAACqC,IAAI,CAACzF,EAAE,CAAC2I,CAAC,CAAC,CAAC;QACxB;MACJ,CAAC,MACI;QACDC,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACJ;IACA,OAAO/F,GAAG;EACd,CAAC;EACD,MAAMgG,YAAY,GAAIC,cAAc,IAAK;IACrC,IAAIA,cAAc,IAAI,IAAI,EAAE;MACxB,IAAI7H,KAAK,CAACC,OAAO,CAAC4H,cAAc,CAAC,EAAE;QAC/B,KAAK,MAAMjD,SAAS,IAAIiD,cAAc,EAAE;UACpCjD,SAAS,CAAC0C,MAAM,CAAC1F,GAAG,CAAC;UACrBO,eAAe,CAACoC,IAAI,CAACK,SAAS,CAAC;QACnC;MACJ,CAAC,MACI;QACDiD,cAAc,CAACP,MAAM,CAAC1F,GAAG,CAAC;QAC1BO,eAAe,CAACoC,IAAI,CAACsD,cAAc,CAAC;MACxC;IACJ;IACA,OAAOjG,GAAG;EACd,CAAC;EACD,MAAMkG,SAAS,GAAIC,cAAc,IAAK;IAClC,MAAMC,SAAS,GAAGtH,UAAU,KAAKqH,cAAc;IAC/CrH,UAAU,GAAGqH,cAAc;IAC3B,IAAIC,SAAS,EAAE;MACXC,eAAe,CAACvH,UAAU,CAAC;IAC/B;IACA,OAAOkB,GAAG;EACd,CAAC;EACD,MAAMqG,eAAe,GAAIF,cAAc,IAAK;IACxC,IAAInF,qBAAqB,EAAE;MACvBI,gBAAgB,CAAC,CAAC,CAACG,OAAO,CAAEyB,SAAS,IAAK;QACtC;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMsD,cAAc,GAAGtD,SAAS,CAACuD,MAAM;QACvC;AAChB;AACA;AACA;AACA;QACgB;QACA,IAAID,cAAc,CAACE,YAAY,EAAE;UAC7BF,cAAc,CAACE,YAAY,CAACL,cAAc,CAAC;QAC/C,CAAC,MACI;UACD,MAAMM,SAAS,GAAG,IAAIC,cAAc,CAACJ,cAAc,CAACK,MAAM,EAAER,cAAc,EAAEG,cAAc,CAACM,SAAS,CAAC,CAAC,CAAC;UACvG5D,SAAS,CAACuD,MAAM,GAAGE,SAAS;QAChC;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;EACI,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC1B;IACApG,uBAAuB,CAACc,OAAO,CAAEkB,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACzD;IACA/B,wBAAwB,CAACa,OAAO,CAAEkB,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAC1D;IACA,MAAMqE,UAAU,GAAG/H,gBAAgB;IACnC,MAAMgI,aAAa,GAAG/H,mBAAmB;IACzC,MAAM8E,MAAM,GAAG3E,iBAAiB;IAChCmB,QAAQ,CAACiB,OAAO,CAAErE,EAAE,IAAK;MACrB,MAAM8J,gBAAgB,GAAG9J,EAAE,CAAC+J,SAAS;MACrCH,UAAU,CAACvF,OAAO,CAAEe,CAAC,IAAK0E,gBAAgB,CAACE,GAAG,CAAC5E,CAAC,CAAC,CAAC;MAClDyE,aAAa,CAACxF,OAAO,CAAEe,CAAC,IAAK0E,gBAAgB,CAACG,MAAM,CAAC7E,CAAC,CAAC,CAAC;MACxD,KAAK,MAAM2B,QAAQ,IAAIH,MAAM,EAAE;QAC3B;QACA,IAAIA,MAAM,CAACsD,cAAc,CAACnD,QAAQ,CAAC,EAAE;UACjCxG,gBAAgB,CAACP,EAAE,EAAE+G,QAAQ,EAAEH,MAAM,CAACG,QAAQ,CAAC,CAAC;QACpD;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACI,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IACzB;IACA1G,sBAAsB,CAACY,OAAO,CAAEkB,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACxD;IACA7B,uBAAuB,CAACW,OAAO,CAAEkB,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACzD;IACA,MAAM6E,WAAW,GAAGzH,YAAY,GAAG,CAAC,GAAG,CAAC;IACxC,MAAMiH,UAAU,GAAG1H,eAAe;IAClC,MAAM2H,aAAa,GAAG1H,kBAAkB;IACxC,MAAMyE,MAAM,GAAGxE,gBAAgB;IAC/BgB,QAAQ,CAACiB,OAAO,CAAErE,EAAE,IAAK;MACrB,MAAM8J,gBAAgB,GAAG9J,EAAE,CAAC+J,SAAS;MACrCH,UAAU,CAACvF,OAAO,CAAEe,CAAC,IAAK0E,gBAAgB,CAACE,GAAG,CAAC5E,CAAC,CAAC,CAAC;MAClDyE,aAAa,CAACxF,OAAO,CAAEe,CAAC,IAAK0E,gBAAgB,CAACG,MAAM,CAAC7E,CAAC,CAAC,CAAC;MACxD,KAAK,MAAM2B,QAAQ,IAAIH,MAAM,EAAE;QAC3B;QACA,IAAIA,MAAM,CAACsD,cAAc,CAACnD,QAAQ,CAAC,EAAE;UACjCxG,gBAAgB,CAACP,EAAE,EAAE+G,QAAQ,EAAEH,MAAM,CAACG,QAAQ,CAAC,CAAC;QACpD;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;AACA;IACQtE,kBAAkB,GAAGxC,SAAS;IAC9BuC,mBAAmB,GAAGvC,SAAS;IAC/ByC,eAAe,GAAGzC,SAAS;IAC3BgD,iBAAiB,CAACoB,OAAO,CAAEgG,gBAAgB,IAAK;MAC5C,OAAOA,gBAAgB,CAACjF,CAAC,CAACgF,WAAW,EAAEtH,GAAG,CAAC;IAC/C,CAAC,CAAC;IACFI,wBAAwB,CAACmB,OAAO,CAAEgG,gBAAgB,IAAK;MACnD,OAAOA,gBAAgB,CAACjF,CAAC,CAACgF,WAAW,EAAEtH,GAAG,CAAC;IAC/C,CAAC,CAAC;IACFI,wBAAwB,CAACsB,MAAM,GAAG,CAAC;IACnC3B,4BAA4B,GAAG,IAAI;IACnC,IAAIF,YAAY,EAAE;MACdC,QAAQ,GAAG,IAAI;IACnB;IACAD,YAAY,GAAG,IAAI;EACvB,CAAC;EACD,MAAM2H,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIjI,oBAAoB,KAAK,CAAC,EAAE;MAC5B;IACJ;IACAA,oBAAoB,EAAE;IACtB,IAAIA,oBAAoB,KAAK,CAAC,EAAE;MAC5B8H,cAAc,CAAC,CAAC;MAChB,IAAInI,eAAe,EAAE;QACjBA,eAAe,CAACsI,eAAe,CAAC,CAAC;MACrC;IACJ;EACJ,CAAC;EACD,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACjCnH,QAAQ,CAACiB,OAAO,CAAE7D,OAAO,IAAK;MAC1B,MAAMsF,SAAS,GAAGtF,OAAO,CAACyD,OAAO,CAACrC,UAAU,EAAE;QAC1CoB,EAAE;QACFgF,KAAK,EAAEP,QAAQ,CAAC,CAAC;QACjBW,QAAQ,EAAEb,WAAW,CAAC,CAAC;QACvBW,MAAM,EAAEZ,SAAS,CAAC,CAAC;QACnBgB,UAAU,EAAEd,aAAa,CAAC,CAAC;QAC3BM,IAAI,EAAEV,OAAO,CAAC,CAAC;QACfO,SAAS,EAAEN,YAAY,CAAC;MAC5B,CAAC,CAAC;MACFvB,SAAS,CAAC0E,KAAK,CAAC,CAAC;MACjB7G,aAAa,CAAC8B,IAAI,CAACK,SAAS,CAAC;IACjC,CAAC,CAAC;IACF,IAAInC,aAAa,CAACa,MAAM,GAAG,CAAC,EAAE;MAC1Bb,aAAa,CAAC,CAAC,CAAC,CAAC8G,QAAQ,GAAG,MAAM;QAC9BH,eAAe,CAAC,CAAC;MACrB,CAAC;IACL;EACJ,CAAC;EACD,MAAMI,mBAAmB,GAAGA,CAAA,KAAM;IAC9Bf,eAAe,CAAC,CAAC;IACjB,IAAI/H,UAAU,CAAC4C,MAAM,GAAG,CAAC,EAAE;MACvB,IAAIV,qBAAqB,EAAE;QACvByG,sBAAsB,CAAC,CAAC;MAC5B;IACJ;IACAxI,WAAW,GAAG,IAAI;EACtB,CAAC;EACD,MAAM4I,gBAAgB,GAAIC,IAAI,IAAK;IAC/BA,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC1C,IAAI9G,qBAAqB,EAAE;MACvBH,aAAa,CAACU,OAAO,CAAEyB,SAAS,IAAK;QACjC;QACAA,SAAS,CAACkF,WAAW,GAAGlF,SAAS,CAACuD,MAAM,CAAC4B,iBAAiB,CAAC,CAAC,CAACjD,KAAK,GAAGT,WAAW,CAAC,CAAC,GAAGqD,IAAI;QACzF9E,SAAS,CAAC0E,KAAK,CAAC,CAAC;MACrB,CAAC,CAAC;IACN;EACJ,CAAC;EACD,MAAMU,kBAAkB,GAAIN,IAAI,IAAK;IACjCjH,aAAa,CAACU,OAAO,CAAEyB,SAAS,IAAK;MACjCA,SAAS,CAACuD,MAAM,CAAC8B,YAAY,CAAC;QAC1BnD,KAAK,EAAEP,QAAQ,CAAC,CAAC;QACjBW,QAAQ,EAAEb,WAAW,CAAC,CAAC;QACvBW,MAAM,EAAEZ,SAAS,CAAC,CAAC;QACnBgB,UAAU,EAAEd,aAAa,CAAC,CAAC;QAC3BM,IAAI,EAAEV,OAAO,CAAC,CAAC;QACfO,SAAS,EAAEN,YAAY,CAAC;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAIuD,IAAI,KAAK3K,SAAS,EAAE;MACpB0K,gBAAgB,CAACC,IAAI,CAAC;IAC1B;EACJ,CAAC;EACD,MAAM/C,MAAM,GAAGA,CAACuD,IAAI,GAAG,KAAK,EAAEC,mBAAmB,GAAG,IAAI,EAAET,IAAI,KAAK;IAC/D,IAAIQ,IAAI,EAAE;MACN/H,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;QACnCA,SAAS,CAAC+B,MAAM,CAACuD,IAAI,EAAEC,mBAAmB,EAAET,IAAI,CAAC;MACrD,CAAC,CAAC;IACN;IACA,IAAI9G,qBAAqB,EAAE;MACvBoH,kBAAkB,CAACN,IAAI,CAAC;IAC5B;IACA,OAAO9H,GAAG;EACd,CAAC;EACD,MAAMwI,aAAa,GAAGA,CAACC,iBAAiB,GAAG,KAAK,EAAEX,IAAI,KAAK;IACvDvH,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;MACnCA,SAAS,CAACwF,aAAa,CAACC,iBAAiB,EAAEX,IAAI,CAAC;IACpD,CAAC,CAAC;IACFY,cAAc,CAAC,CAAC;IAChBlJ,uBAAuB,GAAGiJ,iBAAiB;IAC3C,IAAI,CAACxJ,WAAW,EAAE;MACd2I,mBAAmB,CAAC,CAAC;IACzB;IACA7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE+C,IAAI,CAAC;IACzB,OAAO9H,GAAG;EACd,CAAC;EACD,MAAM2I,YAAY,GAAIb,IAAI,IAAK;IAC3BvH,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;MACnCA,SAAS,CAAC2F,YAAY,CAACb,IAAI,CAAC;IAChC,CAAC,CAAC;IACFD,gBAAgB,CAACC,IAAI,CAAC;IACtB,OAAO9H,GAAG;EACd,CAAC;EACD,MAAM4I,WAAW,GAAGA,CAACC,MAAM,EAAEf,IAAI,EAAEgB,GAAG,KAAK;IACvCtJ,uBAAuB,GAAG,KAAK;IAC/Be,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;MACnCA,SAAS,CAAC4F,WAAW,CAACC,MAAM,EAAEf,IAAI,EAAEgB,GAAG,CAAC;IAC5C,CAAC,CAAC;IACF,IAAIA,GAAG,KAAK3L,SAAS,EAAE;MACnBwC,kBAAkB,GAAGmJ,GAAG;IAC5B;IACAhJ,QAAQ,GAAG,KAAK;IAChBD,YAAY,GAAG,IAAI;IACnB,IAAIgJ,MAAM,KAAK,CAAC,EAAE;MACdnJ,mBAAmB,GAAG6E,YAAY,CAAC,CAAC,KAAK,SAAS,GAAG,QAAQ,GAAG,SAAS;MACzE,IAAI7E,mBAAmB,KAAK,SAAS,EAAE;QACnCG,YAAY,GAAG,KAAK;MACxB;MACA,IAAImB,qBAAqB,EAAE;QACvB+D,MAAM,CAAC,CAAC;QACR8C,gBAAgB,CAAC,CAAC,GAAGC,IAAI,CAAC;MAC9B,CAAC,MACI;QACDlI,eAAe,GAAG,CAAC,CAAC,GAAGkI,IAAI,IAAIrD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QACjDM,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;MACxB;IACJ,CAAC,MACI,IAAI8D,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI7H,qBAAqB,EAAE;QACvB+D,MAAM,CAAC,CAAC;QACR8C,gBAAgB,CAACC,IAAI,CAAC;MAC1B,CAAC,MACI;QACDlI,eAAe,GAAGkI,IAAI,GAAGrD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3CM,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;MACxB;IACJ;IACA,IAAI8D,MAAM,KAAK1L,SAAS,IAAI,CAAC+B,eAAe,EAAE;MAC1C6J,IAAI,CAAC,CAAC;IACV;IACA,OAAO/I,GAAG;EACd,CAAC;EACD,MAAM0I,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIzJ,WAAW,EAAE;MACb,IAAI+B,qBAAqB,EAAE;QACvBH,aAAa,CAACU,OAAO,CAAEyB,SAAS,IAAK;UACjCA,SAAS,CAAC0E,KAAK,CAAC,CAAC;QACrB,CAAC,CAAC;MACN,CAAC,MACI;QACDpH,QAAQ,CAACiB,OAAO,CAAE7D,OAAO,IAAK;UAC1BD,gBAAgB,CAACC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,CAAC;QAC/D,CAAC,CAAC;MACN;MACAuC,MAAM,GAAG,IAAI;IACjB;EACJ,CAAC;EACD,MAAMyH,KAAK,GAAGA,CAAA,KAAM;IAChBnH,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;MACnCA,SAAS,CAAC0E,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;IACFgB,cAAc,CAAC,CAAC;IAChB,OAAO1I,GAAG;EACd,CAAC;EACD,MAAMgJ,iBAAiB,GAAGA,CAAA,KAAM;IAC5BxB,eAAe,CAAC,CAAC;EACrB,CAAC;EACD,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC5BpI,aAAa,CAACU,OAAO,CAAEyB,SAAS,IAAK;MACjCA,SAAS,CAAC+F,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAIjK,UAAU,CAAC4C,MAAM,KAAK,CAAC,IAAIpB,QAAQ,CAACoB,MAAM,KAAK,CAAC,EAAE;MAClD8F,eAAe,CAAC,CAAC;IACrB;EACJ,CAAC;EACD,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIlI,qBAAqB,EAAE;MACvB6G,gBAAgB,CAAC,CAAC,CAAC;MACnBO,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACD,MAAMW,IAAI,GAAIrG,IAAI,IAAK;IACnB,OAAO,IAAIyG,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI1G,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC2G,IAAI,EAAE;QACvD5J,uBAAuB,GAAG,IAAI;QAC9BoD,QAAQ,CAAC,MAAOpD,uBAAuB,GAAG,KAAM,EAAE;UAAEsD,eAAe,EAAE;QAAK,CAAC,CAAC;MAChF;MACA,IAAI,CAAC9D,WAAW,EAAE;QACd2I,mBAAmB,CAAC,CAAC;MACzB;MACA,IAAI9H,QAAQ,EAAE;QACVoJ,cAAc,CAAC,CAAC;QAChBpJ,QAAQ,GAAG,KAAK;MACpB;MACA,IAAIC,4BAA4B,EAAE;QAC9BR,oBAAoB,GAAGgB,eAAe,CAACmB,MAAM,GAAG,CAAC;QACjD3B,4BAA4B,GAAG,KAAK;MACxC;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMuJ,cAAc,GAAGA,CAAA,KAAM;QACzBtH,aAAa,CAACuF,gBAAgB,EAAEnH,wBAAwB,CAAC;QACzDgJ,OAAO,CAAC,CAAC;MACb,CAAC;MACD,MAAM7B,gBAAgB,GAAGA,CAAA,KAAM;QAC3BvF,aAAa,CAACsH,cAAc,EAAEjJ,sBAAsB,CAAC;QACrD+I,OAAO,CAAC,CAAC;MACb,CAAC;MACD;AACZ;AACA;AACA;MACYvG,QAAQ,CAAC0E,gBAAgB,EAAE;QAAExE,eAAe,EAAE;MAAK,CAAC,CAAC;MACrDP,MAAM,CAAC8G,cAAc,EAAE;QAAEvG,eAAe,EAAE;MAAK,CAAC,CAAC;MACjDxC,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;QACnCA,SAAS,CAAC+F,IAAI,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI/H,qBAAqB,EAAE;QACvBiI,iBAAiB,CAAC,CAAC;MACvB,CAAC,MACI;QACDD,iBAAiB,CAAC,CAAC;MACvB;MACA/I,MAAM,GAAG,KAAK;IAClB,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMsJ,IAAI,GAAGA,CAAA,KAAM;IACfhJ,eAAe,CAACgB,OAAO,CAAEyB,SAAS,IAAK;MACnCA,SAAS,CAACuG,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAItK,WAAW,EAAE;MACb2C,eAAe,CAAC,CAAC;MACjB3C,WAAW,GAAG,KAAK;IACvB;IACA6C,UAAU,CAAC,CAAC;IACZzB,sBAAsB,CAACkB,OAAO,CAAE+H,cAAc,IAAKA,cAAc,CAAChH,CAAC,CAAC,CAAC,EAAEtC,GAAG,CAAC,CAAC;IAC5EK,sBAAsB,CAACqB,MAAM,GAAG,CAAC;EACrC,CAAC;EACD,MAAM8H,IAAI,GAAGA,CAACvF,QAAQ,EAAErG,KAAK,KAAK;IAC9B,MAAM6L,UAAU,GAAG3K,UAAU,CAAC,CAAC,CAAC;IAChC,IAAI2K,UAAU,KAAKtM,SAAS,KAAKsM,UAAU,CAACC,MAAM,KAAKvM,SAAS,IAAIsM,UAAU,CAACC,MAAM,KAAK,CAAC,CAAC,EAAE;MAC1FD,UAAU,CAACxF,QAAQ,CAAC,GAAGrG,KAAK;IAChC,CAAC,MACI;MACDkB,UAAU,GAAG,CAAC;QAAE4K,MAAM,EAAE,CAAC;QAAE,CAACzF,QAAQ,GAAGrG;MAAM,CAAC,EAAE,GAAGkB,UAAU,CAAC;IAClE;IACA,OAAOkB,GAAG;EACd,CAAC;EACD,MAAM2J,EAAE,GAAGA,CAAC1F,QAAQ,EAAErG,KAAK,KAAK;IAC5B,MAAMgM,SAAS,GAAG9K,UAAU,CAACA,UAAU,CAAC4C,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIkI,SAAS,KAAKzM,SAAS,KAAKyM,SAAS,CAACF,MAAM,KAAKvM,SAAS,IAAIyM,SAAS,CAACF,MAAM,KAAK,CAAC,CAAC,EAAE;MACvFE,SAAS,CAAC3F,QAAQ,CAAC,GAAGrG,KAAK;IAC/B,CAAC,MACI;MACDkB,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE;QAAE4K,MAAM,EAAE,CAAC;QAAE,CAACzF,QAAQ,GAAGrG;MAAM,CAAC,CAAC;IAClE;IACA,OAAOoC,GAAG;EACd,CAAC;EACD,MAAM6J,MAAM,GAAGA,CAAC5F,QAAQ,EAAE6F,SAAS,EAAEC,OAAO,KAAK;IAC7C,OAAOP,IAAI,CAACvF,QAAQ,EAAE6F,SAAS,CAAC,CAACH,EAAE,CAAC1F,QAAQ,EAAE8F,OAAO,CAAC;EAC1D,CAAC;EACD,OAAQ/J,GAAG,GAAG;IACVd,eAAe;IACfoB,QAAQ;IACRC,eAAe;IACfL,EAAE;IACFsH,eAAe;IACfgC,IAAI;IACJG,EAAE;IACFE,MAAM;IACNnE,MAAM;IACNqD,IAAI;IACJrB,KAAK;IACL6B,IAAI;IACJlI,OAAO;IACP6E,SAAS;IACTF,YAAY;IACZL,UAAU;IACVZ,MAAM;IACNC,IAAI;IACJH,SAAS;IACTW,UAAU;IACVF,QAAQ;IACRF,MAAM;IACNF,KAAK;IACL9D,gBAAgB;IAChBwD,YAAY;IACZN,OAAO;IACPC,YAAY;IACZI,QAAQ;IACRD,aAAa;IACbF,SAAS;IACTC,WAAW;IACXhB,YAAY;IACZC,aAAa;IACbW,gBAAgB;IAChBD,WAAW;IACXD,gBAAgB;IAChBD,aAAa;IACbb,aAAa;IACbE,cAAc;IACdQ,iBAAiB;IACjBF,YAAY;IACZD,iBAAiB;IACjBD,cAAc;IACdd,QAAQ;IACRd,SAAS;IACTyG,aAAa;IACbG,YAAY;IACZC;EACJ,CAAC;AACL,CAAC;;;;;;;;;;;;;;;;AChzBD;AACA;AACA;AACA,MAAM7L,GAAG,GAAG,OAAOiN,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG7M,SAAS;AAC9D,MAAM8M,GAAG,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG/M,SAAS", "sources": ["./node_modules/@ionic/core/components/animation.js", "./node_modules/@ionic/core/components/index5.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index5.js';\n\nlet animationPrefix;\nconst getAnimationPrefix = (el) => {\n    if (animationPrefix === undefined) {\n        const supportsUnprefixed = el.style.animationName !== undefined;\n        const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n        animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n    }\n    return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n    const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n    element.style.setProperty(prefix + propertyName, value);\n};\nconst addClassToArray = (classes = [], className) => {\n    if (className !== undefined) {\n        const classNameToAppend = Array.isArray(className) ? className : [className];\n        return [...classes, ...classNameToAppend];\n    }\n    return classes;\n};\n\nconst createAnimation = (animationId) => {\n    let _delay;\n    let _duration;\n    let _easing;\n    let _iterations;\n    let _fill;\n    let _direction;\n    let _keyframes = [];\n    let beforeAddClasses = [];\n    let beforeRemoveClasses = [];\n    let initialized = false;\n    let parentAnimation;\n    let beforeStylesValue = {};\n    let afterAddClasses = [];\n    let afterRemoveClasses = [];\n    let afterStylesValue = {};\n    let numAnimationsRunning = 0;\n    let shouldForceLinearEasing = false;\n    let shouldForceSyncPlayback = false;\n    let forceDirectionValue;\n    let forceDurationValue;\n    let forceDelayValue;\n    let willComplete = true;\n    let finished = false;\n    let shouldCalculateNumAnimations = true;\n    let ani;\n    let paused = false;\n    const id = animationId;\n    const onFinishCallbacks = [];\n    const onFinishOneTimeCallbacks = [];\n    const onStopOneTimeCallbacks = [];\n    const elements = [];\n    const childAnimations = [];\n    const stylesheets = [];\n    const _beforeAddReadFunctions = [];\n    const _beforeAddWriteFunctions = [];\n    const _afterAddReadFunctions = [];\n    const _afterAddWriteFunctions = [];\n    const webAnimations = [];\n    const supportsAnimationEffect = typeof AnimationEffect === 'function' ||\n        (win !== undefined && typeof win.AnimationEffect === 'function');\n    /**\n     * This is a feature detection for Web Animations.\n     *\n     * Certain environments such as emulated browser environments for testing,\n     * do not support Web Animations. As a result, we need to check for support\n     * and provide a fallback to test certain functionality related to Web Animations.\n     */\n    const supportsWebAnimations = typeof Element === 'function' &&\n        typeof Element.prototype.animate === 'function' &&\n        supportsAnimationEffect;\n    const getWebAnimations = () => {\n        return webAnimations;\n    };\n    const destroy = (clearStyleSheets) => {\n        childAnimations.forEach((childAnimation) => {\n            childAnimation.destroy(clearStyleSheets);\n        });\n        cleanUp(clearStyleSheets);\n        elements.length = 0;\n        childAnimations.length = 0;\n        _keyframes.length = 0;\n        clearOnFinish();\n        initialized = false;\n        shouldCalculateNumAnimations = true;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations, removes\n     * any animation properties from the\n     * animation's elements, and removes the\n     * animation's stylesheets from the DOM.\n     */\n    const cleanUp = (clearStyleSheets) => {\n        cleanUpElements();\n        if (clearStyleSheets) {\n            cleanUpStyleSheets();\n        }\n    };\n    const resetFlags = () => {\n        shouldForceLinearEasing = false;\n        shouldForceSyncPlayback = false;\n        shouldCalculateNumAnimations = true;\n        forceDirectionValue = undefined;\n        forceDurationValue = undefined;\n        forceDelayValue = undefined;\n        numAnimationsRunning = 0;\n        finished = false;\n        willComplete = true;\n        paused = false;\n    };\n    const isRunning = () => {\n        return numAnimationsRunning !== 0 && !paused;\n    };\n    /**\n     * @internal\n     * Remove a callback from a chosen callback array\n     * @param callbackToRemove: A reference to the callback that should be removed\n     * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n     */\n    const clearCallback = (callbackToRemove, callbackObjects) => {\n        const index = callbackObjects.findIndex((callbackObject) => callbackObject.c === callbackToRemove);\n        if (index > -1) {\n            callbackObjects.splice(index, 1);\n        }\n    };\n    /**\n     * @internal\n     * Add a callback to be fired when an animation is stopped/cancelled.\n     * @param callback: A reference to the callback that should be fired\n     * @param opts: Any options associated with this particular callback\n     */\n    const onStop = (callback, opts) => {\n        onStopOneTimeCallbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const onFinish = (callback, opts) => {\n        const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n        callbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const clearOnFinish = () => {\n        onFinishCallbacks.length = 0;\n        onFinishOneTimeCallbacks.length = 0;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations and removes\n     * any animation properties from the\n     * the animation's elements.\n     */\n    const cleanUpElements = () => {\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                animation.cancel();\n            });\n            webAnimations.length = 0;\n        }\n    };\n    /**\n     * Removes the animation's stylesheets\n     * from the DOM.\n     */\n    const cleanUpStyleSheets = () => {\n        stylesheets.forEach((stylesheet) => {\n            /**\n             * When sharing stylesheets, it's possible\n             * for another animation to have already\n             * cleaned up a particular stylesheet\n             */\n            if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n                stylesheet.parentNode.removeChild(stylesheet);\n            }\n        });\n        stylesheets.length = 0;\n    };\n    const beforeAddRead = (readFn) => {\n        _beforeAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const beforeAddWrite = (writeFn) => {\n        _beforeAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const afterAddRead = (readFn) => {\n        _afterAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const afterAddWrite = (writeFn) => {\n        _afterAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const beforeAddClass = (className) => {\n        beforeAddClasses = addClassToArray(beforeAddClasses, className);\n        return ani;\n    };\n    const beforeRemoveClass = (className) => {\n        beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n        return ani;\n    };\n    /**\n     * Set CSS inline styles to the animation's\n     * elements before the animation begins.\n     */\n    const beforeStyles = (styles = {}) => {\n        beforeStylesValue = styles;\n        return ani;\n    };\n    /**\n     * Clear CSS inline styles from the animation's\n     * elements before the animation begins.\n     */\n    const beforeClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            beforeStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const afterAddClass = (className) => {\n        afterAddClasses = addClassToArray(afterAddClasses, className);\n        return ani;\n    };\n    const afterRemoveClass = (className) => {\n        afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n        return ani;\n    };\n    const afterStyles = (styles = {}) => {\n        afterStylesValue = styles;\n        return ani;\n    };\n    const afterClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            afterStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const getFill = () => {\n        if (_fill !== undefined) {\n            return _fill;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getFill();\n        }\n        return 'both';\n    };\n    const getDirection = () => {\n        if (forceDirectionValue !== undefined) {\n            return forceDirectionValue;\n        }\n        if (_direction !== undefined) {\n            return _direction;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDirection();\n        }\n        return 'normal';\n    };\n    const getEasing = () => {\n        if (shouldForceLinearEasing) {\n            return 'linear';\n        }\n        if (_easing !== undefined) {\n            return _easing;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getEasing();\n        }\n        return 'linear';\n    };\n    const getDuration = () => {\n        if (shouldForceSyncPlayback) {\n            return 0;\n        }\n        if (forceDurationValue !== undefined) {\n            return forceDurationValue;\n        }\n        if (_duration !== undefined) {\n            return _duration;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDuration();\n        }\n        return 0;\n    };\n    const getIterations = () => {\n        if (_iterations !== undefined) {\n            return _iterations;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getIterations();\n        }\n        return 1;\n    };\n    const getDelay = () => {\n        if (forceDelayValue !== undefined) {\n            return forceDelayValue;\n        }\n        if (_delay !== undefined) {\n            return _delay;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDelay();\n        }\n        return 0;\n    };\n    const getKeyframes = () => {\n        return _keyframes;\n    };\n    const direction = (animationDirection) => {\n        _direction = animationDirection;\n        update(true);\n        return ani;\n    };\n    const fill = (animationFill) => {\n        _fill = animationFill;\n        update(true);\n        return ani;\n    };\n    const delay = (animationDelay) => {\n        _delay = animationDelay;\n        update(true);\n        return ani;\n    };\n    const easing = (animationEasing) => {\n        _easing = animationEasing;\n        update(true);\n        return ani;\n    };\n    const duration = (animationDuration) => {\n        /**\n         * CSS Animation Durations of 0ms work fine on Chrome\n         * but do not run on Safari, so force it to 1ms to\n         * get it to run on both platforms.\n         */\n        if (!supportsWebAnimations && animationDuration === 0) {\n            animationDuration = 1;\n        }\n        _duration = animationDuration;\n        update(true);\n        return ani;\n    };\n    const iterations = (animationIterations) => {\n        _iterations = animationIterations;\n        update(true);\n        return ani;\n    };\n    const parent = (animation) => {\n        parentAnimation = animation;\n        return ani;\n    };\n    const addElement = (el) => {\n        if (el != null) {\n            if (el.nodeType === 1) {\n                elements.push(el);\n            }\n            else if (el.length >= 0) {\n                for (let i = 0; i < el.length; i++) {\n                    elements.push(el[i]);\n                }\n            }\n            else {\n                console.error('Invalid addElement value');\n            }\n        }\n        return ani;\n    };\n    const addAnimation = (animationToAdd) => {\n        if (animationToAdd != null) {\n            if (Array.isArray(animationToAdd)) {\n                for (const animation of animationToAdd) {\n                    animation.parent(ani);\n                    childAnimations.push(animation);\n                }\n            }\n            else {\n                animationToAdd.parent(ani);\n                childAnimations.push(animationToAdd);\n            }\n        }\n        return ani;\n    };\n    const keyframes = (keyframeValues) => {\n        const different = _keyframes !== keyframeValues;\n        _keyframes = keyframeValues;\n        if (different) {\n            updateKeyframes(_keyframes);\n        }\n        return ani;\n    };\n    const updateKeyframes = (keyframeValues) => {\n        if (supportsWebAnimations) {\n            getWebAnimations().forEach((animation) => {\n                /**\n                 * animation.effect's type is AnimationEffect.\n                 * However, in this case we have a more specific\n                 * type of AnimationEffect called KeyframeEffect which\n                 * inherits from AnimationEffect. As a result,\n                 * we cast animation.effect to KeyframeEffect.\n                 */\n                const keyframeEffect = animation.effect;\n                /**\n                 * setKeyframes is not supported in all browser\n                 * versions that Ionic supports, so we need to\n                 * check for support before using it.\n                 */\n                // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                if (keyframeEffect.setKeyframes) {\n                    keyframeEffect.setKeyframes(keyframeValues);\n                }\n                else {\n                    const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n                    animation.effect = newEffect;\n                }\n            });\n        }\n    };\n    /**\n     * Run all \"before\" animation hooks.\n     */\n    const beforeAnimation = () => {\n        // Runs all before read callbacks\n        _beforeAddReadFunctions.forEach((callback) => callback());\n        // Runs all before write callbacks\n        _beforeAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation runs\n        const addClasses = beforeAddClasses;\n        const removeClasses = beforeRemoveClasses;\n        const styles = beforeStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n    };\n    /**\n     * Run all \"after\" animation hooks.\n     */\n    const afterAnimation = () => {\n        // Runs all after read callbacks\n        _afterAddReadFunctions.forEach((callback) => callback());\n        // Runs all after write callbacks\n        _afterAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation ends\n        const currentStep = willComplete ? 1 : 0;\n        const addClasses = afterAddClasses;\n        const removeClasses = afterRemoveClasses;\n        const styles = afterStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n        /**\n         * Clean up any value coercion before\n         * the user callbacks fire otherwise\n         * they may get stale values. For example,\n         * if someone calls progressStart(0) the\n         * animation may still be reversed.\n         */\n        forceDurationValue = undefined;\n        forceDirectionValue = undefined;\n        forceDelayValue = undefined;\n        onFinishCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.length = 0;\n        shouldCalculateNumAnimations = true;\n        if (willComplete) {\n            finished = true;\n        }\n        willComplete = true;\n    };\n    const animationFinish = () => {\n        if (numAnimationsRunning === 0) {\n            return;\n        }\n        numAnimationsRunning--;\n        if (numAnimationsRunning === 0) {\n            afterAnimation();\n            if (parentAnimation) {\n                parentAnimation.animationFinish();\n            }\n        }\n    };\n    const initializeWebAnimation = () => {\n        elements.forEach((element) => {\n            const animation = element.animate(_keyframes, {\n                id,\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n            animation.pause();\n            webAnimations.push(animation);\n        });\n        if (webAnimations.length > 0) {\n            webAnimations[0].onfinish = () => {\n                animationFinish();\n            };\n        }\n    };\n    const initializeAnimation = () => {\n        beforeAnimation();\n        if (_keyframes.length > 0) {\n            if (supportsWebAnimations) {\n                initializeWebAnimation();\n            }\n        }\n        initialized = true;\n    };\n    const setAnimationStep = (step) => {\n        step = Math.min(Math.max(step, 0), 0.9999);\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                // When creating the animation the delay is guaranteed to be set to a number.\n                animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n                animation.pause();\n            });\n        }\n    };\n    const updateWebAnimation = (step) => {\n        webAnimations.forEach((animation) => {\n            animation.effect.updateTiming({\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n        });\n        if (step !== undefined) {\n            setAnimationStep(step);\n        }\n    };\n    const update = (deep = false, toggleAnimationName = true, step) => {\n        if (deep) {\n            childAnimations.forEach((animation) => {\n                animation.update(deep, toggleAnimationName, step);\n            });\n        }\n        if (supportsWebAnimations) {\n            updateWebAnimation(step);\n        }\n        return ani;\n    };\n    const progressStart = (forceLinearEasing = false, step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStart(forceLinearEasing, step);\n        });\n        pauseAnimation();\n        shouldForceLinearEasing = forceLinearEasing;\n        if (!initialized) {\n            initializeAnimation();\n        }\n        update(false, true, step);\n        return ani;\n    };\n    const progressStep = (step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStep(step);\n        });\n        setAnimationStep(step);\n        return ani;\n    };\n    const progressEnd = (playTo, step, dur) => {\n        shouldForceLinearEasing = false;\n        childAnimations.forEach((animation) => {\n            animation.progressEnd(playTo, step, dur);\n        });\n        if (dur !== undefined) {\n            forceDurationValue = dur;\n        }\n        finished = false;\n        willComplete = true;\n        if (playTo === 0) {\n            forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n            if (forceDirectionValue === 'reverse') {\n                willComplete = false;\n            }\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(1 - step);\n            }\n            else {\n                forceDelayValue = (1 - step) * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        else if (playTo === 1) {\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(step);\n            }\n            else {\n                forceDelayValue = step * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        if (playTo !== undefined && !parentAnimation) {\n            play();\n        }\n        return ani;\n    };\n    const pauseAnimation = () => {\n        if (initialized) {\n            if (supportsWebAnimations) {\n                webAnimations.forEach((animation) => {\n                    animation.pause();\n                });\n            }\n            else {\n                elements.forEach((element) => {\n                    setStyleProperty(element, 'animation-play-state', 'paused');\n                });\n            }\n            paused = true;\n        }\n    };\n    const pause = () => {\n        childAnimations.forEach((animation) => {\n            animation.pause();\n        });\n        pauseAnimation();\n        return ani;\n    };\n    const playCSSAnimations = () => {\n        animationFinish();\n    };\n    const playWebAnimations = () => {\n        webAnimations.forEach((animation) => {\n            animation.play();\n        });\n        if (_keyframes.length === 0 || elements.length === 0) {\n            animationFinish();\n        }\n    };\n    const resetAnimation = () => {\n        if (supportsWebAnimations) {\n            setAnimationStep(0);\n            updateWebAnimation();\n        }\n    };\n    const play = (opts) => {\n        return new Promise((resolve) => {\n            if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n                shouldForceSyncPlayback = true;\n                onFinish(() => (shouldForceSyncPlayback = false), { oneTimeCallback: true });\n            }\n            if (!initialized) {\n                initializeAnimation();\n            }\n            if (finished) {\n                resetAnimation();\n                finished = false;\n            }\n            if (shouldCalculateNumAnimations) {\n                numAnimationsRunning = childAnimations.length + 1;\n                shouldCalculateNumAnimations = false;\n            }\n            /**\n             * When one of these callbacks fires we\n             * need to clear the other's callback otherwise\n             * you can potentially get these callbacks\n             * firing multiple times if the play method\n             * is subsequently called.\n             * Example:\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * animation.stop() (onStop callback is fired, onFinish is not)\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * Total onStop callbacks: 1\n             * Total onFinish callbacks: 2\n             */\n            const onStopCallback = () => {\n                clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n                resolve();\n            };\n            const onFinishCallback = () => {\n                clearCallback(onStopCallback, onStopOneTimeCallbacks);\n                resolve();\n            };\n            /**\n             * The play method resolves when an animation\n             * run either finishes or is cancelled.\n             */\n            onFinish(onFinishCallback, { oneTimeCallback: true });\n            onStop(onStopCallback, { oneTimeCallback: true });\n            childAnimations.forEach((animation) => {\n                animation.play();\n            });\n            if (supportsWebAnimations) {\n                playWebAnimations();\n            }\n            else {\n                playCSSAnimations();\n            }\n            paused = false;\n        });\n    };\n    /**\n     * Stops an animation and resets it state to the\n     * beginning. This does not fire any onFinish\n     * callbacks because the animation did not finish.\n     * However, since the animation was not destroyed\n     * (i.e. the animation could run again) we do not\n     * clear the onFinish callbacks.\n     */\n    const stop = () => {\n        childAnimations.forEach((animation) => {\n            animation.stop();\n        });\n        if (initialized) {\n            cleanUpElements();\n            initialized = false;\n        }\n        resetFlags();\n        onStopOneTimeCallbacks.forEach((onStopCallback) => onStopCallback.c(0, ani));\n        onStopOneTimeCallbacks.length = 0;\n    };\n    const from = (property, value) => {\n        const firstFrame = _keyframes[0];\n        if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n            firstFrame[property] = value;\n        }\n        else {\n            _keyframes = [{ offset: 0, [property]: value }, ..._keyframes];\n        }\n        return ani;\n    };\n    const to = (property, value) => {\n        const lastFrame = _keyframes[_keyframes.length - 1];\n        if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n            lastFrame[property] = value;\n        }\n        else {\n            _keyframes = [..._keyframes, { offset: 1, [property]: value }];\n        }\n        return ani;\n    };\n    const fromTo = (property, fromValue, toValue) => {\n        return from(property, fromValue).to(property, toValue);\n    };\n    return (ani = {\n        parentAnimation,\n        elements,\n        childAnimations,\n        id,\n        animationFinish,\n        from,\n        to,\n        fromTo,\n        parent,\n        play,\n        pause,\n        stop,\n        destroy,\n        keyframes,\n        addAnimation,\n        addElement,\n        update,\n        fill,\n        direction,\n        iterations,\n        duration,\n        easing,\n        delay,\n        getWebAnimations,\n        getKeyframes,\n        getFill,\n        getDirection,\n        getDelay,\n        getIterations,\n        getEasing,\n        getDuration,\n        afterAddRead,\n        afterAddWrite,\n        afterClearStyles,\n        afterStyles,\n        afterRemoveClass,\n        afterAddClass,\n        beforeAddRead,\n        beforeAddWrite,\n        beforeClearStyles,\n        beforeStyles,\n        beforeRemoveClass,\n        beforeAddClass,\n        onFinish,\n        isRunning,\n        progressStart,\n        progressStep,\n        progressEnd,\n    });\n};\n\nexport { createAnimation as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst doc = typeof document !== 'undefined' ? document : undefined;\n\nexport { doc as d, win as w };\n"], "names": ["w", "win", "animationPrefix", "getAnimationPrefix", "el", "undefined", "supportsUnprefixed", "style", "animationName", "supportsWebkitPrefix", "webkitAnimationName", "setStyleProperty", "element", "propertyName", "value", "prefix", "startsWith", "setProperty", "addClassToArray", "classes", "className", "classNameToAppend", "Array", "isArray", "createAnimation", "animationId", "_delay", "_duration", "_easing", "_iterations", "_fill", "_direction", "_keyframes", "beforeAddClasses", "beforeRemoveClasses", "initialized", "parentAnimation", "beforeStylesValue", "afterAddClasses", "afterRemoveClasses", "afterStylesValue", "numAnimationsRunning", "shouldForceLinearEasing", "shouldForceSyncPlayback", "forceDirectionValue", "forceDurationValue", "forceDelayValue", "willComplete", "finished", "shouldCalculateNumAnimations", "ani", "paused", "id", "onFinishCallbacks", "onFinishOneTimeCallbacks", "onStopOneTimeCallbacks", "elements", "childAnimations", "stylesheets", "_beforeAddReadFunctions", "_beforeAddWriteFunctions", "_afterAddReadFunctions", "_afterAddWriteFunctions", "webAnimations", "supportsAnimationEffect", "AnimationEffect", "supportsWebAnimations", "Element", "prototype", "animate", "getWebAnimations", "destroy", "clearStyleSheets", "for<PERSON>ach", "childAnimation", "cleanUp", "length", "clearOnFinish", "cleanUpElements", "cleanUpStyleSheets", "resetFlags", "isRunning", "clearCallback", "callback<PERSON><PERSON><PERSON><PERSON><PERSON>", "callbackObjects", "index", "findIndex", "callbackObject", "c", "splice", "onStop", "callback", "opts", "push", "o", "onFinish", "callbacks", "oneTimeCallback", "animation", "cancel", "stylesheet", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "beforeAddRead", "readFn", "beforeAddWrite", "writeFn", "afterAddRead", "afterAddWrite", "beforeAddClass", "beforeRemoveClass", "beforeStyles", "styles", "beforeClearStyles", "propertyNames", "property", "afterAddClass", "afterRemoveClass", "afterStyles", "afterClearStyles", "getFill", "getDirection", "getEasing", "getDuration", "getIterations", "get<PERSON>elay", "getKeyframes", "direction", "animationDirection", "update", "fill", "animationFill", "delay", "animationDelay", "easing", "animationEasing", "duration", "animationDuration", "iterations", "animationIterations", "parent", "addElement", "nodeType", "i", "console", "error", "addAnimation", "animationToAdd", "keyframes", "keyframeValues", "different", "updateKeyframes", "keyframeEffect", "effect", "setKeyframes", "newEffect", "KeyframeEffect", "target", "getTiming", "beforeAnimation", "addClasses", "removeClasses", "elementClassList", "classList", "add", "remove", "hasOwnProperty", "afterAnimation", "currentStep", "onFinishCallback", "animationFinish", "initializeWebAnimation", "pause", "onfinish", "initializeAnimation", "setAnimationStep", "step", "Math", "min", "max", "currentTime", "getComputedTiming", "updateWebAnimation", "updateTiming", "deep", "toggleAnimationName", "progressStart", "forceLinearEasing", "pauseAnimation", "progressStep", "progressEnd", "playTo", "dur", "play", "playCSSAnimations", "playWebAnimations", "resetAnimation", "Promise", "resolve", "sync", "onStopCallback", "stop", "from", "firstFrame", "offset", "to", "<PERSON><PERSON><PERSON><PERSON>", "fromTo", "fromValue", "toValue", "window", "doc", "document", "d"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1]}