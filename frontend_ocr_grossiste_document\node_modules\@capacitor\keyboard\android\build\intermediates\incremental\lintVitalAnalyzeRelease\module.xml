<lint-module
    format="1"
    dir="C:\Users\<USER>\Downloads\Work __Abder<PERSON>mane_ouhna\OCR_DOCUMENT_GROSSISTE\Frontend ocr grossiste document\frontend_ocr_grossiste_document\node_modules\@capacitor\keyboard\android"
    name=":capacitor-keyboard"
    type="LIBRARY"
    maven="android:capacitor-keyboard:"
    agpVersion="8.2.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
