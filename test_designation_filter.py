#!/usr/bin/env python3
"""
Test script to verify that rows with empty designation strings are filtered out
while keeping rows with null/None designations.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from app.utils.document_model import DocumentModel

def test_designation_filtering():
    """Test that empty designation strings are filtered out but null designations are kept."""
    
    # Create a DocumentModel instance
    doc_model = DocumentModel()
    
    # Create test table entries
    # Entry 1: Normal entry with designation
    entry1 = DocumentModel.Table()
    entry1.designation = "CLOMITER CRE CR"
    entry1.quantity = "1"
    entry1.pph = "19.614"
    entry1.ppv = "29.7"
    
    # Entry 2: Entry with empty string designation (should be filtered out)
    entry2 = DocumentModel.Table()
    entry2.designation = ""
    entry2.quantity = "1"
    entry2.pph = "7.937"
    entry2.ppv = "12"
    
    # Entry 3: Entry with None designation (should be kept)
    entry3 = DocumentModel.Table()
    entry3.designation = None
    entry3.quantity = "4"
    entry3.pph = "48.3"
    entry3.ppv = "69"
    
    # Entry 4: Another normal entry
    entry4 = DocumentModel.Table()
    entry4.designation = "BRUFEN 400MG 30CP co"
    entry4.quantity = "2"
    entry4.pph = "23.323"
    entry4.ppv = "35.3"
    
    # Entry 5: Another empty string designation (should be filtered out)
    entry5 = DocumentModel.Table()
    entry5.designation = ""
    entry5.quantity = "000"
    entry5.pph = ""
    entry5.ppv = ""
    
    # Add all entries to the document model
    doc_model.table = [entry1, entry2, entry3, entry4, entry5]
    
    print("Original table entries:")
    for i, entry in enumerate(doc_model.table):
        print(f"  Entry {i+1}: designation='{entry.designation}', quantity='{entry.quantity}'")
    
    # Convert to dictionary (this should apply the filtering)
    result_dict = doc_model.to_dict()
    
    print(f"\nOriginal table length: {len(doc_model.table)}")
    print(f"Filtered table length: {len(result_dict['table'])}")
    
    print("\nFiltered table entries:")
    for i, entry in enumerate(result_dict['table']):
        print(f"  Entry {i+1}: designation='{entry['designation']}', quantity='{entry['quantity']}'")
    
    # Verify the results
    expected_entries = 3  # entry1, entry3 (None designation), entry4
    actual_entries = len(result_dict['table'])
    
    if actual_entries == expected_entries:
        print(f"\n✅ SUCCESS: Filtering worked correctly!")
        print(f"   Expected {expected_entries} entries, got {actual_entries}")
        
        # Check that the remaining entries are the correct ones
        designations = [entry['designation'] for entry in result_dict['table']]
        expected_designations = ["CLOMITER CRE CR", None, "BRUFEN 400MG 30CP co"]
        
        if designations == expected_designations:
            print("✅ SUCCESS: Correct entries were kept!")
        else:
            print(f"❌ ERROR: Wrong entries kept. Expected {expected_designations}, got {designations}")
            return False
    else:
        print(f"❌ ERROR: Expected {expected_entries} entries, but got {actual_entries}")
        return False
    
    return True

def test_merge_parts_filtering():
    """Test that the merge_parts function also applies filtering."""
    from app.utils.document_model import merge_parts
    
    # Create test components
    general = DocumentModel.General()
    header = DocumentModel.Header()
    footer = DocumentModel.Footer()
    
    # Create test table entries
    entry1 = DocumentModel.Table()
    entry1.designation = "Valid Entry"
    entry1.quantity = "1"
    
    entry2 = DocumentModel.Table()
    entry2.designation = ""  # Should be filtered out
    entry2.quantity = "2"
    
    entry3 = DocumentModel.Table()
    entry3.designation = None  # Should be kept
    entry3.quantity = "3"
    
    table = [entry1, entry2, entry3]
    
    print("\nTesting merge_parts function:")
    print("Original table entries:")
    for i, entry in enumerate(table):
        print(f"  Entry {i+1}: designation='{entry.designation}', quantity='{entry.quantity}'")
    
    # Test merge_parts
    result = merge_parts(general, header, table, footer)
    
    print(f"\nOriginal table length: {len(table)}")
    print(f"Filtered table length: {len(result['table'])}")
    
    print("\nFiltered table entries:")
    for i, entry in enumerate(result['table']):
        print(f"  Entry {i+1}: designation='{entry['designation']}', quantity='{entry['quantity']}'")
    
    # Verify the results
    expected_entries = 2  # entry1 and entry3 (None designation)
    actual_entries = len(result['table'])
    
    if actual_entries == expected_entries:
        print(f"\n✅ SUCCESS: merge_parts filtering worked correctly!")
        return True
    else:
        print(f"❌ ERROR: Expected {expected_entries} entries, but got {actual_entries}")
        return False

if __name__ == "__main__":
    print("Testing designation filtering logic...")
    print("=" * 60)
    
    success1 = test_designation_filtering()
    success2 = test_merge_parts_filtering()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED!")
        sys.exit(1)
