<variant
    name="release"
    package="com.capacitorjs.plugins.camera"
    minSdkVersion="22"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.2.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\classes;build\intermediates\compile_r_class_jar\release\R.jar"
      type="MAIN"
      applicationId="com.capacitorjs.plugins.camera"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\cb88afe6ffd350568a48997eba8edc37\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
