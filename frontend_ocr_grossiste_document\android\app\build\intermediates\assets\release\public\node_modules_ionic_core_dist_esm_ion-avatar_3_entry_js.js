"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js"],{

/***/ 19642:
/*!*****************************************************************!*\
  !*** ./node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ion_avatar: () => (/* binding */ Avatar),
/* harmony export */   ion_badge: () => (/* binding */ Badge),
/* harmony export */   ion_thumbnail: () => (/* binding */ Thumbnail)
/* harmony export */ });
/* harmony import */ var _index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-c71c5417.js */ 5634);
/* harmony import */ var _ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ionic-global-b9c0d1da.js */ 36331);
/* harmony import */ var _theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./theme-01f3f29c.js */ 71882);
/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */



const avatarIosCss = ":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}";
const IonAvatarIosStyle0 = avatarIosCss;
const avatarMdCss = ":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}";
const IonAvatarMdStyle0 = avatarMdCss;
const Avatar = class {
  constructor(hostRef) {
    (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    return (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.f, {
      key: 'dc1e3cd535e419eebe5599574fd2393ebfde8bbc',
      class: (0,_ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_1__.b)(this)
    }, (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'edb8441c063ea592b41345ea97d88ecd90cb3052'
    }));
  }
};
Avatar.style = {
  ios: IonAvatarIosStyle0,
  md: IonAvatarMdStyle0
};
const badgeIosCss = ":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}";
const IonBadgeIosStyle0 = badgeIosCss;
const badgeMdCss = ":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}";
const IonBadgeMdStyle0 = badgeMdCss;
const Badge = class {
  constructor(hostRef) {
    (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    this.color = undefined;
  }
  render() {
    const mode = (0,_ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_1__.b)(this);
    return (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.f, {
      key: '1253618692342bcf9487188402dc3d49ae0de480',
      class: (0,_theme_01f3f29c_js__WEBPACK_IMPORTED_MODULE_2__.c)(this.color, {
        [mode]: true
      })
    }, (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: '71d65e203965ea37b94504a8a0a96beb52d4e356'
    }));
  }
};
Badge.style = {
  ios: IonBadgeIosStyle0,
  md: IonBadgeMdStyle0
};
const thumbnailCss = ":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}";
const IonThumbnailStyle0 = thumbnailCss;
const Thumbnail = class {
  constructor(hostRef) {
    (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
  }
  render() {
    return (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.f, {
      key: 'ea55000055f941b0c79561e8934be6242ec8e114',
      class: (0,_ionic_global_b9c0d1da_js__WEBPACK_IMPORTED_MODULE_1__.b)(this)
    }, (0,_index_c71c5417_js__WEBPACK_IMPORTED_MODULE_0__.h)("slot", {
      key: 'a4f934f442797f5c66a77e0ef8920fdd07c204f2'
    }));
  }
};
Thumbnail.style = IonThumbnailStyle0;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js.js.map