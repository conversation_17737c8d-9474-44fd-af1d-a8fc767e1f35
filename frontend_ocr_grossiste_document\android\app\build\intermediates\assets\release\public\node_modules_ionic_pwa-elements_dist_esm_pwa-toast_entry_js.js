"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["node_modules_ionic_pwa-elements_dist_esm_pwa-toast_entry_js"],{

/***/ 70071:
/*!**********************************************************************!*\
  !*** ./node_modules/@ionic/pwa-elements/dist/esm/pwa-toast.entry.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   pwa_toast: () => (/* binding */ PWAToast)
/* harmony export */ });
/* harmony import */ var _index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-1c5c47b4.js */ 54787);

const toastCss = ":host{position:fixed;bottom:20px;left:0;right:0;display:-ms-flexbox;display:flex;opacity:0}:host(.in){-webkit-transition:opacity 300ms;transition:opacity 300ms;opacity:1}:host(.out){-webkit-transition:opacity 1s;transition:opacity 1s;opacity:0}.wrapper{-ms-flex:1;flex:1;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.toast{font-family:-apple-system, system-ui, \"Helvetica Neue\", Roboto, sans-serif;background-color:#eee;color:black;border-radius:5px;padding:10px 15px;font-size:14px;font-weight:500;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.20);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.20)}";
const PWAToast = class {
  constructor(hostRef) {
    (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.r)(this, hostRef);
    this.message = undefined;
    this.duration = 2000;
    this.closing = null;
  }
  hostData() {
    const classes = {
      out: !!this.closing
    };
    if (this.closing !== null) {
      classes['in'] = !this.closing;
    }
    return {
      class: classes
    };
  }
  componentDidLoad() {
    setTimeout(() => {
      this.closing = false;
    });
    setTimeout(() => {
      this.close();
    }, this.duration);
  }
  close() {
    this.closing = true;
    setTimeout(() => {
      this.el.parentNode.removeChild(this.el);
    }, 1000);
  }
  __stencil_render() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.h)("div", {
      class: "wrapper"
    }, (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.h)("div", {
      class: "toast"
    }, this.message));
  }
  get el() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.g)(this);
  }
  render() {
    return (0,_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.h)(_index_1c5c47b4_js__WEBPACK_IMPORTED_MODULE_0__.H, this.hostData(), this.__stencil_render());
  }
};
PWAToast.style = toastCss;


/***/ })

}]);
//# sourceMappingURL=node_modules_ionic_pwa-elements_dist_esm_pwa-toast_entry_js.js.map