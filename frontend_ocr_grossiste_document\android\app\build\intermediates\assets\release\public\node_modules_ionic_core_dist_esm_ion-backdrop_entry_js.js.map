{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-backdrop_entry_js.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC4F;AAC/B;AAE7D,MAAMS,cAAc,GAAG,wWAAwW;AAC/X,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,wWAAwW;AAC9X,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGb,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACc,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACAC,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACC,OAAO,CAACD,EAAE,CAAC;EACpB;EACAC,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,IAAI,CAACF,eAAe,EAAE;MACtBE,EAAE,CAACE,cAAc,CAAC,CAAC;MACnBF,EAAE,CAACF,eAAe,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACF,cAAc,CAACQ,IAAI,CAAC,CAAC;IAC9B;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGlB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQJ,qDAAC,CAACE,iDAAI,EAAE;MAAEqB,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE,IAAI;MAAE,aAAa,EAAE,MAAM;MAAEC,KAAK,EAAE;QACzG,CAACH,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE,CAAC,IAAI,CAACT,OAAO;QAC9B,sBAAsB,EAAE,CAAC,IAAI,CAACC;MAClC;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDL,QAAQ,CAACiB,KAAK,GAAG;EACbC,GAAG,EAAErB,oBAAoB;EACzBsB,EAAE,EAAEpB;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host } from './index-c71c5417.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropIosStyle0 = backdropIosCss;\n\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\nconst IonBackdropMdStyle0 = backdropMdCss;\n\nconst Backdrop = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n        this.visible = true;\n        this.tappable = true;\n        this.stopPropagation = true;\n    }\n    onMouseDown(ev) {\n        this.emitTap(ev);\n    }\n    emitTap(ev) {\n        if (this.stopPropagation) {\n            ev.preventDefault();\n            ev.stopPropagation();\n        }\n        if (this.tappable) {\n            this.ionBackdropTap.emit();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'c803b4302c8e722064feb03dafe3cb6e190b4f2b', tabindex: \"-1\", \"aria-hidden\": \"true\", class: {\n                [mode]: true,\n                'backdrop-hide': !this.visible,\n                'backdrop-no-tappable': !this.tappable,\n            } }));\n    }\n};\nBackdrop.style = {\n    ios: IonBackdropIosStyle0,\n    md: IonBackdropMdStyle0\n};\n\nexport { Backdrop as ion_backdrop };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "b", "getIonMode", "backdropIosCss", "IonBackdropIosStyle0", "backdropMdCss", "IonBackdropMdStyle0", "Backdrop", "constructor", "hostRef", "ionBackdropTap", "visible", "tappable", "stopPropagation", "onMouseDown", "ev", "emitTap", "preventDefault", "emit", "render", "mode", "key", "tabindex", "class", "style", "ios", "md", "ion_backdrop"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}