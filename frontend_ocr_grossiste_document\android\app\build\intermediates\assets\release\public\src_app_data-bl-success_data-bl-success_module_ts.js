"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_data-bl-success_data-bl-success_module_ts"],{

/***/ 78886:
/*!*******************************************************************!*\
  !*** ./src/app/data-bl-success/data-bl-success-routing.module.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBlSuccessPageRoutingModule: () => (/* binding */ DataBlSuccessPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _data_bl_success_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./data-bl-success.page */ 51992);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _DataBlSuccessPageRoutingModule;




const routes = [{
  path: '',
  component: _data_bl_success_page__WEBPACK_IMPORTED_MODULE_0__.DataBlSuccessPage
}];
class DataBlSuccessPageRoutingModule {}
_DataBlSuccessPageRoutingModule = DataBlSuccessPageRoutingModule;
_DataBlSuccessPageRoutingModule.ɵfac = function DataBlSuccessPageRoutingModule_Factory(t) {
  return new (t || _DataBlSuccessPageRoutingModule)();
};
_DataBlSuccessPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _DataBlSuccessPageRoutingModule
});
_DataBlSuccessPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](DataBlSuccessPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 53775:
/*!***********************************************************!*\
  !*** ./src/app/data-bl-success/data-bl-success.module.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBlSuccessPageModule: () => (/* binding */ DataBlSuccessPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _data_bl_success_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./data-bl-success-routing.module */ 78886);
/* harmony import */ var _data_bl_success_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./data-bl-success.page */ 51992);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _DataBlSuccessPageModule;





 // Import SharedModule

class DataBlSuccessPageModule {}
_DataBlSuccessPageModule = DataBlSuccessPageModule;
_DataBlSuccessPageModule.ɵfac = function DataBlSuccessPageModule_Factory(t) {
  return new (t || _DataBlSuccessPageModule)();
};
_DataBlSuccessPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _DataBlSuccessPageModule
});
_DataBlSuccessPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _data_bl_success_routing_module__WEBPACK_IMPORTED_MODULE_0__.DataBlSuccessPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](DataBlSuccessPageModule, {
    declarations: [_data_bl_success_page__WEBPACK_IMPORTED_MODULE_1__.DataBlSuccessPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _data_bl_success_routing_module__WEBPACK_IMPORTED_MODULE_0__.DataBlSuccessPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
  });
})();

/***/ }),

/***/ 51992:
/*!*********************************************************!*\
  !*** ./src/app/data-bl-success/data-bl-success.page.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataBlSuccessPage: () => (/* binding */ DataBlSuccessPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_signal_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/signal.service */ 56658);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sweetalert2 */ 37581);
/* harmony import */ var sweetalert2__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(sweetalert2__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);

var _DataBlSuccessPage;










const _c0 = ["idBlInput"];
const _c1 = () => ["/profile"];
class DataBlSuccessPage {
  constructor() {
    this.slidesData = [];
    this.navCtrl = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_6__.NavController);
    this.signalService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_services_signal_service__WEBPACK_IMPORTED_MODULE_1__.SignalService);
    this.loadingController = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.LoadingController);
    this.alertController = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_7__.AlertController);
    this.supplier_name = '';
    this.apiService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_services_api_service__WEBPACK_IMPORTED_MODULE_2__.ApiService);
    this.date_BL_origine = '';
  }
  ngOnInit() {
    this.BL_id = history.state.BL_id;
    this.supplier_name = history.state.supplier_name;
    console.log('BL_id:', this.BL_id);
    this.slidesData = this.signalService.getTransformedData();
    if (this.slidesData.length === 0) {
      this.navCtrl.navigateBack('/scan-bl');
    }
    console.log('Received data:', this.slidesData);
    localStorage.removeItem('forceSupplierGlobal');
  }
  focusInput() {
    if (this.idBlInput) {
      setTimeout(() => {
        this.idBlInput.setFocus();
      }, 150);
    }
  }
  onInputClick(event) {
    event.preventDefault();
    const input = event.target;
    input.focus();
  }
  IgnorerBL() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().fire({
        icon: 'warning',
        title: "vous allez ignorer le BL",
        html: 'Veuillez confirmer ?',
        footer: '<span class="btn_swal_custom">Scanner un autre BL</span>',
        showConfirmButton: false,
        showCloseButton: true,
        customClass: {
          closeButton: 'custom-close-button',
          popup: 'custom-popup',
          footer: 'custom-footer' // Custom class for the footer
        },
        didOpen: () => {
          var _Swal$getFooter;
          const footerLink = (_Swal$getFooter = sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().getFooter()) === null || _Swal$getFooter === void 0 ? void 0 : _Swal$getFooter.querySelector('.btn_swal_custom');
          if (footerLink) {
            footerLink.addEventListener('click', () => {
              sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().close();
              _this.signalService.removeAllData();
              localStorage.removeItem('selectedSupplier');
              _this.navCtrl.navigateRoot('/scan-bl');
            });
          }
        }
      });
    })();
  }
  sendBL() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this2.BL_id) {
        if (_this2.id_BL_origine) {
          try {
            var _this2$date_BL_origin, _this2$supplier_name;
            yield _this2.apiService.updateBLStatus(_this2.BL_id, 'EN_COURS', _this2.id_BL_origine, (_this2$date_BL_origin = _this2.date_BL_origine) !== null && _this2$date_BL_origin !== void 0 ? _this2$date_BL_origin : '', (_this2$supplier_name = _this2.supplier_name) !== null && _this2$supplier_name !== void 0 ? _this2$supplier_name : '').toPromise();
            // Show success message
            sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().fire({
              icon: 'success',
              title: 'Le BL a été envoyé avec succès',
              html: 'Vous pouvez retrouver les résultats du BL dans votre espace <b>WinPlusPharma</b> :<br><p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>',
              footer: "<span class='btn_swal_custom'>D'accord</span>",
              showConfirmButton: false,
              showCloseButton: true,
              customClass: {
                closeButton: 'custom-close-button',
                popup: 'custom-popup',
                footer: 'custom-footer'
              },
              didOpen: () => {
                var _Swal$getFooter2;
                const footerLink = (_Swal$getFooter2 = sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().getFooter()) === null || _Swal$getFooter2 === void 0 ? void 0 : _Swal$getFooter2.querySelector('.btn_swal_custom');
                if (footerLink) {
                  footerLink.addEventListener('click', () => {
                    sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().close();
                    _this2.navCtrl.navigateRoot('/scan-bl');
                  });
                }
              }
            });
            // Navigate to scan-bl page and remove all data
            _this2.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            _this2.navCtrl.navigateRoot('/scan-bl');
          } catch (error) {
            console.error('Error sending BL:', error);
            // Show error message
            sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().fire({
              icon: 'error',
              title: "Erreur lors de l'envoi du BL",
              html: 'Veuillez réessayer plus tard.',
              footer: '<a href="/guide">Comment capturer une image de qualité ?</a>',
              showConfirmButton: false,
              showCloseButton: true,
              customClass: {
                closeButton: 'custom-close-button',
                popup: 'custom-popup',
                footer: 'custom-footer' // Custom class for the footer
              }
            });
          }
        } else {
          // Show error message of the BL ID Field Required
          sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().fire({
            icon: 'error',
            title: 'Erreur de validation du BL !',
            html: 'Veuillez renseigner le BL',
            footer: '<span class="btn_swal_custom">Ressayer</span>',
            showConfirmButton: false,
            showCloseButton: true,
            customClass: {
              closeButton: 'custom-close-button',
              popup: 'custom-popup',
              footer: 'custom-footer' // Custom class for the footer
            },
            didOpen: () => {
              var _Swal$getFooter3;
              const footerLink = (_Swal$getFooter3 = sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().getFooter()) === null || _Swal$getFooter3 === void 0 ? void 0 : _Swal$getFooter3.querySelector('.btn_swal_custom');
              if (footerLink) {
                footerLink.addEventListener('click', () => {
                  sweetalert2__WEBPACK_IMPORTED_MODULE_3___default().close();
                });
              }
            }
          });
        }
      }
    })();
  }
  NewBL() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this3.alertController.create({
        header: 'Voulez-vous créer un nouveau document ?',
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Nouveau BL',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            _this3.signalService.removeAllData();
            localStorage.removeItem('selectedSupplier');
            _this3.navCtrl.navigateForward('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  EditCurrentBL() {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this4.alertController.create({
        header: 'Voulez-vous vraiment modifier les pages de ce document ?',
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button-rename-doc cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Modifier',
          cssClass: 'custom-alert-button-rename-doc rename',
          handler: () => {
            _this4.navCtrl.navigateRoot('/process-doc');
          }
        }]
      });
      yield alert.present();
    })();
  }
  logout() {
    var _this5 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield _this5.apiService.logout(); // Wait for the confirmation dialog
      _this5.navCtrl.navigateRoot('/login'); // Then navigate to login
    })();
  }
}
_DataBlSuccessPage = DataBlSuccessPage;
_DataBlSuccessPage.ɵfac = function DataBlSuccessPage_Factory(t) {
  return new (t || _DataBlSuccessPage)();
};
_DataBlSuccessPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
  type: _DataBlSuccessPage,
  selectors: [["app-data-bl-success"]],
  viewQuery: function DataBlSuccessPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 5);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.idBlInput = _t.first);
    }
  },
  decls: 51,
  vars: 9,
  consts: [["idBlInput", ""], ["slot", "end"], [3, "click"], ["slot", "icon-only", "name", "log-out-outline"], [1, "modal-overlay", "data-bl-wrapper"], [1, "modal-container"], [1, "close-button"], [1, "modal-title"], [1, "modal-message"], ["lines", "none", 1, "input-item"], [1, "label-wrapper"], ["for", "id-bl"], [1, "required-star"], ["id", "id-bl", "placeholder", "Num\u00E9ro du BL", "type", "text", "autocomplete", "off", "autocorrect", "off", "enterkeyhint", "next", 3, "ngModelChange", "click", "clearInput", "value", "ngModel"], ["for", "date-bl"], ["id", "date-bl", "placeholder", "Date BL", "type", "date", 3, "ngModelChange", "clearInput", "value", "ngModel"], [1, "modal-buttons"], [1, "button", "button-cancel", "button-modal", 3, "click"], [1, "button", "button-success", "button-modal", 3, "click"], ["size", "small", 1, "menu-button", "active", 3, "click"], ["name", "files"], [1, "menu-button-middle", 3, "click"], ["name", "extract"], ["size", "small", 1, "menu-button", 3, "routerLink"], ["name", "settings"]],
  template: function DataBlSuccessPage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Envoyer les donn\u00E9es");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "ion-buttons", 1)(5, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_ion_button_click_5_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.logout());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](6, "ion-icon", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "ion-content")(8, "div", 4)(9, "div", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](10, "button", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "h2", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](12, "Traitement termin\u00E9 avec succ\u00E8s");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](13, "p", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](14, " Votre BL de ");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](15, "b");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](16);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](17, " a \u00E9t\u00E9 trait\u00E9 et est pr\u00EAt \u00E0 \u00EAtre envoy\u00E9. ");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "ion-row")(19, "ion-col")(20, "ion-item", 9)(21, "div", 10)(22, "label", 11);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](23, " Num\u00E9ro du BL ");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](24, "span", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](25, "*");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "ion-input", 13, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function DataBlSuccessPage_Template_ion_input_ngModelChange_26_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.id_BL_origine, $event) || (ctx.id_BL_origine = $event);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_ion_input_click_26_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.onInputClick($event));
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "ion-row")(29, "ion-col")(30, "ion-item", 9)(31, "div", 10)(32, "label", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](33, " Date Bl ");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](34, "ion-input", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function DataBlSuccessPage_Template_ion_input_ngModelChange_34_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.date_BL_origine, $event) || (ctx.date_BL_origine = $event);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](35, "div", 16)(36, "button", 17);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_button_click_36_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.IgnorerBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](37, "Ignorer");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](38, "button", 18);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_button_click_38_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.sendBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](39, "Envoyer");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](40, "ion-footer")(41, "ion-toolbar")(42, "ion-buttons")(43, "ion-button", 19);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_ion_button_click_43_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.EditCurrentBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](44, "app-custom-icon", 20);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "ion-button", 21);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function DataBlSuccessPage_Template_ion_button_click_45_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.NewBL());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](46, "app-custom-icon", 22);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](47, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](48, "NOUVEAU");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "ion-button", 23);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](50, "app-custom-icon", 24);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](16);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](ctx.supplier_name);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpropertyInterpolate"]("value", ctx.id_BL_origine);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("clearInput", true);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.id_BL_origine);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](8);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpropertyInterpolate"]("value", ctx.date_BL_origine);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("clearInput", true);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.date_BL_origine);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](15);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](8, _c1));
    }
  },
  dependencies: [_angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonCol, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonInput, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonRow, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.TextValueAccessor, _ionic_angular__WEBPACK_IMPORTED_MODULE_7__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_9__.RouterLink, _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_4__.CustomIconComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];[_nghost-%COMP%] {\n  height: 100dvh;\n}\n\n*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  scrollbar-width: none !important;\n  -ms-overflow-style: none !important;\n}\n\nion-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  --offset-top: 0px !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 70px;\n  --border: 0;\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n}\n\n  ion-header ion-toolbar app-custom-icon .custom-icon {\n  width: 40px !important;\n  height: 40px !important;\n  margin-right: 5px !important;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #101010;\n  padding-right: 1rem;\n}\n\n.data-bl-wrapper[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n  height: 80%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  margin-top: 2rem;\n}\n\n  .scan-bl-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .scan-bl-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n}\n\n.scan-bl-content[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 100px;\n  color: #c4c4c4;\n}\n\nion-label[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\nion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  font-weight: bold;\n  color: #404040;\n  font-size: 14px;\n}\n\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  color: #888888;\n  font-size: 14px;\n  margin-top: 10px;\n  margin-bottom: 10px;\n  opacity: 0.5;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #555555;\n  font-size: 18px;\n  margin-top: 20px;\n}\n\n.arrow-icon[_ngcontent-%COMP%] {\n  font-size: 30px;\n  color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\n.camera-button[_ngcontent-%COMP%] {\n  --background: #3b82f6;\n  --background-activated: #2563eb;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  margin-top: -30px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-button[slot=icon-only][_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 160px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 35px !important;\n  height: 35px !important;\n  color: #fff;\n}\n  .menu-button-middle span {\n  color: #fff;\n  font-weight: 500;\n  font-size: 16px;\n  padding-left: 10px;\n}\n\n.section-title[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  margin-bottom: 0;\n  color: #4b4b4b;\n  font-size: 16px;\n  margin-left: 16px;\n  opacity: 0.5;\n}\n\nswiper-container.swiper[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\n  padding: 0;\n  margin: 0;\n  border-radius: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 10px;\n  margin-left: 10px;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  width: 90%;\n  height: 100%;\n  box-shadow: none;\n  margin: 0;\n  padding: 0;\n  border: 2px solid #e5ebfd;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 150px;\n  object-fit: cover;\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 10px 20px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: flex-start;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  width: 100%;\n  font-size: 13px;\n  text-align: left;\n  font-weight: 600;\n  color: #202020;\n}\n\n.content-global-card[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #e5ebfd;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  padding-bottom: 10px;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1n) {\n  color: #4b4b4b;\n  font-size: 14px;\n  opacity: 0.5;\n}\n.card-doc[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2n) {\n  color: #070707;\n  font-size: 14px;\n  font-weight: 500;\n  opacity: 1;\n}\n\n\n\n.swiper[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.swiper[_ngcontent-%COMP%]   swiper-slide[_ngcontent-%COMP%] {\n  background-position: center;\n  background-size: cover;\n  width: 250px;\n}\n\n  .swiper swiper-slide .swiper-slide-shadow,   .swiper swiper-slide .swiper-slide-shadow-left,   .swiper swiper-slide .swiper-slide-shadow-right {\n  background: none !important;\n  --background: none !important;\n}\n\n.card-doc[_ngcontent-%COMP%] {\n  left: 0px !important;\n}\n\nswiper-container.bottom-swiper[_ngcontent-%COMP%] {\n  width: 100%;\n}\n\n.card-data-bl[_ngcontent-%COMP%] {\n  width: 90%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  background-color: rgba(241, 244, 255, 0.4);\n  border-radius: 10px;\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);\n  margin: 40px 22px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\n  padding: 0 5px;\n}\n\n  .card-data-bl ion-item {\n  font-size: 16px;\n  font-weight: 600;\n  color: #050505;\n  text-align: left;\n  --background: none; \n\n  background-color: none;\n  padding: 0 10px;\n}\n\nion-item[_ngcontent-%COMP%]::part(native) {\n  padding-left: 0 !important;\n  --padding-start: 0px !important;\n  --inner-padding-end: 0px !important;\n}\n\n  .card-data-bl ion-item ion-input div.label-text-wrapper {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #1F41BB;\n  font-weight: bold;\n}\n\n  .card-data-bl ion-item ion-input div.native-wrapper {\n  width: 100%;\n  font-size: 12px;\n  font-weight: 600;\n  color: #050505;\n  text-align: left;\n  margin: 5px 0;\n  padding: 8px 10px;\n  border: 1px solid #1F41BB;\n  border-radius: 10px;\n  --background: #fff; \n\n  background-color: #fff;\n}\n\n  .card-data-bl ion-row:last-child ion-item ion-input div.native-wrapper {\n  margin-bottom: 20px;\n}\n\n  .card-data-bl .native-wrapper {\n  display: flex !important;\n  justify-content: center !important;\n  align-items: center !important;\n  flex-direction: column !important;\n  padding: 14px 10px !important;\n}\n  .card-data-bl .native-wrapper button {\n  display: none;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\n  padding-top: 30px;\n  padding-bottom: 10px;\n  text-align: center;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-direction: row;\n  width: 100%;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%] {\n  font-size: 12px;\n  font-weight: 500;\n  color: #202020;\n  margin-top: 5px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: bold;\n  color: #202020;\n}\n\n  .card-data-bl ion-card-header app-custom-icon {\n  color: #202020;\n  opacity: 0.5;\n  cursor: pointer;\n}\n  .card-data-bl ion-card-header app-custom-icon img {\n  width: 45px !important;\n  height: 45px !important;\n}\n  .card-data-bl ion-card-header app-custom-icon:first-child {\n  margin-left: 10px;\n}\n  .card-data-bl ion-card-header app-custom-icon:last-child {\n  margin-right: 10px;\n}\n\n.card-data-bl[_ngcontent-%COMP%]   .hr-card[_ngcontent-%COMP%] {\n  width: 80%;\n  height: 3px;\n  border-bottom: 2px dotted #8b8b8b;\n  padding: 0 20px;\n  margin-top: 0;\n}\n\n.modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.modal-container[_ngcontent-%COMP%] {\n  background-color: white;\n  padding: 30px;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 400px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);\n  position: relative;\n  border: 1px solid #cacbce;\n}\n\n.close-button[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #999;\n}\n\n.modal-title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  margin-bottom: 15px;\n  text-align: center;\n}\n\n.modal-message[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #666;\n  margin-bottom: 5px;\n}\n\n.modal-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 10px;\n}\n\n.button-modal[_ngcontent-%COMP%] {\n  padding: 12px 25px;\n  border-radius: 8px;\n  font-size: 16px;\n  cursor: pointer;\n  border: none;\n}\n\n.button-cancel[_ngcontent-%COMP%] {\n  background-color: #f1f1f1;\n  color: #333;\n}\n\n.button-success[_ngcontent-%COMP%] {\n  background-color: #2f4fcd;\n  color: white;\n}\n\n.btn_swal_custom[_ngcontent-%COMP%] {\n  background: var(--ion-color-primary, #0054e9);\n  padding: 15px 20px;\n  color: #fff;\n  text-decoration: none;\n  font-weight: bold;\n  border-radius: 10px;\n}\n\n  div:where(.swal2-container) div:where(.swal2-footer) {\n  border-top: 0px !important;\n  margin: 1em 0 10px;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n  background: #fff;\n  height: 80vh;\n}\n\n  .swal2-container {\n  height: 100vh !important;\n}\n\n  .swal2-html-container {\n  padding: 1em 0.6em 0.3em !important;\n}\n  .swal2-html-container ul li {\n  text-align: left;\n  padding: 13px 10px 0 0px;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n  background: #fff;\n  height: 80vh;\n}\n\n  .swal2-container {\n  height: 100vh !important;\n}\n\n  .swal2-html-container {\n  padding: 1em 0.6em 0.3em !important;\n}\n  .swal2-html-container ul li {\n  text-align: left;\n  padding: 13px 10px 0 0px;\n}\n\n  .swal2-footer {\n  padding: 0 !important;\n  margin: 1.5em 0.5rem 2rem;\n}\n  .swal2-footer a {\n  position: relative;\n  font-size: 16px;\n  font-weight: bold;\n  text-decoration: none;\n  padding: 20px 2%;\n  background-color: #2f4fcd;\n  color: #fff;\n  line-height: 1.5;\n  border-radius: 10px;\n  margin-bottom: 10px;\n  line-height: 1.5;\n  box-shadow: 0 0 12px 0px #0053e5;\n  animation: ping 1.2s infinite ease-out;\n}\n  .swal2-footer a::before {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(47, 79, 205, 0.5);\n  border-radius: 5px;\n  transform: scale(1);\n  opacity: 0;\n  animation: _ngcontent-ng-c2504662896_ping-pulse 2s infinite;\n}\n\n.required-star[_ngcontent-%COMP%] {\n  color: red;\n}\n\n  ion-item ion-input div.label-text-wrapper {\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #1F41BB;\n  font-weight: bold;\n}\n\n  ion-item ion-input div.native-wrapper {\n  width: 100%;\n  font-size: 12px;\n  font-weight: 600;\n  color: #050505;\n  text-align: left;\n  margin: 5px 0;\n  padding: 8px 10px;\n  border: 1px solid #1F41BB;\n  border-radius: 10px;\n  --background: #fff; \n\n  background-color: #fff;\n}\n\nion-item[_ngcontent-%COMP%]   .label-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  touch-action: manipulation;\n}\n\nion-item[_ngcontent-%COMP%]   .label-wrapper[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-size: 12px;\n}\n\n  .btn_swal_custom {\n  background: var(--ion-color-primary, #0054e9);\n  padding: 15px 20px;\n  color: #fff;\n  text-decoration: none;\n  font-weight: bold;\n  border-radius: 10px;\n}\n\nion-input[_ngcontent-%COMP%] {\n  --padding-start: 10px;\n  --padding-end: 10px;\n  --padding-top: 10px;\n  --padding-bottom: 10px;\n  touch-action: manipulation;\n}\nion-input[_ngcontent-%COMP%]::part(native) {\n  padding: 0;\n  min-height: 40px;\n}\n\n.ios[_nghost-%COMP%]   ion-input[_ngcontent-%COMP%], .ios   [_nghost-%COMP%]   ion-input[_ngcontent-%COMP%] {\n  --padding-top: 12px;\n  --padding-bottom: 12px;\n}\n\n.input-item[_ngcontent-%COMP%] {\n  pointer-events: auto !important;\n}\n.input-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%] {\n  --background: #ffffff;\n  z-index: 1;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_data-bl-success_data-bl-success_module_ts.js.map