{"version": 3, "file": "src_app_crop-doc_crop-doc_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAYuB;;;;;;;ICAjBC,uDAAA,eAOE;;;;;;;;;;;IAsBAA,4DAFF,QAAqD,gBAUjD;IADAA,wDAAA,yBAAAI,4EAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAN,2DAAA,CAAAQ,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAV,2DAAA;MAAA,OAAAA,yDAAA,CAAeU,MAAA,CAAAG,aAAA,CAAAR,MAAA,EAAAC,IAAA,CAAwB;IAAA,EAAC;IAP1CN,0DAAA,EAQE;IAGFA,uDAAA,iBAQE;IACJA,0DAAA,EAAI;;;;;;IAnBAA,uDAAA,EAAqB;;IAerBA,uDAAA,EAAwC;IAAxCA,yDAAA,iBAAAU,MAAA,CAAAO,WAAA,KAAAX,IAAA,CAAwC;;;;;;;;IAO1CN,4DADF,QAAqD,eAYjD;IADAA,wDAAA,yBAAAkB,0EAAAb,MAAA;MAAA,MAAAc,IAAA,GAAAnB,2DAAA,CAAAoB,GAAA,EAAAX,KAAA;MAAA,MAAAC,MAAA,GAAAV,2DAAA;MAAA,OAAAA,yDAAA,CAAeU,MAAA,CAAAW,qBAAA,CAAAhB,MAAA,EAAAc,IAAA,CAAgC;IAAA,EAAC;IAEpDnB,0DAZE,EAWE,EACA;;;;;;IAHAA,uDAAA,EAA2C;IAA3CA,yDAAA,iBAAAU,MAAA,CAAAY,cAAA,KAAAH,IAAA,CAA2C;;;;;;;IA6B3CnB,uDAAA,gBAME;;;;;;;;;;IAGFA,qEAAA,GAA2C;IAWzCA,uDATA,eAME,eASA;;;;;IAdAA,uDAAA,EAAsC;;IAStCA,uDAAA,EAA2C;;;;;;;IA3CrDA,4DAAA,YAA2C;IAEzCA,uDAAA,iBAKE;IAGFA,4DAAA,mBAA6B;IAC3BA,uDAAA,aAIE;IACJA,0DAAA,EAAW;IAKTA,4DAFF,YAAmC,QAEQ;IAUvCA,wDATA,IAAAyB,2DAAA,oBAME,IAAAC,kEAAA,2BAGyC;IAoB/C1B,0DADE,EAAI,EACF;IAGJA,uDAAA,iBAKE;IACJA,0DAAA,EAAI;;;;IAzDAA,uDAAA,EAA+B;;IAS7BA,uDAAA,GAA+B;;IAS9BA,uDAAA,GAAqC;;IAEnCA,uDAAA,EAAS;IAATA,wDAAA,SAAAU,MAAA,CAAAkB,GAAA,CAAS;IAQG5B,uDAAA,EAA0B;IAA1BA,wDAAA,SAAAU,MAAA,CAAAO,WAAA,UAA0B;IAwB3CjB,uDAAA,EAA+B;;;;AD7GjC,MAAO6B,2BAA2B;EAStCC,YAAYA,CAAA;IACV,IAAI,CAACC,YAAY,EAAE;EACrB;EAGAC,aAAaA,CAACC,KAAmB;IAC/B,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;IAEtBD,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,IAAI,CAACC,UAAU,CAACJ,KAAK,CAAC;EACxB;EAGAK,YAAYA,CAACL,KAAiB;IAC5B,IAAIA,KAAK,CAACM,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAExCP,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,MAAMK,KAAK,GAAGR,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;IAC9B,MAAMG,OAAO,GAAGC,QAAQ,CAACC,gBAAgB,CAACH,KAAK,CAACI,OAAO,EAAEJ,KAAK,CAACK,OAAO,CAAC;IAEvE,IAAIJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEK,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACpD,MAAMvC,KAAK,GAAGwC,QAAQ,CAACP,OAAO,CAACQ,YAAY,CAAC,YAAY,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MACrE,IAAI,CAACC,iBAAiB,CAAClB,KAAK,EAAExB,KAAK,CAAC;;EAExC;EAGA2C,WAAWA,CAACnB,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACC,UAAU,IAAID,KAAK,CAACM,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpDP,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,MAAMK,KAAK,GAAGR,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACF,UAAU,CAACI,KAAK,CAAC;EACxB;EAGAY,UAAUA,CAAA;IACR,IAAI,CAACtB,YAAY,EAAE;EACrB;EAGAuB,mBAAmBA,CAACrB,KAAiB;IACnC,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;EACxB;EAEA;EACA,IAAasB,QAAQA,CAACC,GAAW;IAC/B,IAAIA,GAAG,EAAE;MACP,MAAM5B,GAAG,GAAG,IAAI6B,KAAK,EAAE;MACvB7B,GAAG,CAAC8B,MAAM,GAAG,MAAK;QAChB,IAAI,CAAC9B,GAAG,GAAGA,GAAG;QACd,IAAI,CAAC+B,YAAY,GAAG/B,GAAG,CAACgC,YAAY;QACpC,IAAI,CAACC,aAAa,GAAGjC,GAAG,CAACkC,aAAa;QAEtC;QACA,IAAI,IAAI,CAACC,mBAAmB,EAAE;UAC5B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACD,mBAAmB;UAClD,IAAI,CAACA,mBAAmB,GAAG,IAAI;SAChC,MAAM;UACL,IAAI,CAACE,uBAAuB,EAAE;;QAGhC,IAAI,CAACC,KAAK,CAACC,aAAa,EAAE;MAC5B,CAAC;MACDvC,GAAG,CAACwC,GAAG,GAAGZ,GAAG;;EAEjB;EAEA,IAAaQ,kBAAkBA,CAACK,MAAqB;IACnD,IAAI,CAACA,MAAM,IAAIA,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACyB,uBAAuB,EAAE;MAC9B;;IAGF,MAAMK,WAAW,GAAGD,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,KAAK,KAAM;MACrDC,CAAC,EAAE,OAAOD,KAAK,CAACC,CAAC,KAAK,QAAQ,GAAGD,KAAK,CAACC,CAAC,GAAG,CAAC;MAC5CC,CAAC,EAAE,OAAOF,KAAK,CAACE,CAAC,KAAK,QAAQ,GAAGF,KAAK,CAACE,CAAC,GAAG;KAC5C,CAAC,CAAC;IAEH,OAAOL,WAAW,CAAC9B,MAAM,GAAG,CAAC,EAAE;MAC7B8B,WAAW,CAACM,IAAI,CAAC;QAAEF,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAC,CAAE,CAAC;;IAGlC,IAAI,IAAI,CAAChB,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACkB,IAAI,CAACC,MAAM,GAAGR,WAAW,CAACE,GAAG,CAAEO,KAAK,IAAI;QAC3C,MAAML,CAAC,GAAG,IAAI,CAACM,iBAAiB,CAACD,KAAK,CAACL,CAAC,EAAE,IAAI,CAACf,YAAY,CAAC;QAC5D,MAAMgB,CAAC,GAAG,IAAI,CAACK,iBAAiB,CAACD,KAAK,CAACJ,CAAC,EAAE,IAAI,CAACd,aAAa,CAAC;QAC7D,OAAO,IAAI,CAACoB,gBAAgB,CAAC;UAAEP,CAAC;UAAEC;QAAC,CAAE,CAAC;MACxC,CAAC,CAAC;MACF,IAAI,CAACO,kBAAkB,EAAE,CAAC,CAAC;MAC3B,IAAI,CAAChB,KAAK,CAACC,aAAa,EAAE;KAC3B,MAAM;MACL,IAAI,CAACJ,mBAAmB,GAAGO,WAAW;;EAE1C;EAEA,IAAaa,QAAQA,CAACC,KAAa;IACjC,IAAI,IAAI,CAACC,SAAS,KAAKD,KAAK,EAAE;MAC5B,MAAME,UAAU,GAAGF,KAAK,IAAI,IAAI,CAACC,SAAS,IAAI,CAAC,CAAC;MAChD,IAAI,CAACA,SAAS,GAAGD,KAAK;MAEtB,IAAI,IAAI,CAACP,IAAI,CAACC,MAAM,IAAI,IAAI,CAACS,mBAAmB,CAAC,IAAI,CAACV,IAAI,CAACC,MAAM,CAAC,EAAE;QAClE,IAAI,CAACU,iBAAiB,CAACF,UAAU,CAAC;;MAGpC,IAAI,CAACpB,KAAK,CAACC,aAAa,EAAE;;EAE9B;EAMQa,iBAAiBA,CAACI,KAAa,EAAEK,GAAW;IAClD,OAAOL,KAAK,IAAI,CAAC,GAAGA,KAAK,GAAGK,GAAG,GAAGL,KAAK;EACzC;EA0BAM,YAAmBxB,KAAwB;IAAxB,KAAAA,KAAK,GAALA,KAAK;IA3Jf,KAAAyB,KAAK,GAAW,MAAM;IACtB,KAAAC,MAAM,GAAW,MAAM;IAGtB,KAAAC,iBAAiB,GAAG,IAAI9F,uDAAY,EAAiB;IAuHvD,KAAAsF,SAAS,GAAW,CAAC;IAErB,KAAAtB,mBAAmB,GAAyB,IAAI;IAMxD,KAAAnC,GAAG,GAA4B,IAAI;IACnC,KAAAiD,IAAI,GAA8B;MAAEC,MAAM,EAAE;IAAE,CAAE;IAChD,KAAAnB,YAAY,GAAW,CAAC;IACxB,KAAAE,aAAa,GAAW,CAAC;IACzB,KAAA5C,WAAW,GAAkB,IAAI;IACjC,KAAAiB,UAAU,GAAG,KAAK;IAClB,KAAA4D,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAuB,IAAI;IAEpC,KAAAC,aAAa,GAAuB,IAAI;IACxC,KAAAC,YAAY,GAAuB,IAAI;IAC/C,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,WAAW,GAAG,KAAK;IACX,KAAAC,cAAc,GAAG,KAAK;IAE9B,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,iBAAiB,GAAG;MAAE5B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAClC,KAAA4B,eAAe,GAAG,GAAG,CAAC,CAAC;IACvB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAEhB,KAAAC,SAAS,GAAkB,EAAE,CAAC,CAAC;IAC/B,KAAAnF,cAAc,GAAkB,IAAI,CAAC,CAAC;IACtC,KAAAoF,sBAAsB,GAAqC,IAAI,CAAC,CAAC;EAEnB;EAE9CC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC9B,IAAI,CAACC,MAAM,CAACtC,MAAM,EAAE;MAC5B,IAAI,CAACyB,uBAAuB,EAAE;KAC/B,MAAM;MACL,IAAI,CAAC2C,YAAY,EAAE;;EAEvB;EAEA,IAAIzB,QAAQA,CAAA;IACV,OAAO,IAAI,CAACE,SAAS,IAAI,CAAC;EAC5B;EAEQpB,uBAAuBA,CAAA;IAC7B,IAAI,IAAI,CAACN,YAAY,GAAG,CAAC,IAAI,IAAI,CAACE,aAAa,GAAG,CAAC,EAAE;MACnD,MAAMgD,MAAM,GAAGC,IAAI,CAACC,GAAG,CACrB,EAAE,EACFD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACpD,YAAY,EAAE,IAAI,CAACE,aAAa,CAAC,GAAG,EAAE,CACrD;MACD,IAAI,CAACgB,IAAI,CAACC,MAAM,GAAG,CACjB;QAAEJ,CAAC,EAAEmC,MAAM;QAAElC,CAAC,EAAEkC;MAAM,CAAE,EACxB;QAAEnC,CAAC,EAAE,IAAI,CAACf,YAAY,GAAGkD,MAAM;QAAElC,CAAC,EAAEkC;MAAM,CAAE,EAC5C;QAAEnC,CAAC,EAAE,IAAI,CAACf,YAAY,GAAGkD,MAAM;QAAElC,CAAC,EAAE,IAAI,CAACd,aAAa,GAAGgD;MAAM,CAAE,EACjE;QAAEnC,CAAC,EAAEmC,MAAM;QAAElC,CAAC,EAAE,IAAI,CAACd,aAAa,GAAGgD;MAAM,CAAE,CAC9C,CAACrC,GAAG,CAAEO,KAAK,IAAK,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC,CAAC;MAC9C,IAAI,CAACG,kBAAkB,EAAE,CAAC,CAAC;;EAE/B;EAEA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA8B,gBAAgBA,CAAC/E,KAAyB;IACxC,MAAMgF,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa;IACzC,MAAMC,GAAG,GAAGH,GAAG,CAACI,YAAY,EAAE;IAC9B,IAAI,CAACD,GAAG,EAAE,OAAO;MAAE1C,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAE/B,MAAM9B,OAAO,GAAG,SAAS,IAAIZ,KAAK,GAAGA,KAAK,CAACY,OAAO,GAAGZ,KAAK,CAACY,OAAO;IAClE,MAAMC,OAAO,GAAG,SAAS,IAAIb,KAAK,GAAGA,KAAK,CAACa,OAAO,GAAGb,KAAK,CAACa,OAAO;IAElE,OAAO;MACL4B,CAAC,EAAE,CAAC7B,OAAO,GAAGuE,GAAG,CAACE,CAAC,IAAIF,GAAG,CAACG,CAAC;MAC5B5C,CAAC,EAAE,CAAC7B,OAAO,GAAGsE,GAAG,CAACI,CAAC,IAAIJ,GAAG,CAACK;KAC5B;EACH;EAEA,IAAIC,kBAAkBA,CAACtC,KAAY;IACjC;EAAA;EAGMuC,0BAA0BA,CAAA;IAChC,MAAMC,KAAK,GAAGd,IAAI,CAACe,GAAG,CAAC,IAAI,CAACxC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAMyC,GAAG,GAAIF,KAAK,GAAGd,IAAI,CAACiB,EAAE,GAAI,GAAG;IACnC,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACF,GAAG,CAAC;IACzB,MAAMG,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACH,GAAG,CAAC;IAEzB;IACA,IAAII,YAAY,GAAGpB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAClE,YAAY,GAAGqE,GAAG,CAAC,GAAGlB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAChE,aAAa,GAAGoE,GAAG,CAAC;IACzF,IAAIE,aAAa,GAAGrB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAClE,YAAY,GAAGsE,GAAG,CAAC,GAAGnB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAChE,aAAa,GAAGmE,GAAG,CAAC;IAE1F;IACA,IAAI,IAAI,CAACnE,aAAa,GAAG,IAAI,CAACF,YAAY,EAAE;MAC1C,IAAIiE,KAAK,GAAG,EAAE,IAAIA,KAAK,GAAG,GAAG,EAAE;QAC7B;QACA;QACA,MAAMQ,KAAK,GAAG,IAAI,CAACvE,aAAa,GAAG,IAAI,CAACF,YAAY;QACpDuE,YAAY,GAAGpB,IAAI,CAACC,GAAG,CAACmB,YAAY,EAAE,IAAI,CAACrE,aAAa,CAAC;QACzDsE,aAAa,GAAGD,YAAY,GAAGE,KAAK;OACrC,MAAM;QACL;QACAF,YAAY,GAAG,IAAI,CAACvE,YAAY;QAChCwE,aAAa,GAAG,IAAI,CAACtE,aAAa;;;IAItC,OAAO;MACL8B,KAAK,EAAEuC,YAAY;MACnBtC,MAAM,EAAEuC;KACT;EACH;EAEAE,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC1E,YAAY,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;MAC7C,OAAO,aAAa;;IAGtB,MAAM;MAAE8B,KAAK;MAAEC;IAAM,CAAE,GAAG,IAAI,CAAC+B,0BAA0B,EAAE;IAE3D;IACA,MAAMW,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;IACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;IAEtC;IACA,MAAM2E,YAAY,GAAG7C,KAAK;IAC1B,MAAM8C,aAAa,GAAG7C,MAAM;IAE5B;IACA,MAAM8C,OAAO,GAAGJ,OAAO,GAAGE,YAAY,GAAG,CAAC;IAC1C,MAAMG,OAAO,GAAGJ,OAAO,GAAGE,aAAa,GAAG,CAAC;IAE3C,OAAO,GAAGC,OAAO,IAAIC,OAAO,IAAIH,YAAY,IAAIC,aAAa,EAAE;EACjE;EAEA;EACQxD,gBAAgBA,CAACR,KAAkB;IACzC,MAAM;MAAEkB,KAAK;MAAEC;IAAM,CAAE,GAAG,IAAI,CAAC+B,0BAA0B,EAAE;IAC3D,MAAMW,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;IACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;IAEtC;IACA,MAAM+E,IAAI,GAAGN,OAAO,GAAG3C,KAAK,GAAG,CAAC;IAChC,MAAMkD,IAAI,GAAGP,OAAO,GAAG3C,KAAK,GAAG,CAAC;IAChC,MAAMmD,IAAI,GAAGP,OAAO,GAAG3C,MAAM,GAAG,CAAC;IACjC,MAAMmD,IAAI,GAAGR,OAAO,GAAG3C,MAAM,GAAG,CAAC;IAEjC,OAAO;MACLlB,CAAC,EAAEoC,IAAI,CAACrB,GAAG,CAACmD,IAAI,EAAE9B,IAAI,CAACC,GAAG,CAAC8B,IAAI,EAAEpE,KAAK,CAACC,CAAC,CAAC,CAAC;MAC1CC,CAAC,EAAEmC,IAAI,CAACrB,GAAG,CAACqD,IAAI,EAAEhC,IAAI,CAACC,GAAG,CAACgC,IAAI,EAAEtE,KAAK,CAACE,CAAC,CAAC;KAC1C;EACH;EAEAqE,oBAAoBA,CAAA;IAClB,MAAMV,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;IACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;IACtC,OAAO,UAAU,IAAI,CAACsB,QAAQ,IAAImD,OAAO,IAAIC,OAAO,GAAG;EACzD;EAEAU,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpE,IAAI,CAACC,MAAM,CAACN,GAAG,CAAEO,KAAK,IAAK,GAAGA,KAAK,CAACL,CAAC,IAAIK,KAAK,CAACJ,CAAC,EAAE,CAAC,CAACuE,IAAI,CAAC,GAAG,CAAC;EAC3E;EAEAC,cAAcA,CAAA;IACZ,MAAMrE,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM;IAC/B,MAAMa,KAAK,GAAG,IAAI,CAAChC,YAAY;IAC/B,MAAMiC,MAAM,GAAG,IAAI,CAAC/B,aAAa;IAEjC,IAAI,CAACiB,MAAM,IAAIA,MAAM,CAACtC,MAAM,GAAG,CAAC,EAAE;MAChC,OAAO,WAAWmD,KAAK,MAAMC,MAAM,QAAQ;;IAG7C,OAAO;gBACKD,KAAK,MAAMC,MAAM;UACvBd,MAAM,CAAC,CAAC,CAAC,CAACJ,CAAC,IAAII,MAAM,CAAC,CAAC,CAAC,CAACH,CAAC;UAC1BG,MAAM,CAAC,CAAC,CAAC,CAACJ,CAAC,IAAII,MAAM,CAAC,CAAC,CAAC,CAACH,CAAC;UAC1BG,MAAM,CAAC,CAAC,CAAC,CAACJ,CAAC,IAAII,MAAM,CAAC,CAAC,CAAC,CAACH,CAAC;UAC1BG,MAAM,CAAC,CAAC,CAAC,CAACJ,CAAC,IAAII,MAAM,CAAC,CAAC,CAAC,CAACH,CAAC;KAC/B;EACH;EAEAyE,gBAAgBA,CAAA;IACd,OAAO,EAAE,CAAC,CAAC;EACb;EAEAC,gBAAgBA,CAACpH,KAAiB;IAChC,MAAMgF,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa;IACzC,MAAMC,GAAG,GAAGH,GAAG,CAACI,YAAY,EAAE;IAC9B,IAAI,CAACD,GAAG,EAAE,OAAO;MAAE1C,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAE/B,OAAO;MACLD,CAAC,EAAE,CAACzC,KAAK,CAACY,OAAO,GAAGuE,GAAG,CAACE,CAAC,IAAIF,GAAG,CAACG,CAAC;MAClC5C,CAAC,EAAE,CAAC1C,KAAK,CAACa,OAAO,GAAGsE,GAAG,CAACI,CAAC,IAAIJ,GAAG,CAACK;KAClC;EACH;EAEA5G,aAAaA,CAACoB,KAAmB,EAAExB,KAAa;IAC9C,IAAI,IAAI,CAAC2F,cAAc,EAAE;IAEzBnE,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,IAAI,CAACgE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAClE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjB,WAAW,GAAGR,KAAK;IACxB,IAAI,CAAC4F,aAAa,GAAG,IAAI;IAEzB,MAAMhC,MAAM,GAAG,IAAI,CAACiF,iBAAiB,CAACrH,KAAK,CAAC;IAC5C,IAAI,CAACsH,uBAAuB,CAAClF,MAAM,CAAC;IAEpC,IAAIpC,KAAK,CAACuH,WAAW,KAAK,OAAO,EAAE;MACjC,IAAI,CAACvD,YAAY,GAAG,IAAI,CAAChB,gBAAgB,CAACZ,MAAM,CAAC;KAClD,MAAM;MACL,IAAI,CAAC0B,iBAAiB,GAAG,IAAI,CAACd,gBAAgB,CAACZ,MAAM,CAAC;;IAGxD,IAAI,CAACH,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEAsF,mBAAmBA,CAACxH,KAA8B;IAChDA,KAAK,CAACE,cAAc,EAAE;IACtB,IAAI,CAAC2D,gBAAgB,GAAG,IAAI;IAE5B,IAAI7D,KAAK,YAAYyH,UAAU,EAAE;MAC/B,IAAI,CAAC3D,iBAAiB,GAAG,IAAI,CAACuD,iBAAiB,CAACrH,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;KAClE,MAAM;MACL,IAAI,CAACwD,iBAAiB,GAAG,IAAI,CAACuD,iBAAiB,CAACrH,KAAK,CAAC;;EAE1D;EAEAkB,iBAAiBA,CAAClB,KAAiB,EAAExB,KAAa;IAChDkJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE3H,KAAK,EAAExB,KAAK,CAAC;IAE9C,IAAI,IAAI,CAAC2F,cAAc,EAAE,OAAO,CAAC;IAEjCnE,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,IAAI,CAACgE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACjE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjB,WAAW,GAAGR,KAAK;IAExB,MAAMgC,KAAK,GAAGR,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC0D,YAAY,GAAG,IAAI,CAACqD,iBAAiB,CAAC7G,KAAK,CAAC;IACjD,IAAI,CAACyB,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEA0F,eAAeA,CAAC5H,KAAiB;IAC/BA,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IAEvB,IAAI,CAAC,IAAI,CAACF,UAAU,IAAI,CAAC,IAAI,CAAC4D,gBAAgB,EAAE;IAEhD,MAAMrD,KAAK,GAAGR,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACL,UAAU,IAAI,IAAI,CAACjB,WAAW,KAAK,IAAI,EAAE;MAChD,MAAM6I,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAAC7G,KAAK,CAAC;MAEhD;MACA,IAAI,CAACoC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC7D,WAAW,CAAC,GAAG6I,UAAU;MAC/C,IAAI,CAAC7D,YAAY,GAAG6D,UAAU;MAE9B;MACA,IAAI,CAAC5F,KAAK,CAACC,aAAa,EAAE;;EAE9B;EAEA4F,mBAAmBA,CAAC9H,KAAiB;IACnCA,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IACvB;EACF;EAEA4H,eAAeA,CAAC/H,KAAiB;IAC/B,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;EACxB;EAEQgI,WAAWA,CAAChI,KAAsB,EAAExB,KAAa;IACvD,IAAI,CAACQ,WAAW,GAAGR,KAAK;IACxB,IAAI,CAACyB,UAAU,GAAG,IAAI;EACxB;EAEA;EACA;EACA;EACA;EAEQG,UAAUA,CAAC6H,KAAyB;IAC1C,IAAI,CAAC,IAAI,CAAChI,UAAU,EAAE;IAEtB,MAAM4H,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAACY,KAAK,CAAC;IAChD,IAAIC,UAAU,GAAG,IAAI,CAAClF,gBAAgB,CAAC6E,UAAU,CAAC;IAElD,IAAI,IAAI,CAAC7I,WAAW,KAAK,IAAI,EAAE;MAC7B,MAAMmJ,SAAS,GAAG,CAAC,GAAG,IAAI,CAACvF,IAAI,CAACC,MAAM,CAAC;MACvCsF,SAAS,CAAC,IAAI,CAACnJ,WAAW,CAAC,GAAGkJ,UAAU;MAExC,IAAI,IAAI,CAACE,oBAAoB,CAACD,SAAS,CAAC,EAAE;QACxC,IAAI,CAACvF,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC7D,WAAW,CAAC,GAAGkJ,UAAU;QAC/C,IAAI,CAACjF,kBAAkB,EAAE,CAAC,CAAC;QAC3B,IAAI,CAACqE,uBAAuB,CAACY,UAAU,CAAC;;KAE3C,MAAM,IAAI,IAAI,CAAC7I,cAAc,KAAK,IAAI,EAAE;MACvC,MAAMgJ,QAAQ,GAAG,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAACnF,cAAc,CAAC;MACpD,IAAI,IAAI,CAACoF,sBAAsB,KAAK,YAAY,EAAE;QAChDyD,UAAU,CAACxF,CAAC,GAAG2F,QAAQ,CAAC3F,CAAC,CAAC,CAAC;OAC5B,MAAM,IAAI,IAAI,CAAC+B,sBAAsB,KAAK,UAAU,EAAE;QACrDyD,UAAU,CAACzF,CAAC,GAAG4F,QAAQ,CAAC5F,CAAC,CAAC,CAAC;;MAE7B,IAAI,CAAC6F,8BAA8B,CAAC,IAAI,CAACjJ,cAAc,EAAE6I,UAAU,CAAC;MACpE,IAAI,CAACjF,kBAAkB,EAAE,CAAC,CAAC;MAC3B,IAAI,CAACqE,uBAAuB,CAACY,UAAU,CAAC;;IAG1C,IAAI,CAACpE,iBAAiB,GAAGoE,UAAU;IACnC,IAAI,CAACjG,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEQkG,oBAAoBA,CAACvF,MAAqB;IAChD,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAErC;IACA,MAAMgI,WAAW,GAAG,EAAE;IAEtB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3F,MAAM,CAACtC,MAAM,EAAEiI,CAAC,EAAE,EAAE;MACtC,KAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG5F,MAAM,CAACtC,MAAM,EAAEkI,CAAC,EAAE,EAAE;QAC1C,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC9F,MAAM,CAAC2F,CAAC,CAAC,EAAE3F,MAAM,CAAC4F,CAAC,CAAC,CAAC;QACvD,IAAIC,QAAQ,GAAGH,WAAW,EAAE;UAC1B,OAAO,KAAK;;;;IAKlB;IACA,OAAO,IAAI,CAACK,QAAQ,CAAC/F,MAAM,CAAC;EAC9B;EAEQ8F,WAAWA,CAACE,EAAe,EAAEC,EAAe;IAClD,MAAMC,EAAE,GAAGD,EAAE,CAACrG,CAAC,GAAGoG,EAAE,CAACpG,CAAC;IACtB,MAAMuG,EAAE,GAAGF,EAAE,CAACpG,CAAC,GAAGmG,EAAE,CAACnG,CAAC;IACtB,OAAOmC,IAAI,CAACoE,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EACrC;EAEQJ,QAAQA,CAAC/F,MAAqB;IACpC,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAErC,IAAI2I,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3F,MAAM,CAACtC,MAAM,EAAEiI,CAAC,EAAE,EAAE;MACtC,MAAMK,EAAE,GAAGhG,MAAM,CAAC2F,CAAC,CAAC;MACpB,MAAMM,EAAE,GAAGjG,MAAM,CAAC,CAAC2F,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAC9B,MAAMW,EAAE,GAAGtG,MAAM,CAAC,CAAC2F,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAE9B,MAAMY,YAAY,GAAG,CAACN,EAAE,CAACrG,CAAC,GAAGoG,EAAE,CAACpG,CAAC,KAAK0G,EAAE,CAACzG,CAAC,GAAGmG,EAAE,CAACnG,CAAC,CAAC,GAC9B,CAACoG,EAAE,CAACpG,CAAC,GAAGmG,EAAE,CAACnG,CAAC,KAAKyG,EAAE,CAAC1G,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC;MAEjD,IAAI+F,CAAC,KAAK,CAAC,EAAE;QACXU,IAAI,GAAGrE,IAAI,CAACqE,IAAI,CAACE,YAAY,CAAC;OAC/B,MAAM,IAAIvE,IAAI,CAACqE,IAAI,CAACE,YAAY,CAAC,KAAKF,IAAI,IAAIE,YAAY,KAAK,CAAC,EAAE;QACjE,OAAO,KAAK;;;IAGhB,OAAO,IAAI;EACb;EAEQC,qBAAqBA,CAACxG,MAAqB;IACjD,OAAOA,MAAM,CAACyG,KAAK,CAACxG,KAAK,IACvBA,KAAK,CAACL,CAAC,IAAI,CAAC,IACZK,KAAK,CAACL,CAAC,IAAI,IAAI,CAACf,YAAY,IAC5BoB,KAAK,CAACJ,CAAC,IAAI,CAAC,IACZI,KAAK,CAACJ,CAAC,IAAI,IAAI,CAACd,aAAa,CAC9B;EACH;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEQyF,iBAAiBA,CAACY,KAAyB;IACjD,MAAMjD,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa;IACzC,MAAMC,GAAG,GAAGH,GAAG,CAACI,YAAY,EAAE;IAC9B,IAAI,CAACD,GAAG,EAAE,OAAO;MAAE1C,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;IAE/B,IAAI9B,OAAe;IACnB,IAAIC,OAAe;IAEnB,IAAIoH,KAAK,YAAYsB,UAAU,EAAE;MAC/B3I,OAAO,GAAGqH,KAAK,CAACrH,OAAO;MACvBC,OAAO,GAAGoH,KAAK,CAACpH,OAAO;KACxB,MAAM;MACLD,OAAO,GAAGqH,KAAK,CAACrH,OAAO;MACvBC,OAAO,GAAGoH,KAAK,CAACpH,OAAO;;IAGzB,OAAO;MACL4B,CAAC,EAAE,CAAC7B,OAAO,GAAGuE,GAAG,CAACE,CAAC,IAAIF,GAAG,CAACG,CAAC;MAC5B5C,CAAC,EAAE,CAAC7B,OAAO,GAAGsE,GAAG,CAACI,CAAC,IAAIJ,GAAG,CAACK;KAC5B;EACH;EAEA;EACQgE,kBAAkBA,CAACvB,KAAyB;IAIlD,IAAIA,KAAK,YAAYsB,UAAU,EAAE;MAC/B;MACA,OAAO;QAAE3I,OAAO,EAAEqH,KAAK,CAACrH,OAAO;QAAEC,OAAO,EAAEoH,KAAK,CAACpH;MAAO,CAAE;KAC1D,MAAM;MACL;MACA,OAAO;QAAED,OAAO,EAAEqH,KAAK,CAACrH,OAAO;QAAEC,OAAO,EAAEoH,KAAK,CAACpH;MAAO,CAAE;;EAE7D;EAEAf,YAAYA,CAAA;IACV,IAAI,CAACG,UAAU,GAAG,KAAK;IACvB,IAAI,CAACjB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACK,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACoF,sBAAsB,GAAG,IAAI,CAAC,CAAC;IACpC,IAAI,CAACX,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACI,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACnC,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEMuH,OAAOA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACX;MACA,IAAID,KAAI,CAACtG,SAAS,KAAK,CAAC,EAAE;QACxB,MAAMiD,OAAO,GAAGqD,KAAI,CAAChI,YAAY,GAAG,CAAC;QACrC,MAAM4E,OAAO,GAAGoD,KAAI,CAAC9H,aAAa,GAAG,CAAC;QACtC,MAAMiE,GAAG,GAAI,CAAC6D,KAAI,CAACtG,SAAS,GAAGyB,IAAI,CAACiB,EAAE,GAAI,GAAG,CAAC,CAAC;QAC/C,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACF,GAAG,CAAC;QACzB,MAAMG,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACH,GAAG,CAAC;QAEzB,MAAM+D,aAAa,GAAGF,KAAI,CAAC9G,IAAI,CAACC,MAAM,CAACN,GAAG,CAAEO,KAAK,IAAI;UACnD;UACA,MAAMiG,EAAE,GAAGjG,KAAK,CAACL,CAAC,GAAG4D,OAAO;UAC5B,MAAM2C,EAAE,GAAGlG,KAAK,CAACJ,CAAC,GAAG4D,OAAO;UAE5B;UACA,MAAMuD,QAAQ,GAAGxD,OAAO,IAAI0C,EAAE,GAAGhD,GAAG,GAAGiD,EAAE,GAAGhD,GAAG,CAAC;UAChD,MAAM8D,QAAQ,GAAGxD,OAAO,IAAIyC,EAAE,GAAG/C,GAAG,GAAGgD,EAAE,GAAGjD,GAAG,CAAC;UAEhD,OAAO;YACLtD,CAAC,EAAEoC,IAAI,CAACkF,KAAK,CAACF,QAAQ,CAAC;YACvBnH,CAAC,EAAEmC,IAAI,CAACkF,KAAK,CAACD,QAAQ;WACvB;QACH,CAAC,CAAC;QAEF,OAAO;UAAEjH,MAAM,EAAE+G;QAAa,CAAE;;MAGlC;MACA,OAAO;QACL/G,MAAM,EAAE6G,KAAI,CAAC9G,IAAI,CAACC,MAAM,CAACN,GAAG,CAAEO,KAAK,KAAM;UACvCL,CAAC,EAAEoC,IAAI,CAACkF,KAAK,CAACjH,KAAK,CAACL,CAAC,CAAC;UACtBC,CAAC,EAAEmC,IAAI,CAACkF,KAAK,CAACjH,KAAK,CAACJ,CAAC;SACtB,CAAC;OACH;IAAC;EACJ;EAIQiC,YAAYA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC/B,IAAI,CAACC,MAAM,EAAE;IAEvB,IAAI,CAACD,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM,CAACN,GAAG,CAAEO,KAAK,IAC5C,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC,CAC7B;EACH;EAEA;EACAS,iBAAiBA,CAACoC,KAAa;IAC7B,IAAI,CAAC,IAAI,CAAC/C,IAAI,CAACC,MAAM,IAAI,IAAI,CAACD,IAAI,CAACC,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;MACtDmH,OAAO,CAACsC,KAAK,CAAC,mCAAmC,CAAC;MAClD;;IAGF,MAAM3D,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;IACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;IACtC,MAAMiE,GAAG,GAAIF,KAAK,GAAGd,IAAI,CAACiB,EAAE,GAAI,GAAG;IACnC,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACF,GAAG,CAAC;IACzB,MAAMG,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACH,GAAG,CAAC;IAEzB;IACA,IAAI,CAACjD,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM,CAACN,GAAG,CAAEO,KAAK,IAAI;MAChD,IAAI,OAAOA,KAAK,CAACL,CAAC,KAAK,WAAW,IAAI,OAAOK,KAAK,CAACJ,CAAC,KAAK,WAAW,EAAE;QACpEgF,OAAO,CAACsC,KAAK,CAAC,4BAA4B,EAAElH,KAAK,CAAC;QAClD,OAAOA,KAAK;;MAGd;MACA,MAAMiG,EAAE,GAAGjG,KAAK,CAACL,CAAC,GAAG4D,OAAO;MAC5B,MAAM2C,EAAE,GAAGlG,KAAK,CAACJ,CAAC,GAAG4D,OAAO;MAE5B;MACA,MAAM2D,IAAI,GAAG5D,OAAO,IAAI0C,EAAE,GAAGhD,GAAG,GAAGiD,EAAE,GAAGhD,GAAG,CAAC;MAC5C,MAAMkE,IAAI,GAAG5D,OAAO,IAAIyC,EAAE,GAAG/C,GAAG,GAAGgD,EAAE,GAAGjD,GAAG,CAAC;MAE5C;MACA,OAAO,IAAI,CAAC/C,gBAAgB,CAAC;QAC3BP,CAAC,EAAEwH,IAAI;QACPvH,CAAC,EAAEwH;OACJ,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACjH,kBAAkB,EAAE;IACzB,IAAI,CAAChB,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEQoB,mBAAmBA,CAACT,MAAqB;IAC/C,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;MAClCmH,OAAO,CAACsC,KAAK,CAAC,2BAA2B,EAAEnH,MAAM,CAAC;MAClD,OAAO,KAAK;;IAGd,OAAOA,MAAM,CAACyG,KAAK,CAAC,CAACxG,KAAK,EAAEtE,KAAK,KAAI;MACnC,IAAI,OAAOsE,KAAK,CAACL,CAAC,KAAK,WAAW,IAAI,OAAOK,KAAK,CAACJ,CAAC,KAAK,WAAW,EAAE;QACpEgF,OAAO,CAACsC,KAAK,CAAC,gCAAgCxL,KAAK,GAAG,EAAEsE,KAAK,CAAC;QAC9D,OAAO,KAAK;;MAEd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEAqH,qBAAqBA,CAAA;IACnB,OAAO,EAAE,CAAC,CAAC;EACb;EAEA7C,uBAAuBA,CAACxE,KAAkB;IACxC,IAAI,IAAI,CAAC9D,WAAW,KAAK,IAAI,EAAE;MAC7B,MAAMA,WAAW,GAAG,IAAI,CAAC4D,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC7D,WAAW,CAAC;MAEtD;MACA,MAAMqH,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;MACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;MACtC,MAAMwI,YAAY,GAAG,IAAI,CAACC,WAAW,CACnCrL,WAAW,EACX;QAAEyD,CAAC,EAAE4D,OAAO;QAAE3D,CAAC,EAAE4D;MAAO,CAAE,EAC1B,IAAI,CAACpD,QAAQ,CACd;MAED,MAAM0B,MAAM,GAAG,IAAI,CAACN,eAAe,GAAG,GAAG;MAEzC;MACA,IAAIgG,UAAU,GAAGF,YAAY,CAAC3H,CAAC;MAC/B,IAAI8H,UAAU,GAAGH,YAAY,CAAC1H,CAAC;MAE/B,IAAI0H,YAAY,CAAC1H,CAAC,GAAG,IAAI,CAACd,aAAa,GAAG,CAAC,EAAE;QAC3C2I,UAAU,GAAGH,YAAY,CAAC1H,CAAC,GAAGkC,MAAM;OACrC,MAAM;QACL2F,UAAU,GAAGH,YAAY,CAAC1H,CAAC,GAAGkC,MAAM;;MAGtC,IAAIwF,YAAY,CAAC3H,CAAC,GAAG,IAAI,CAACf,YAAY,GAAG,CAAC,EAAE;QAC1C4I,UAAU,GAAGF,YAAY,CAAC3H,CAAC,GAAGmC,MAAM;OACrC,MAAM;QACL0F,UAAU,GAAGF,YAAY,CAAC3H,CAAC,GAAGmC,MAAM;;MAGtC,IAAI,CAACP,iBAAiB,GAAG;QACvB5B,CAAC,EAAE6H,UAAU;QACb5H,CAAC,EAAE6H;OACJ;MAED,IAAI,CAACtI,KAAK,CAACC,aAAa,EAAE;;EAE9B;EAEA;EACQmI,WAAWA,CACjBvH,KAAkB,EAClB0H,MAAmB,EACnBC,YAAoB;IAEpB,MAAMC,YAAY,GAAID,YAAY,GAAG5F,IAAI,CAACiB,EAAE,GAAI,GAAG;IACnD,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAAC2E,YAAY,CAAC;IAClC,MAAM1E,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAAC0E,YAAY,CAAC;IAElC,MAAM3B,EAAE,GAAGjG,KAAK,CAACL,CAAC,GAAG+H,MAAM,CAAC/H,CAAC;IAC7B,MAAMuG,EAAE,GAAGlG,KAAK,CAACJ,CAAC,GAAG8H,MAAM,CAAC9H,CAAC;IAE7B,OAAO;MACLD,CAAC,EAAE+H,MAAM,CAAC/H,CAAC,IAAIsG,EAAE,GAAGhD,GAAG,GAAGiD,EAAE,GAAGhD,GAAG,CAAC;MACnCtD,CAAC,EAAE8H,MAAM,CAAC9H,CAAC,IAAIqG,EAAE,GAAG/C,GAAG,GAAGgD,EAAE,GAAGjD,GAAG;KACnC;EACH;EAEA;EACA4E,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC3L,WAAW,KAAK,IAAI,EAAE,OAAO,EAAE;IAExC,MAAMA,WAAW,GAAG,IAAI,CAAC4D,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC7D,WAAW,CAAC;IACtD,MAAMmH,KAAK,GAAG,IAAI,CAAC5B,UAAU;IAE7B;IACA,MAAM8B,OAAO,GAAG,IAAI,CAAC3E,YAAY,GAAG,CAAC;IACrC,MAAM4E,OAAO,GAAG,IAAI,CAAC1E,aAAa,GAAG,CAAC;IAEtC;IACA,MAAMmH,EAAE,GAAG,IAAI,CAAC1E,iBAAiB,CAAC5B,CAAC,GAAGzD,WAAW,CAACyD,CAAC,GAAG0D,KAAK;IAC3D,MAAM6C,EAAE,GAAG,IAAI,CAAC3E,iBAAiB,CAAC3B,CAAC,GAAG1D,WAAW,CAAC0D,CAAC,GAAGyD,KAAK;IAE3D;IACA,OAAO,aAAa4C,EAAE,KAAKC,EAAE,eAAe3C,OAAO,GAAGF,KAAK,KAAKG,OAAO,GAAGH,KAAK,YAAY,IAAI,CAACjD,QAAQ,eAAe,CAACmD,OAAO,GAAGF,KAAK,KAAK,CAACG,OAAO,GAAGH,KAAK,WAAWA,KAAK,GAAG;EACjL;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEAyE,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAAC5L,WAAW,KAAK,IAAI,EAAE,OAAO;MAAE6L,QAAQ,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE;IAE1E,MAAMhI,KAAK,GAAG,IAAI,CAACF,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC7D,WAAW,CAAC;IAChD,MAAM+L,UAAU,GAAG,EAAE;IAErB;IACA,MAAMF,QAAQ,GAAG;MACfG,EAAE,EAAElI,KAAK,CAACL,CAAC;MACXwI,EAAE,EAAEnI,KAAK,CAACJ,CAAC,GAAGqI,UAAU;MACxBG,EAAE,EAAEpI,KAAK,CAACL,CAAC;MACX0I,EAAE,EAAErI,KAAK,CAACJ,CAAC,GAAGqI;KACf;IAED,MAAMD,UAAU,GAAG;MACjBE,EAAE,EAAElI,KAAK,CAACL,CAAC,GAAGsI,UAAU;MACxBE,EAAE,EAAEnI,KAAK,CAACJ,CAAC;MACXwI,EAAE,EAAEpI,KAAK,CAACL,CAAC,GAAGsI,UAAU;MACxBI,EAAE,EAAErI,KAAK,CAACJ;KACX;IAED;IACA,OAAO;MACLmI,QAAQ;MACRC;KACD;EACH;EAEQ7H,kBAAkBA,CAAA;IACxB,IAAI,CAACuB,SAAS,GAAG,EAAE;IACnB,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMK,EAAE,GAAG,IAAI,CAACjG,IAAI,CAACC,MAAM,CAAC2F,CAAC,CAAC;MAC9B,MAAMM,EAAE,GAAG,IAAI,CAAClG,IAAI,CAACC,MAAM,CAAC,CAAC2F,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MACxC,MAAMH,QAAQ,GAAG;QACf5F,CAAC,EAAE,CAACoG,EAAE,CAACpG,CAAC,GAAGqG,EAAE,CAACrG,CAAC,IAAI,CAAC;QACpBC,CAAC,EAAE,CAACmG,EAAE,CAACnG,CAAC,GAAGoG,EAAE,CAACpG,CAAC,IAAI;OACpB;MACD,IAAI,CAAC8B,SAAS,CAAC7B,IAAI,CAAC0F,QAAQ,CAAC;;EAEjC;EAEAjJ,qBAAqBA,CAACY,KAAmB,EAAExB,KAAa;IACtDwB,KAAK,CAACE,cAAc,EAAE;IACtBF,KAAK,CAACG,eAAe,EAAE;IACvB,IAAI,CAACF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACZ,cAAc,GAAGb,KAAK;IAC3B,IAAI,CAAC4F,aAAa,GAAG,KAAK;IAE1B;IACA,MAAMgH,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC7M,KAAK,CAAC;IAC1C,MAAM8M,eAAe,GAAGzG,IAAI,CAACe,GAAG,CAACwF,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;IAEnD;IACA,IAAIE,eAAe,GAAG,EAAE,IAAIA,eAAe,GAAG,GAAG,EAAE;MACjD;MACA,IAAI,CAAC7G,sBAAsB,GAAG,UAAU;KACzC,MAAM;MACL;MACA,IAAI,CAACA,sBAAsB,GAAG,YAAY;;IAG5C,MAAMrC,MAAM,GAAG,IAAI,CAACiF,iBAAiB,CAACrH,KAAK,CAAC;IAC5C,IAAI,CAACsH,uBAAuB,CAAClF,MAAM,CAAC;IACpC,IAAI,CAAC0B,iBAAiB,GAAG,IAAI,CAACd,gBAAgB,CAACZ,MAAM,CAAC;IACtD,IAAI,CAACH,KAAK,CAACC,aAAa,EAAE;EAC5B;EAEQoG,8BAA8BA,CAACiD,aAAqB,EAAEC,MAAmB;IAC/E,MAAMC,SAAS,GAAGF,aAAa;IAC/B,MAAMG,SAAS,GAAG,CAACH,aAAa,GAAG,CAAC,IAAI,CAAC;IACzC,MAAMI,SAAS,GAAG,IAAI,CAAC/I,IAAI,CAACC,MAAM,CAAC4I,SAAS,CAAC;IAC7C,MAAMG,SAAS,GAAG,IAAI,CAAChJ,IAAI,CAACC,MAAM,CAAC6I,SAAS,CAAC;IAE7C,IAAI,IAAI,CAACjH,sBAAsB,KAAK,YAAY,EAAE;MAChDkH,SAAS,CAAClJ,CAAC,GAAG+I,MAAM,CAAC/I,CAAC;MACtBmJ,SAAS,CAACnJ,CAAC,GAAG+I,MAAM,CAAC/I,CAAC;KACvB,MAAM,IAAI,IAAI,CAACgC,sBAAsB,KAAK,UAAU,EAAE;MACrDkH,SAAS,CAACjJ,CAAC,GAAG8I,MAAM,CAAC9I,CAAC;MACtBkJ,SAAS,CAAClJ,CAAC,GAAG8I,MAAM,CAAC9I,CAAC;;EAE1B;EAEQ2I,YAAYA,CAAC7M,KAAa;IAChC,MAAMqK,EAAE,GAAG,IAAI,CAACjG,IAAI,CAACC,MAAM,CAACrE,KAAK,CAAC;IAClC,MAAMsK,EAAE,GAAG,IAAI,CAAClG,IAAI,CAACC,MAAM,CAAC,CAACrE,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAMuK,EAAE,GAAGD,EAAE,CAACrG,CAAC,GAAGoG,EAAE,CAACpG,CAAC;IACtB,MAAMuG,EAAE,GAAGF,EAAE,CAACpG,CAAC,GAAGmG,EAAE,CAACnG,CAAC;IACtB,OAAOmC,IAAI,CAACgH,KAAK,CAAC7C,EAAE,EAAED,EAAE,CAAC,IAAI,GAAG,GAAGlE,IAAI,CAACiB,EAAE,CAAC,CAAC,CAAC;EAC/C;EAGAgG,gBAAgBA,CAACtN,KAAa;IAC5B;IACA,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,EAAE;MAC9B,OAAO,GAAG,CAAC,CAAC;;IAEd;IACA,OAAO,EAAE,CAAC,CAAC;EACb;EAEAuN,iBAAiBA,CAACvN,KAAa;IAC7B;IACA,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,EAAE;MAC9B,OAAO,EAAE,CAAC,CAAC;;IAEb;IACA,OAAO,GAAG,CAAC,CAAC;EACd;EAEAwN,YAAYA,CAAC3D,QAAqB,EAAE7J,KAAa;IAC/C,MAAM;MAAEkF;IAAK,CAAE,GAAG,IAAI,CAACuI,qBAAqB,CAACzN,KAAK,CAAC;IACnD,OAAO6J,QAAQ,CAAC5F,CAAC,GAAGiB,KAAK,GAAG,CAAC;EAC/B;EAEAwI,YAAYA,CAAC7D,QAAqB,EAAE7J,KAAa;IAC/C,MAAM;MAAEmF;IAAM,CAAE,GAAG,IAAI,CAACsI,qBAAqB,CAACzN,KAAK,CAAC;IACpD,OAAO6J,QAAQ,CAAC3F,CAAC,GAAGiB,MAAM,GAAG,CAAC;EAChC;EAEAwI,iBAAiBA,CAAC3N,KAAa;IAC7B,OAAO,EAAE,CAAC,CAAC;EACb;EAEAyN,qBAAqBA,CAACzN,KAAa;IACjC;IACA,MAAMmH,KAAK,GAAGd,IAAI,CAACe,GAAG,CAAC,IAAI,CAACyF,YAAY,CAAC7M,KAAK,CAAC,GAAG,GAAG,CAAC;IAEtD;IACA;IACA,IAAImH,KAAK,GAAG,EAAE,IAAIA,KAAK,GAAG,GAAG,EAAE;MAC7B;MACA,OAAO;QAAEjC,KAAK,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAE,CAAE;KAClC,MAAM;MACL;MACA,OAAO;QAAED,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAE;;EAErC;;+BAn5BW/D,2BAA2B;;mBAA3BA,4BAA2B,EAAA7B,+DAAA,CAAAA,4DAAA;AAAA;;QAA3B6B,4BAA2B;EAAA0M,SAAA;EAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;MAA3B1O,wDAAA,uBAAA4O,yDAAA;QAAA,OAAAD,GAAA,CAAA7M,YAAA,EAAc;MAAA,EAAa,2BAAA+M,6DAAA;QAAA,OAA3BF,GAAA,CAAA7M,YAAA,EAAc;MAAA,EAAa,yBAAAgN,2DAAAzO,MAAA;QAAA,OAA3BsO,GAAA,CAAA3M,aAAA,CAAA3B,MAAA,CAAqB;MAAA,EAAM,wBAAA0O,0DAAA1O,MAAA;QAAA,OAA3BsO,GAAA,CAAArM,YAAA,CAAAjC,MAAA,CAAoB;MAAA,EAAO,uBAAA2O,yDAAA3O,MAAA;QAAA,OAA3BsO,GAAA,CAAAvL,WAAA,CAAA/C,MAAA,CAAmB;MAAA,EAAQ,sBAAA4O,wDAAA;QAAA,OAA3BN,GAAA,CAAAtL,UAAA,EAAY;MAAA,EAAe,uBAAA6L,yDAAA7O,MAAA;QAAA,OAA3BsO,GAAA,CAAArL,mBAAA,CAAAjD,MAAA,CAA2B;MAAA,UAAAL,+DAAA;;;;;;;;;;;;;;;;;;;MC1BxCA,4DAAA,aAA6E;;MAC3EA,4DAAA,gBAQC;MAFCA,wDAHA,uBAAAoP,8DAAA/O,MAAA;QAAAL,2DAAA,CAAAqP,GAAA;QAAA,OAAArP,yDAAA,CAAa2O,GAAA,CAAA9E,eAAA,CAAAxJ,MAAA,CAAuB;MAAA,EAAC,sBAAAiP,6DAAA;QAAAtP,2DAAA,CAAAqP,GAAA;QAAA,OAAArP,yDAAA,CACzB2O,GAAA,CAAA5M,YAAA,EAAc;MAAA,EAAC,yBAAAwN,gEAAA;QAAAvP,2DAAA,CAAAqP,GAAA;QAAA,OAAArP,yDAAA,CACZ2O,GAAA,CAAA5M,YAAA,EAAc;MAAA,EAAC,wBAAAyN,+DAAA;QAAAxP,2DAAA,CAAAqP,GAAA;QAAA,OAAArP,yDAAA,CAChB2O,GAAA,CAAA5M,YAAA,EAAc;MAAA,EAAC;MAI7B/B,4DAAA,QAA6C;MAC3CA,wDAAA,IAAAyP,iDAAA,mBAOE;MACJzP,0DAAA,EAAI;MAGJA,uDAAA,cAIE;MAKAA,4DAFF,QAAyC,iBAMrC;MADAA,wDAAA,wBAAA0P,mEAAArP,MAAA;QAAAL,2DAAA,CAAAqP,GAAA;QAAA,OAAArP,yDAAA,CAAc2O,GAAA,CAAA5E,mBAAA,CAAA1J,MAAA,CAA2B;MAAA,EAAC;MAH5CL,0DAAA,EAIE;MA2BFA,wDAxBA,IAAA2P,6CAAA,gBAAqD,IAAAC,6CAAA,eAwBA;MAcvD5P,0DAAA,EAAI;MAGJA,wDAAA,KAAA6P,8CAAA,gBAA2C;MA8D/C7P,0DADE,EAAM,EACF;;;MA9I+CA,yDAAtB,UAAA2O,GAAA,CAAAhJ,KAAA,CAAqB,WAAAgJ,GAAA,CAAA/I,MAAA,CAAwB;MAGxE5F,uDAAA,EAA6B;;MAQ1BA,uDAAA,GAAyC;;MAEvCA,uDAAA,EAAS;MAATA,wDAAA,SAAA2O,GAAA,CAAA/M,GAAA,CAAS;MAWZ5B,uDAAA,EAA2B;;MAM1BA,uDAAA,EAAqC;;MAGpCA,uDAAA,EAAkC;;MAMfA,uDAAA,EAAiB;MAAjBA,wDAAA,YAAA2O,GAAA,CAAA9J,IAAA,kBAAA8J,GAAA,CAAA9J,IAAA,CAAAC,MAAA,CAAiB;MAwBd9E,uDAAA,EAAc;MAAdA,wDAAA,YAAA2O,GAAA,CAAAlI,SAAA,CAAc;MAiBpCzG,uDAAA,EAAmB;MAAnBA,wDAAA,SAAA2O,GAAA,CAAAtI,aAAA,CAAmB;;;;;;;;;;;;;;;;;;;;;;;;AC/E4B;AAET;;;AAE9C,MAAM4J,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,uDAAWA;CACvB,CACF;AAMK,MAAOI,wBAAwB;4BAAxBA,wBAAwB;;mBAAxBA,yBAAwB;AAAA;;QAAxBA;AAAwB;;YAHzBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,wBAAwB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFzBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEwB;AACd,CAAC;AACV;AACkE;;AAa1G,MAAOc,iBAAiB;qBAAjBA,iBAAiB;;mBAAjBA,kBAAiB;AAAA;;QAAjBA;AAAiB;;YAT1BJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,8EAAwB,EACxBQ,+DAAY;AAAA;;sHAKHC,iBAAiB;IAAAC,YAAA,GAFbd,uDAAW,EAAEnO,wHAA2B;IAAAyO,OAAA,GAPrDG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,8EAAwB,EACxBQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfwI;AAEpE;AAC/B,CAAE;AAEI;AAGA;;;;;;;;;;;;;;;;AASrD,MAAOZ,WAAW;EAqBtBtK,YACU0L,KAAqB,EACrBC,QAAkB,EAClBC,iBAAoC,EACpCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,QAAkB,EAClBC,QAAmB;IAPnB,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,QAAQ,GAARA,QAAQ;IA5BlB,KAAApO,QAAQ,GAAiB,IAAI;IAC7B,KAAAqO,WAAW,GAAkB,EAAE;IAC/B,KAAAC,IAAI,GAAiB,IAAI;IACzB,KAAAC,cAAc,GAAY,KAAK;IAE/B,KAAAC,aAAa,GAAW,CAAC;IAGzB,KAAAC,OAAO,GAAGjB,sDAAM,CAACC,0DAAa,CAAC;IAC/B,KAAAiB,UAAU,GAAGlB,sDAAM,CAACE,6DAAU,CAAC,CAAC,CAAE;IAClC,KAAAiB,aAAa,GAAGnB,sDAAM,CAACG,mEAAa,CAAC;IAErC,KAAAiB,QAAQ,GAAG,CAAC;IACZ,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,IAAI,CAAC,CAAC;IAGZ,KAAA1O,YAAY,GAAG,CAAC;IAChB,KAAAE,aAAa,GAAG,CAAC;IAYvB;EACF;EAEAyO,eAAeA,CAAA;IAEb;IACA,IAAI,CAAC3O,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,aAAa,GAAG,IAAI;IAEzB,MAAM0O,MAAM,GAAG,IAAI,CAAClB,QAAQ,CAACmB,QAAQ,EAA6F;IAClI,IAAG,CAACD,MAAM,CAAChP,QAAQ,IAAI,CAACgP,MAAM,CAACX,WAAW,IAAI,CAACW,MAAM,CAACV,IAAI,EAAE;MAC1D,IAAI,CAACG,OAAO,CAACS,YAAY,CAAC,UAAU,CAAC;MACrC;;IAEF,IAAI,CAACC,cAAc,CAACH,MAAM,CAAChP,QAAQ,CAAC,CAACoP,IAAI,CAAEC,OAAO,IAAI;MACpD,IAAI,CAACA,OAAO,EAAE;QACZ,IAAI,CAACZ,OAAO,CAACS,YAAY,CAAC,UAAU,CAAC;QACrC;;MAEF,IAAI,CAAClP,QAAQ,GAAGgP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhP,QAAQ;MAChC,IAAI,CAACqO,WAAW,GAAGW,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEX,WAAW;MACtC,IAAI,CAACC,IAAI,GAAGU,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEV,IAAI;MACxB,IAAI,CAACC,cAAc,GAAGS,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAET,cAAc;MAE7Ce,UAAU,CAAC,MAAK;QAEd,IAAI,CAACC,WAAW,CAAEP,MAAM,CAAChP,QAAQ,EAAEgP,MAAM,CAACX,WAAW,CAAC;MACxD,CAAC,EAAE,GAAG,CAAC;MAENjI,OAAO,CAACC,GAAG,CAAC,IAAI,CAACyH,QAAQ,CAACmB,QAAQ,EAAE,CAAC;IACvC,CAAC,CAAC;IACF7I,OAAO,CAACC,GAAG,CAAC,IAAI,CAACyH,QAAQ,CAACmB,QAAQ,EAAE,CAAC;IAErC,IAAID,MAAM,aAANA,MAAM,eAANA,MAAM,CAAET,cAAc,EAAE;MAC1B,IAAI,CAACiB,iBAAiB,EAAE;;IAI1B;IACA,IAAI,CAACtB,cAAc,CAACuB,gBAAgB,EAAE,CAACC,SAAS,CAAEC,SAAkB,IAAI;MACtE,IAAI,CAACb,WAAW,GAAGa,SAAS;IAC9B,CAAC,CAAC;IAEF,IAAI,IAAI,CAACxB,QAAQ,CAACyB,EAAE,CAAC,SAAS,CAAC,EAAE;MAC/B,IAAI,CAACxB,QAAQ,CAACyB,QAAQ,CAACzQ,QAAQ,CAAC0Q,IAAI,EAAE,kBAAkB,CAAC;KAC1D,MAAM;MACL,IAAI,CAAC1B,QAAQ,CAACyB,QAAQ,CAACzQ,QAAQ,CAAC0Q,IAAI,EAAE,gBAAgB,CAAC;;IAGzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;EACF;EAEMN,iBAAiBA,CAAA;IAAA,IAAApH,KAAA;IAAA,OAAAC,6OAAA;MACrB,MAAM0H,KAAK,SAAS3H,KAAI,CAAC4F,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE,mEAAmE;QAC5EC,OAAO,EAAE,CAAC,IAAI;OACf,CAAC;MAEF,MAAMJ,KAAK,CAACK,OAAO,EAAE;IAAC;EACxB;EAEA;EACQC,sBAAsBA,CAACC,EAAe,EAAEC,QAAgB;IAAA,IAAAC,KAAA,EAAAC,KAAA;IAC9D;IACA,MAAMtP,CAAC,IAAAqP,KAAA,GAAGF,EAAE,CAACnP,CAAC,cAAAqP,KAAA,cAAAA,KAAA,GAAI,CAAC,CAAC,CAAE;IACtB,MAAMpP,CAAC,IAAAqP,KAAA,GAAGH,EAAE,CAAClP,CAAC,cAAAqP,KAAA,cAAAA,KAAA,GAAI,CAAC,CAAC,CAAE;IAEtBrK,OAAO,CAACC,GAAG,CAAC;MACVlF,CAAC,EAAEC,CAAC;MACJA,CAAC,EAAEmP,QAAQ,GAAGpP;KACf,CAAC;IACF;IACA,OAAO;MACLA,CAAC,EAAEC,CAAC;MACJA,CAAC,EAAEmP,QAAQ,GAAGpP;KACf;EACH;EAEF;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGAuP,WAAWA,CAACrM,KAAa;IACvB;IACA,MAAMsM,MAAM,GAAGvR,QAAQ,CAACwR,aAAa,CAAC,sBAAsB,CAAC;IAC7DD,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEnR,SAAS,CAACqR,GAAG,CAAC,UAAU,CAAC;IAEjC;IACAvB,UAAU,CAAC,MAAK;MACZqB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEnR,SAAS,CAACsR,MAAM,CAAC,UAAU,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,CAACtC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa,GAAGnK,KAAK,IAAI,GAAG;IACvD,IAAI,IAAI,CAACmK,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,aAAa,IAAI,GAAG;;IAG7B;IACA,IAAI,IAAI,CAACuC,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACnP,QAAQ,GAAG,IAAI,CAAC4M,aAAa;;EAEzD;EAKE;;;;;;;;EAQMwC,aAAaA,CAACV,EAAe,EAAEjM,KAAa;IAAA,IAAA4M,MAAA,EAAAC,MAAA;IAClD,MAAM/P,CAAC,IAAA8P,MAAA,GAAGX,EAAE,CAACnP,CAAC,cAAA8P,MAAA,cAAAA,MAAA,GAAI,CAAC,CAAC,CAAC;IACrB,MAAM7P,CAAC,IAAA8P,MAAA,GAAGZ,EAAE,CAAClP,CAAC,cAAA8P,MAAA,cAAAA,MAAA,GAAI,CAAC;IAEnB;IACA,IAAItH,EAAE,GAAGzI,CAAC;IACV,IAAI0I,EAAE,GAAGzI,CAAC;IAEV;IACA,MAAM+P,CAAC,GAAG,IAAI,CAAC/Q,YAAY;IAC3B,MAAMgR,CAAC,GAAG,IAAI,CAAC9Q,aAAa;IAE5B;IACA,QAAQ+D,KAAK;MACX,KAAK,EAAE;QACL;QACA;QACAuF,EAAE,GAAGxI,CAAC;QACNyI,EAAE,GAAGsH,CAAC,GAAGhQ,CAAC;QACV;QACA;MACF,KAAK,GAAG;QACN;QACA;QACAyI,EAAE,GAAGuH,CAAC,GAAGhQ,CAAC;QACV0I,EAAE,GAAGuH,CAAC,GAAGhQ,CAAC;QACV;QACA;MACF,KAAK,GAAG;QACN;QACA;QACAwI,EAAE,GAAGwH,CAAC,GAAGhQ,CAAC;QACVyI,EAAE,GAAG1I,CAAC;QACN;QACA;MACF;QACE;QACAiF,OAAO,CAACiL,IAAI,CAAC,iDAAiDhN,KAAK,EAAE,CAAC;QACtE;;IAGJ,OAAO;MAAElD,CAAC,EAAEyI,EAAE;MAAExI,CAAC,EAAEyI;IAAE,CAAE;EACzB;EAIA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEM0F,WAAWA,CAACvP,QAAA,GAAmB,EAAE,EAAEqO,WAAA,GAA6B,EAAE;IAAA,IAAAiD,MAAA;IAAA,OAAAjJ,6OAAA;MACtE,IAAI,CAACrI,QAAQ,IAAI,CAACqO,WAAW,CAACpP,MAAM,EAAE;MAEtCmH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAErG,QAAQ;QAAEqO;MAAW,CAAE,CAAC;MAE/D;MACA,MAAMkD,oBAAoB,GAAGD,MAAI,CAACE,kBAAkB,CAACnD,WAAW,CAAC;MACjEjI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkL,oBAAoB,CAAC;MAE3D;MACA,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIpC,UAAU,CAACoC,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,IAAIJ,MAAI,CAACP,gBAAgB,EAAE;QACzB;QACAO,MAAI,CAACP,gBAAgB,CAAC/Q,QAAQ,GAAGA,QAAQ;QAEzC;QACA,MAAM,IAAIyR,OAAO,CAACC,OAAO,IAAIpC,UAAU,CAACoC,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtD;QACAJ,MAAI,CAACP,gBAAgB,CAACtQ,kBAAkB,GAAG8Q,oBAAoB;QAE/D;QACAD,MAAI,CAACP,gBAAgB,CAACnP,QAAQ,GAAG0P,MAAI,CAAC9C,aAAa;OACpD,MAAM;QACLpI,OAAO,CAACsC,KAAK,CAAC,6BAA6B,CAAC;;IAC7C;EACH;EAIA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEMiJ,qBAAqBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvJ,6OAAA;MACzB,IAAI,CAACuJ,MAAI,CAACb,gBAAgB,EAAE;QAC1B3K,OAAO,CAACsC,KAAK,CAAC,6BAA6B,CAAC;QAC5C;;MAGFkJ,MAAI,CAAC/C,SAAS,GAAG,IAAI;MACrB+C,MAAI,CAACC,KAAK,GAAGD,MAAI,CAAClD,UAAU,CAACoD,aAAa,EAAE;MAE5C,IAAI;QACF,MAAMxQ,IAAI,SAASsQ,MAAI,CAACb,gBAAgB,CAAC5I,OAAO,EAAE;QAClD,MAAMkG,WAAW,GAAG/M,IAAI,CAACC,MAAM;QAE/B6E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuL,MAAI,CAACvD,WAAW,CAAC;QACtDjI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgI,WAAW,CAAC;QAEhD,MAAM0D,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,QAAQ;QACvE,MAAMC,OAAO,GAAG;UACd7D,WAAW;UACX8D,SAAS,EAAEP,MAAI,CAACtD,IAAI;UACpByD,UAAU,EAAEA,UAAU,KAAK,OAAO,IAAIA,UAAU,KAAK,WAAW,GAAG,QAAQ,GAAGA,UAAU;UACxFnQ,QAAQ,EAAEgQ,MAAI,CAACpD;SAChB;QAEDpI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE6L,OAAO,CAAC;QAExC;QACA,MAAME,YAAY,GAAG,GAAGxE,qEAAW,CAACyE,YAAY,IAAIT,MAAI,CAACC,KAAK,EAAE;QAChED,MAAI,CAAC3D,gBAAgB,CAACqE,OAAO,CAACF,YAAY,EAAER,MAAI,CAACC,KAAK,CAAC;QAEvDD,MAAI,CAAC3D,gBAAgB,CAACsE,SAAS,CAACX,MAAI,CAACC,KAAK,CAAC,CAACnC,SAAS,CAACQ,OAAO,IAAG;UAC9D,IAAIA,OAAO,CAACtB,QAAQ,KAAK4D,SAAS,EAAE;YAClCZ,MAAI,CAAChD,QAAQ,GAAGsB,OAAO,CAACtB,QAAQ;;QAEpC,CAAC,CAAC;QAEF;QACAgD,MAAI,CAAClD,UAAU,CAAC+D,gBAAgB,CAACP,OAAO,EAAEN,MAAI,CAACC,KAAK,CAAC,CAACnC,SAAS,CAC7DgD,QAAQ,IAAG;UACTd,MAAI,CAAC/C,SAAS,GAAG,KAAK;UACtB+C,MAAI,CAACjD,aAAa,CAACgE,OAAO,CAACD,QAAQ,CAAC;UACpCd,MAAI,CAACnD,OAAO,CAACmE,eAAe,CAAC,cAAc,EAAE;YAAEC,KAAK,EAAEH;UAAQ,CAAE,CAAC;UACjEd,MAAI,CAAC3D,gBAAgB,CAAC6E,KAAK,CAAClB,MAAI,CAACC,KAAM,CAAC;QAC1C,CAAC,EACDnJ,KAAK,IAAG;UACNkJ,MAAI,CAAC/C,SAAS,GAAG,KAAK;UACtB,MAAMkE,YAAY,GAAG;;;;;;;;SAQpB;UACDnB,MAAI,CAAClD,UAAU,CAACsE,cAAc,CAACD,YAAY,CAAC;UAC5CnB,MAAI,CAAC3D,gBAAgB,CAAC6E,KAAK,CAAClB,MAAI,CAACC,KAAM,CAAC;QAC1C,CAAC,CACF;OACF,CAAC,OAAOnJ,KAAK,EAAE;QACdkJ,MAAI,CAAC/C,SAAS,GAAG,KAAK;QACtBzI,OAAO,CAACsC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD;EACH;EAIE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA8I,kBAAkBA,CAACyB,GAAU;IAC3B,OAAOA,GAAG,CAAChS,GAAG,CAACiS,IAAI,IAAG;MACpB;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvB,OAAO;UACL/R,CAAC,EAAEkS,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;UACjClS,CAAC,EAAEiS,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC;SACjC;OACF,MAAM;QACL,OAAO;UACLnS,CAAC,EAAEkS,UAAU,CAAC,CAACH,IAAI,CAAC/R,CAAC,IAAI,CAAC,EAAEmS,OAAO,CAAC,CAAC,CAAC,CAAC;UACvClS,CAAC,EAAEiS,UAAU,CAAC,CAACH,IAAI,CAAC9R,CAAC,IAAI,CAAC,EAAEkS,OAAO,CAAC,CAAC,CAAC;SACvC;;IAEL,CAAC,CAAC;EACJ;EAEMnE,cAAcA,CAACoE,OAAe;IAAA,OAAAlL,6OAAA;MAClC,IAAI;QACF,MAAMqK,QAAQ,SAASc,KAAK,CAACD,OAAO,CAAC;QACrC,OAAOb,QAAQ,CAACe,EAAE,CAAC,CAAC;OACrB,CAAC,OAAO/K,KAAK,EAAE;QACd,OAAO,KAAK,CAAC,CAAC;;IACf;EACH;EAEMgL,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtL,6OAAA;MAClB,MAAMuL,OAAO,SAASD,MAAI,CAAC5F,iBAAiB,CAACiC,MAAM,CAAC;QAClDE,OAAO,EAAE,eAAe;QACxB2D,OAAO,EAAE;QACT;OACD,CAAC;MACF,MAAMD,OAAO,CAACxD,OAAO,EAAE;MACvB,OAAOwD,OAAO;IAAC;EACjB;EAEME,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1L,6OAAA;MACtBjC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAM0J,KAAK,SAASgE,MAAI,CAAC/F,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE,yDAAyD;QAClEC,OAAO,EAAE,CACP;UACE6D,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZ/N,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACE2N,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZJ,MAAI,CAACpF,aAAa,CAACyF,aAAa,EAAE;YAClC;YACAL,MAAI,CAACtF,OAAO,CAAC4F,YAAY,CAAC,UAAU,CAAC;UACvC;SACD;OAEJ,CAAC;MAEF,MAAMtE,KAAK,CAACK,OAAO,EAAE;IAAC;EACxB;EAEMkE,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlM,6OAAA;MACf,MAAM0H,KAAK,SAASwE,MAAI,CAACvG,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE,sDAAsD;QAC/DC,OAAO,EAAE,CACP;UACE6D,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZ/N,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACE2N,IAAI,EAAE,KAAK;UACXE,QAAQ,EAAE,qBAAqB;UAC/BC,OAAO,EAAEA,CAAA,KAAK;YACZ;YACA;YACAI,MAAI,CAAC9F,OAAO,CAAC4F,YAAY,CAAC,UAAU,CAAC;UACvC;SACD;OAEJ,CAAC;MAEF,MAAMtE,KAAK,CAACK,OAAO,EAAE;IAAC;EACxB;EACAoE,iBAAiBA,CAAA;IACfpO,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI,CAACsI,aAAa,CAACyF,aAAa,EAAE;IAClC,IAAI,CAAC3F,OAAO,CAAC4F,YAAY,CAAC,UAAU,CAAC;EACvC;EAGAI,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACzU,QAAQ,EAAE;MACjB,MAAM3B,GAAG,GAAG,IAAI6B,KAAK,EAAE;MACvB7B,GAAG,CAACwC,GAAG,GAAG,IAAI,CAACb,QAAQ;MAEvB,MAAM0U,cAAc,GAAGC,MAAM,CAACC,WAAW;MACzC,MAAMC,eAAe,GAAGH,cAAc,GAAG,GAAG,CAAC,CAAC;MAE9C,OAAO;QACL;QACAtS,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAGwS,eAAe,IAAI;QAC9BC,SAAS,EAAE,MAAM;QACjBxR,MAAM,EAAE,MAAM;QACdyR,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE;OACb;;IAEH,OAAO;MACL7S,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE;KACT;EACH;EAEA6S,qBAAqBA,CAACC,QAAgB,EAAEC,SAAiB;IACvD,MAAMC,aAAa,GAAGV,MAAM,CAACW,UAAU;IACvC,MAAMZ,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAMW,WAAW,GAAGJ,QAAQ,GAAGC,SAAS;IACxC,MAAMI,UAAU,GAAGJ,SAAS,GAAGD,QAAQ;IAEvC,IAAIK,UAAU,EAAE;MACd;MACA,MAAMC,QAAQ,GAAGJ,aAAa,GAAG,IAAI,CAAC,CAAC;MACvC,MAAMK,gBAAgB,GAAGD,QAAQ,GAAGF,WAAW;MAE/C,IAAIG,gBAAgB,GAAGhB,cAAc,GAAG,IAAI,EAAE;QAAE;QAC9C,MAAMI,SAAS,GAAGJ,cAAc,GAAG,IAAI,CAAC,CAAC;QACzC,OAAO,GAAGI,SAAS,GAAGS,WAAW,IAAI;;MAGvC,OAAO,GAAGE,QAAQ,IAAI;KACvB,MAAM;MACL;MACA,MAAMA,QAAQ,GAAGJ,aAAa,GAAG,IAAI,CAAC,CAAC;MACvC,OAAO,GAAGI,QAAQ,IAAI;;EAE1B;EAGAE,QAAQA,CAAA;IACN,IAAI,CAACpG,WAAW,CAAC,IAAI,CAACvP,QAAQ,EAAE,IAAI,CAACqO,WAAW,CAAC;EACnD;;eAjnBW5B,WAAW;;mBAAXA,YAAW,EAAAhQ,gEAAA,CAAAuQ,4DAAA,GAAAvQ,gEAAA,CAAAoZ,sDAAA,GAAApZ,gEAAA,CAAAsZ,8DAAA,GAAAtZ,gEAAA,CAAAsZ,4DAAA,GAAAtZ,gEAAA,CAAAyZ,yEAAA,GAAAzZ,gEAAA,CAAA2Z,qEAAA,GAAA3Z,gEAAA,CAAAsZ,qDAAA,GAAAtZ,gEAAA,CAAAA,qDAAA;AAAA;;QAAXgQ,YAAW;EAAAzB,SAAA;EAAAC,SAAA,WAAAuL,kBAAArL,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;MAAX1O,yDAAA,oBAAAga,sCAAA;QAAA,OAAArL,GAAA,CAAAuK,QAAA,EAAU;MAAA,UAAAlZ,8DAAA,CAAC;;;;;;;;;MCjBpBA,6DAFJ,oBAA+C,kBAChC,gBACA;MAAAA,qDAAA,uBAAgB;MAAAA,2DAAA,EAAY;MAErCA,6DADF,qBAA0B,oBACmB;MAA9BA,yDAAA,mBAAAma,iDAAA;QAAAna,4DAAA,CAAAqP,GAAA;QAAA,OAAArP,0DAAA,CAAS2O,GAAA,CAAAoJ,iBAAA,EAAmB;MAAA,EAAC;MACxC/X,wDAAA,kBAAiD;MAIzDA,2DAHM,EAAa,EACD,EACF,EACH;MAEbA,6DAAA,qBAAyE;MACvEA,wDAAA,wBAAuC;MAGnCA,6DAFJ,gBAAwF,cACjD,iBAC0D;MAA1BA,yDAAA,mBAAAoa,8CAAA;QAAApa,4DAAA,CAAAqP,GAAA;QAAA,OAAArP,0DAAA,CAAS2O,GAAA,CAAAsF,WAAA,CAAY,EAAE,CAAC;MAAA,EAAC;MAC1FjU,wDAAA,oBAA4C;MAEhDA,2DADE,EAAS,EACL;MAMNA,wDAAA,uCAMyB;MAG7BA,2DAFE,EAAM,EAEM;MAIdA,6DAAA,eAA+D;MAC7DA,wDAAA,4BAA2D;MAC7DA,2DAAA,EAAM;MAKAA,6DAHN,qBAA+C,mBAChC,mBACE,sBACwE;MAA/BA,yDAAA,mBAAAqa,kDAAA;QAAAra,4DAAA,CAAAqP,GAAA;QAAA,OAAArP,0DAAA,CAAS2O,GAAA,CAAA0I,kBAAA,EAAoB;MAAA,EAAC;MAChFrX,wDAAA,2BAAiD;MACnDA,2DAAA,EAAa;MACbA,6DAAA,sBAAyE;MAAlCA,yDAAA,mBAAAsa,kDAAA;QAAAta,4DAAA,CAAAqP,GAAA;QAAA,OAAArP,0DAAA,CAAS2O,GAAA,CAAAuG,qBAAA,EAAuB;MAAA,EAAC;MACtElV,wDAAA,2BAAkD;MAClDA,6DAAA,YAAM;MAAAA,qDAAA,eAAO;MACfA,2DADe,EAAO,EACT;MACbA,6DAAA,sBAA4E;MAAxBA,yDAAA,mBAAAua,kDAAA;QAAAva,4DAAA,CAAAqP,GAAA;QAAA,OAAArP,0DAAA,CAAS2O,GAAA,CAAAkJ,WAAA,EAAa;MAAA,EAAC;MACzE7X,wDAAA,2BAAoD;MAI5DA,2DAHM,EAAa,EACD,EACF,EACH;;;MAxDDA,yDAAA,YAAAA,8DAAA,KAAAya,GAAA,EAAA9L,GAAA,CAAAyD,SAAA,EAAkC;MAWjCpS,wDAAA,GAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,KAAAya,GAAA,EAAA9L,GAAA,CAAAyD,SAAA,EAAkC;MAIdpS,wDAAA,GAAuC;MAAvCA,0DAAA,mBAAA2O,GAAA,CAAAmD,cAAA,CAAuC;MAWpE9R,wDAAA,GAAqB;MAGrBA,yDAHA,aAAA2O,GAAA,CAAApL,QAAA,CAAqB,uBAAAoL,GAAA,CAAAiD,WAAA,CACa,aAAAjD,GAAA,CAAAoD,aAAA,CACR,YAAApD,GAAA,CAAAqJ,gBAAA,GACI;MAQNhY,wDAAA,GAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,KAAAya,GAAA,EAAA9L,GAAA,CAAAyD,SAAA,EAAkC;MAC1CpS,wDAAA,EAAqB;MAArBA,yDAAA,aAAA2O,GAAA,CAAAwD,QAAA,CAAqB;MAG7BnS,wDAAA,EAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,KAAAya,GAAA,EAAA9L,GAAA,CAAAyD,SAAA,EAAkC;;;;;;;;;;;;;;;;;;;;;;ACxCH;;AAMrC,MAAOsH,gBAAgB;EAI3BhU,YAAA;IAFQ,KAAAiV,QAAQ,GAAsC,EAAE;EAGxD;EAEO9E,OAAOA,CAACrS,GAAW,EAAE4R,KAAa;IACvC,IAAI,CAACwF,MAAM,GAAG,IAAIC,SAAS,CAACrX,GAAG,CAAC;IAEhC,IAAI,CAACoX,MAAM,CAACE,MAAM,GAAI7Y,KAAK,IAAI;MAC7B0H,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE3H,KAAK,CAAC;IACzD,CAAC;IAED,IAAI,CAAC2Y,MAAM,CAACG,SAAS,GAAI9Y,KAAK,IAAI;MAChC,MAAM+Y,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACjZ,KAAK,CAAC+Y,IAAI,CAAC;MACnC,IAAIA,IAAI,CAACG,MAAM,IAAI,IAAI,CAACR,QAAQ,CAACK,IAAI,CAACG,MAAM,CAAC,EAAE;QAC7C,IAAI,CAACR,QAAQ,CAACK,IAAI,CAACG,MAAM,CAAC,CAACC,IAAI,CAACJ,IAAI,CAAC;OACtC,MAAM,IAAI,IAAI,CAACL,QAAQ,CAACvF,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACuF,QAAQ,CAACvF,KAAK,CAAC,CAACgG,IAAI,CAACJ,IAAI,CAAC;;IAEnC,CAAC;IAED,IAAI,CAACJ,MAAM,CAACS,OAAO,GAAIpZ,KAAK,IAAI;MAC9B0H,OAAO,CAACsC,KAAK,CAAC,2BAA2B,EAAEhK,KAAK,CAAC;MACjD,IAAI,CAACqZ,SAAS,CAAC9X,GAAG,EAAE4R,KAAK,CAAC;IAC5B,CAAC;IAED,IAAI,CAACwF,MAAM,CAACW,OAAO,GAAItZ,KAAK,IAAI;MAC9B0H,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE3H,KAAK,CAAC;MAClD,IAAI,CAACqZ,SAAS,CAAC9X,GAAG,EAAE4R,KAAK,CAAC;IAC5B,CAAC;EACH;EAEQkG,SAASA,CAAC9X,GAAW,EAAE4R,KAAa;IAC1CvC,UAAU,CAAC,MAAK;MACd,IAAI,CAACgD,OAAO,CAACrS,GAAG,EAAE4R,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEOoG,IAAIA,CAACR,IAAS;IAAA,IAAAS,YAAA;IACnB,IAAI,EAAAA,YAAA,OAAI,CAACb,MAAM,cAAAa,YAAA,uBAAXA,YAAA,CAAaC,UAAU,MAAKb,SAAS,CAACc,IAAI,EAAE;MAAA,IAAAC,aAAA;MAC9C,CAAAA,aAAA,OAAI,CAAChB,MAAM,cAAAgB,aAAA,eAAXA,aAAA,CAAaJ,IAAI,CAACP,IAAI,CAACY,SAAS,CAACb,IAAI,CAAC,CAAC;KACxC,MAAM;MACLrR,OAAO,CAACsC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEO6J,SAASA,CAACV,KAAa;IAC5B,IAAI,CAAC,IAAI,CAACuF,QAAQ,CAACvF,KAAK,CAAC,EAAE;MACzB,IAAI,CAACuF,QAAQ,CAACvF,KAAK,CAAC,GAAG,IAAIsF,yCAAO,EAAO;;IAE3C,OAAO,IAAI,CAACC,QAAQ,CAACvF,KAAK,CAAC,CAAC0G,YAAY,EAAE;EAC5C;EAEOzF,KAAKA,CAACjB,KAAa;IACxB,IAAI,IAAI,CAACwF,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACvE,KAAK,EAAE;MACnB,OAAO,IAAI,CAACsE,QAAQ,CAACvF,KAAK,CAAC;;EAE/B;;oBA5DWsE,gBAAgB;;mBAAhBA,iBAAgB;AAAA;;SAAhBA,iBAAgB;EAAAqC,OAAA,EAAhBrC,iBAAgB,CAAAsC,IAAA;EAAAC,UAAA,EAFf;AAAM", "sources": ["./src/app/components/image-cropper-custom/image-cropper-custom.component.ts", "./src/app/components/image-cropper-custom/image-cropper-custom.component.html", "./src/app/crop-doc/crop-doc-routing.module.ts", "./src/app/crop-doc/crop-doc.module.ts", "./src/app/crop-doc/crop-doc.page.ts", "./src/app/crop-doc/crop-doc.page.html", "./src/app/services/websocket.service.ts"], "sourcesContent": ["// image-cropper-custom.component.ts\r\nimport {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ElementRef,\r\n  ViewChild,\r\n  OnInit,\r\n  HostListener,\r\n  ChangeDetectorRef,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { Coordinates } from '../../../models/coordinates';\r\n\r\ninterface PointerPosition {\r\n  clientX: number;\r\n  clientY: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-cropper-custom',\r\n  templateUrl: './image-cropper-custom.component.html',\r\n  styleUrls: ['./image-cropper-custom.component.scss'], // Use SCSS if needed\r\n  encapsulation: ViewEncapsulation.None, // Remove style encapsulation\r\n})\r\nexport class ImageCropperCustomComponent implements OnInit {\r\n  @Input() width: string = '100%';\r\n  @Input() height: string = '100%';\r\n  // @Input() rotation: number = 0;\r\n  @ViewChild('svgElement') svgElement!: ElementRef;\r\n  @Output() coordinatesChange = new EventEmitter<Coordinates[]>();\r\n\r\n  @HostListener('pointerup')\r\n  @HostListener('pointercancel')\r\n  onPointerEnd() {\r\n    this.stopDragging();\r\n  }\r\n\r\n  @HostListener('pointermove', ['$event'])\r\n  onPointerMove(event: PointerEvent) {\r\n    if (!this.isDragging) return;\r\n\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    this.handleMove(event);\r\n  }\r\n\r\n  @HostListener('touchstart', ['$event'])\r\n  onTouchStart(event: TouchEvent) {\r\n    if (event.touches.length !== 1) return; // Only handle single touches\r\n\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    const touch = event.touches[0];\r\n    const element = document.elementFromPoint(touch.clientX, touch.clientY);\r\n\r\n    if (element?.classList.contains('handle-touch-area')) {\r\n      const index = parseInt(element.getAttribute('data-index') || '0', 10);\r\n      this.onPointTouchStart(event, index);\r\n    }\r\n  }\r\n\r\n  @HostListener('touchmove', ['$event'])\r\n  onTouchMove(event: TouchEvent) {\r\n    if (!this.isDragging || event.touches.length !== 1) return;\r\n\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    const touch = event.touches[0];\r\n    this.handleMove(touch);\r\n  }\r\n\r\n  @HostListener('touchend')\r\n  onTouchEnd() {\r\n    this.stopDragging();\r\n  }\r\n\r\n  @HostListener('document:mousemove', ['$event'])\r\n  onDocumentMouseMove(event: MouseEvent) {\r\n    this.handleMove(event);\r\n  }\r\n\r\n  // In image-cropper-custom.component.ts\r\n  @Input() set imageUrl(url: string) {\r\n    if (url) {\r\n      const img = new Image();\r\n      img.onload = () => {\r\n        this.img = img;\r\n        this.currentWidth = img.naturalWidth;\r\n        this.currentHeight = img.naturalHeight;\r\n\r\n        // Apply pending coordinates if they exist\r\n        if (this._pendingCoordinates) {\r\n          this.initialCoordinates = this._pendingCoordinates;\r\n          this._pendingCoordinates = null;\r\n        } else {\r\n          this.initializeDefaultPoints();\r\n        }\r\n\r\n        this.cdRef.detectChanges();\r\n      };\r\n      img.src = url;\r\n    }\r\n  }\r\n\r\n  @Input() set initialCoordinates(coords: Coordinates[]) {\r\n    if (!coords || coords.length === 0) {\r\n      this.initializeDefaultPoints();\r\n      return;\r\n    }\r\n  \r\n    const validCoords = coords.slice(0, 4).map((coord) => ({\r\n      x: typeof coord.x === 'number' ? coord.x : 0,\r\n      y: typeof coord.y === 'number' ? coord.y : 0,\r\n    }));\r\n  \r\n    while (validCoords.length < 4) {\r\n      validCoords.push({ x: 0, y: 0 });\r\n    }\r\n  \r\n    if (this.currentWidth > 0) {\r\n      this.quad.points = validCoords.map((point) => {\r\n        const x = this.convertCoordinate(point.x, this.currentWidth);\r\n        const y = this.convertCoordinate(point.y, this.currentHeight);\r\n        return this.clampCoordinates({ x, y });\r\n      });\r\n      this.calculateMidpoints(); // Add this line to calculate midpoints\r\n      this.cdRef.detectChanges();\r\n    } else {\r\n      this._pendingCoordinates = validCoords;\r\n    }\r\n  }\r\n\r\n  @Input() set rotation(value: number) {\r\n    if (this._rotation !== value) {\r\n      const deltaAngle = value - (this._rotation || 0);\r\n      this._rotation = value;\r\n\r\n      if (this.quad.points && this.validateCoordinates(this.quad.points)) {\r\n        this.rotateCoordinates(deltaAngle);\r\n      }\r\n\r\n      this.cdRef.detectChanges();\r\n    }\r\n  }\r\n\r\n  private _rotation: number = 0;\r\n\r\n  private _pendingCoordinates: Coordinates[] | null = null;\r\n\r\n  private convertCoordinate(value: number, max: number): number {\r\n    return value <= 1 ? value * max : value;\r\n  }\r\n\r\n  img: HTMLImageElement | null = null;\r\n  quad: { points: Coordinates[] } = { points: [] };\r\n  currentWidth: number = 0;\r\n  currentHeight: number = 0;\r\n  activePoint: number | null = null;\r\n  isDragging = false;\r\n  isMovingCropArea = false;\r\n  lastMousePosition: Coordinates | null = null;\r\n\r\n  private touchStartPos: Coordinates | null = null;\r\n  private lastTouchPos: Coordinates | null = null;\r\n  isTouchMoving = false;\r\n  touchActive = false;\r\n  private isTouchHandled = false;\r\n\r\n  showMagnifier = false;\r\n  magnifierPosition = { x: 0, y: 0 };\r\n  magnifierRadius = 350; // Radius of the magnifier\r\n  zoomFactor = 4; // How much to zoom in\r\n\r\n  midpoints: Coordinates[] = []; // Array to store midpoint coordinates\r\n  activeMidpoint: number | null = null; // Track the active midpoint being dragged\r\n  midpointDragConstraint: 'horizontal' | 'vertical' | null = null; // Constraint for midpoint movement\r\n\r\n  constructor(public cdRef: ChangeDetectorRef) {}\r\n\r\n  ngOnInit() {\r\n    if (!this.quad.points.length) {\r\n      this.initializeDefaultPoints();\r\n    } else {\r\n      this.validateQuad();\r\n    }\r\n  }\r\n\r\n  get rotation(): number {\r\n    return this._rotation || 0;\r\n  }\r\n\r\n  private initializeDefaultPoints() {\r\n    if (this.currentWidth > 0 && this.currentHeight > 0) {\r\n      const margin = Math.min(\r\n        50,\r\n        Math.min(this.currentWidth, this.currentHeight) / 10\r\n      );\r\n      this.quad.points = [\r\n        { x: margin, y: margin },\r\n        { x: this.currentWidth - margin, y: margin },\r\n        { x: this.currentWidth - margin, y: this.currentHeight - margin },\r\n        { x: margin, y: this.currentHeight - margin },\r\n      ].map((point) => this.clampCoordinates(point));\r\n      this.calculateMidpoints(); // Add this line\r\n    }\r\n  }\r\n\r\n  // handleTouchMove(event: TouchEvent) {\r\n  //   if (!this.isDragging && !this.isMovingCropArea) return;\r\n\r\n  //   event.preventDefault();\r\n  //   const touch = event.touches[0];\r\n  //   const pos = this.getTouchPosition(touch);\r\n\r\n  //   if (this.isDragging && this.activePoint !== null) {\r\n  //     this.quad.points[this.activePoint] = pos;\r\n  //   } else if (this.isMovingCropArea) {\r\n  //     const delta = {\r\n  //       x: pos.x - (this.lastTouchPos?.x || 0),\r\n  //       y: pos.y - (this.lastTouchPos?.y || 0)\r\n  //     };\r\n\r\n  //     this.quad.points = this.quad.points.map(p => ({\r\n  //       x: p.x + delta.x,\r\n  //       y: p.y + delta.y\r\n  //     }));\r\n  //   }\r\n\r\n  //   this.lastTouchPos = pos;\r\n  // }\r\n\r\n  getTouchPosition(event: Touch | MouseEvent): Coordinates {\r\n    const svg = this.svgElement.nativeElement;\r\n    const CTM = svg.getScreenCTM();\r\n    if (!CTM) return { x: 0, y: 0 };\r\n\r\n    const clientX = 'touches' in event ? event.clientX : event.clientX;\r\n    const clientY = 'touches' in event ? event.clientY : event.clientY;\r\n\r\n    return {\r\n      x: (clientX - CTM.e) / CTM.a,\r\n      y: (clientY - CTM.f) / CTM.d,\r\n    };\r\n  }\r\n\r\n  set inactiveSelections(value: any[]) {\r\n    // Maintain compatibility with original component\r\n  }\r\n\r\n  private calculateRotatedDimensions(): { width: number, height: number } {\r\n    const angle = Math.abs(this._rotation % 180); // Normalize angle to 0-180\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n  \r\n    // Calculate rotated dimensions\r\n    let rotatedWidth = Math.abs(this.currentWidth * cos) + Math.abs(this.currentHeight * sin);\r\n    let rotatedHeight = Math.abs(this.currentWidth * sin) + Math.abs(this.currentHeight * cos);\r\n  \r\n    // If image is vertical (height > width)\r\n    if (this.currentHeight > this.currentWidth) {\r\n      if (angle > 45 && angle < 135) {\r\n        // When rotated horizontally (closer to 90 degrees)\r\n        // Adjust the dimensions to maintain aspect ratio\r\n        const scale = this.currentHeight / this.currentWidth;\r\n        rotatedWidth = Math.min(rotatedWidth, this.currentHeight);\r\n        rotatedHeight = rotatedWidth / scale;\r\n      } else {\r\n        // When vertical or close to vertical, use original dimensions\r\n        rotatedWidth = this.currentWidth;\r\n        rotatedHeight = this.currentHeight;\r\n      }\r\n    }\r\n  \r\n    return {\r\n      width: rotatedWidth,\r\n      height: rotatedHeight\r\n    };\r\n  }\r\n  \r\n  getViewBox(): string {\r\n    if (!this.currentWidth || !this.currentHeight) {\r\n      return '0 0 800 600';\r\n    }\r\n  \r\n    const { width, height } = this.calculateRotatedDimensions();\r\n  \r\n    // Calculate the center point\r\n    const centerX = this.currentWidth / 2;\r\n    const centerY = this.currentHeight / 2;\r\n  \r\n    // Calculate the viewBox dimensions\r\n    const viewBoxWidth = width;\r\n    const viewBoxHeight = height;\r\n  \r\n    // Calculate the offset to center the content\r\n    const offsetX = centerX - viewBoxWidth / 2;\r\n    const offsetY = centerY - viewBoxHeight / 2;\r\n  \r\n    return `${offsetX} ${offsetY} ${viewBoxWidth} ${viewBoxHeight}`;\r\n  }\r\n  \r\n  // Update clampCoordinates to use the new dimensions\r\n  private clampCoordinates(coord: Coordinates): Coordinates {\r\n    const { width, height } = this.calculateRotatedDimensions();\r\n    const centerX = this.currentWidth / 2;\r\n    const centerY = this.currentHeight / 2;\r\n  \r\n    // Calculate bounds based on rotated dimensions\r\n    const minX = centerX - width / 2;\r\n    const maxX = centerX + width / 2;\r\n    const minY = centerY - height / 2;\r\n    const maxY = centerY + height / 2;\r\n  \r\n    return {\r\n      x: Math.max(minX, Math.min(maxX, coord.x)),\r\n      y: Math.max(minY, Math.min(maxY, coord.y))\r\n    };\r\n  }\r\n\r\n  getRotationTransform(): string {\r\n    const centerX = this.currentWidth / 2;\r\n    const centerY = this.currentHeight / 2;\r\n    return `rotate(${this.rotation} ${centerX} ${centerY})`;\r\n  }\r\n\r\n  getPolygonPoints(): string {\r\n    return this.quad.points.map((point) => `${point.x},${point.y}`).join(' ');\r\n  }\r\n\r\n  getOverlayPath(): string {\r\n    const points = this.quad.points;\r\n    const width = this.currentWidth;\r\n    const height = this.currentHeight;\r\n\r\n    if (!points || points.length < 4) {\r\n      return `M 0,0 H ${width} V ${height} H 0 Z`;\r\n    }\r\n\r\n    return `\r\n      M 0,0 H ${width} V ${height} H 0 Z\r\n      M ${points[0].x},${points[0].y}\r\n      L ${points[1].x},${points[1].y}\r\n      L ${points[2].x},${points[2].y}\r\n      L ${points[3].x},${points[3].y} Z\r\n    `;\r\n  }\r\n\r\n  getCropTransform(): string {\r\n    return ''; // For additional transformations if needed\r\n  }\r\n\r\n  getMousePosition(event: MouseEvent): Coordinates {\r\n    const svg = this.svgElement.nativeElement;\r\n    const CTM = svg.getScreenCTM();\r\n    if (!CTM) return { x: 0, y: 0 };\r\n\r\n    return {\r\n      x: (event.clientX - CTM.e) / CTM.a,\r\n      y: (event.clientY - CTM.f) / CTM.d,\r\n    };\r\n  }\r\n\r\n  startDragging(event: PointerEvent, index: number) {\r\n    if (this.isTouchHandled) return;\r\n\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    this.isTouchHandled = true;\r\n    this.isDragging = true;\r\n    this.activePoint = index;\r\n    this.showMagnifier = true;\r\n\r\n    const coords = this.getSVGCoordinates(event);\r\n    this.updateMagnifierPosition(coords);\r\n\r\n    if (event.pointerType === 'touch') {\r\n      this.lastTouchPos = this.clampCoordinates(coords);\r\n    } else {\r\n      this.lastMousePosition = this.clampCoordinates(coords);\r\n    }\r\n\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  startMovingCropArea(event: MouseEvent | TouchEvent) {\r\n    event.preventDefault();\r\n    this.isMovingCropArea = true;\r\n\r\n    if (event instanceof TouchEvent) {\r\n      this.lastMousePosition = this.getSVGCoordinates(event.touches[0]);\r\n    } else {\r\n      this.lastMousePosition = this.getSVGCoordinates(event);\r\n    }\r\n  }\r\n\r\n  onPointTouchStart(event: TouchEvent, index: number) {\r\n    console.log('onPointTouchStart', event, index);\r\n\r\n    if (this.isTouchHandled) return; // Prevent multiple triggers\r\n\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    this.isTouchHandled = true;\r\n    this.touchActive = true;\r\n    this.isDragging = true;\r\n    this.activePoint = index;\r\n\r\n    const touch = event.touches[0];\r\n    this.lastTouchPos = this.getSVGCoordinates(touch);\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  handleTouchMove(event: TouchEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    if (!this.isDragging && !this.isMovingCropArea) return;\r\n\r\n    const touch = event.touches[0];\r\n    if (this.isDragging && this.activePoint !== null) {\r\n      const currentPos = this.getSVGCoordinates(touch);\r\n\r\n      // Direct position update for more responsive feeling\r\n      this.quad.points[this.activePoint] = currentPos;\r\n      this.lastTouchPos = currentPos;\r\n\r\n      // Force change detection\r\n      this.cdRef.detectChanges();\r\n    }\r\n  }\r\n\r\n  onPolygonTouchStart(event: TouchEvent) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    // this.startMovingCropArea(event);\r\n  }\r\n\r\n  handleMouseMove(event: MouseEvent) {\r\n    this.handleMove(event);\r\n  }\r\n\r\n  private handleStart(event: PointerPosition, index: number) {\r\n    this.activePoint = index;\r\n    this.isDragging = true;\r\n  }\r\n\r\n  // private handleAreaStart(event: PointerPosition) {\r\n  //   this.isMovingCropArea = true;\r\n  //   this.lastMousePosition = this.getPosition(event);\r\n  // }\r\n\r\n  private handleMove(input: MouseEvent | Touch) {\r\n    if (!this.isDragging) return;\r\n  \r\n    const currentPos = this.getSVGCoordinates(input);\r\n    let clampedPos = this.clampCoordinates(currentPos);\r\n  \r\n    if (this.activePoint !== null) {\r\n      const newPoints = [...this.quad.points];\r\n      newPoints[this.activePoint] = clampedPos;\r\n  \r\n      if (this.isValidQuadrilateral(newPoints)) {\r\n        this.quad.points[this.activePoint] = clampedPos;\r\n        this.calculateMidpoints(); // Recalculate midpoints after corner move\r\n        this.updateMagnifierPosition(clampedPos);\r\n      }\r\n    } else if (this.activeMidpoint !== null) {\r\n      const midpoint = this.midpoints[this.activeMidpoint];\r\n      if (this.midpointDragConstraint === 'horizontal') {\r\n        clampedPos.y = midpoint.y; // Lock y-axis\r\n      } else if (this.midpointDragConstraint === 'vertical') {\r\n        clampedPos.x = midpoint.x; // Lock x-axis\r\n      }\r\n      this.updateCornerPointsFromMidpoint(this.activeMidpoint, clampedPos);\r\n      this.calculateMidpoints(); // Recalculate midpoints after move\r\n      this.updateMagnifierPosition(clampedPos);\r\n    }\r\n  \r\n    this.lastMousePosition = clampedPos;\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  private isValidQuadrilateral(points: Coordinates[]): boolean {\r\n    if (points.length !== 4) return false;\r\n  \r\n    // Minimum distance between points (in pixels)\r\n    const minDistance = 20;\r\n  \r\n    // Check minimum distance between all points\r\n    for (let i = 0; i < points.length; i++) {\r\n      for (let j = i + 1; j < points.length; j++) {\r\n        const distance = this.getDistance(points[i], points[j]);\r\n        if (distance < minDistance) {\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n  \r\n    // Check if points form a convex quadrilateral\r\n    return this.isConvex(points);\r\n  }\r\n  \r\n  private getDistance(p1: Coordinates, p2: Coordinates): number {\r\n    const dx = p2.x - p1.x;\r\n    const dy = p2.y - p1.y;\r\n    return Math.sqrt(dx * dx + dy * dy);\r\n  }\r\n  \r\n  private isConvex(points: Coordinates[]): boolean {\r\n    if (points.length !== 4) return false;\r\n  \r\n    let sign = 0;\r\n    for (let i = 0; i < points.length; i++) {\r\n      const p1 = points[i];\r\n      const p2 = points[(i + 1) % 4];\r\n      const p3 = points[(i + 2) % 4];\r\n  \r\n      const crossProduct = (p2.x - p1.x) * (p3.y - p1.y) - \r\n                          (p2.y - p1.y) * (p3.x - p1.x);\r\n  \r\n      if (i === 0) {\r\n        sign = Math.sign(crossProduct);\r\n      } else if (Math.sign(crossProduct) !== sign && crossProduct !== 0) {\r\n        return false;\r\n      }\r\n    }\r\n    return true;\r\n  }\r\n  \r\n  private arePointsWithinBounds(points: Coordinates[]): boolean {\r\n    return points.every(point => \r\n      point.x >= 0 && \r\n      point.x <= this.currentWidth && \r\n      point.y >= 0 && \r\n      point.y <= this.currentHeight\r\n    );\r\n  }\r\n\r\n  // Generic position getter that works with both touch and mouse events\r\n  // private getPosition(event: MouseEvent | TouchEvent): Coordinates {\r\n  //   const svg = this.svgElement.nativeElement;\r\n  //   const CTM = svg.getScreenCTM();\r\n  //   if (!CTM) return { x: 0, y: 0 };\r\n\r\n  //   // 2) Extract clientX, clientY via our helper\r\n  //   const { clientX, clientY } = this.getPointerPosition(event);\r\n\r\n  //   return {\r\n  //     x: (clientX - CTM.e) / CTM.a,\r\n  //     y: (clientY - CTM.f) / CTM.d,\r\n  //   };\r\n  // }\r\n\r\n  private getSVGCoordinates(input: MouseEvent | Touch): Coordinates {\r\n    const svg = this.svgElement.nativeElement;\r\n    const CTM = svg.getScreenCTM();\r\n    if (!CTM) return { x: 0, y: 0 };\r\n\r\n    let clientX: number;\r\n    let clientY: number;\r\n\r\n    if (input instanceof MouseEvent) {\r\n      clientX = input.clientX;\r\n      clientY = input.clientY;\r\n    } else {\r\n      clientX = input.clientX;\r\n      clientY = input.clientY;\r\n    }\r\n\r\n    return {\r\n      x: (clientX - CTM.e) / CTM.a,\r\n      y: (clientY - CTM.f) / CTM.d,\r\n    };\r\n  }\r\n\r\n  // The helper function\r\n  private getPointerPosition(input: MouseEvent | Touch): {\r\n    clientX: number;\r\n    clientY: number;\r\n  } {\r\n    if (input instanceof MouseEvent) {\r\n      // It's a MouseEvent\r\n      return { clientX: input.clientX, clientY: input.clientY };\r\n    } else {\r\n      // It's a single Touch object\r\n      return { clientX: input.clientX, clientY: input.clientY };\r\n    }\r\n  }\r\n\r\n  stopDragging() {\r\n    this.isDragging = false;\r\n    this.activePoint = null;\r\n    this.activeMidpoint = null; // Reset midpoint\r\n    this.midpointDragConstraint = null; // Reset constraint\r\n    this.lastMousePosition = null;\r\n    this.touchActive = false;\r\n    this.isTouchHandled = false;\r\n    this.showMagnifier = false;\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  async getQuad() {\r\n    // If there's a rotation, we need to apply the inverse rotation to get the original coordinates\r\n    if (this._rotation !== 0) {\r\n      const centerX = this.currentWidth / 2;\r\n      const centerY = this.currentHeight / 2;\r\n      const rad = (-this._rotation * Math.PI) / 180; // Note the negative angle for inverse rotation\r\n      const cos = Math.cos(rad);\r\n      const sin = Math.sin(rad);\r\n\r\n      const rotatedPoints = this.quad.points.map((point) => {\r\n        // Translate to origin\r\n        const dx = point.x - centerX;\r\n        const dy = point.y - centerY;\r\n\r\n        // Rotate\r\n        const rotatedX = centerX + (dx * cos - dy * sin);\r\n        const rotatedY = centerY + (dx * sin + dy * cos);\r\n\r\n        return {\r\n          x: Math.round(rotatedX),\r\n          y: Math.round(rotatedY),\r\n        };\r\n      });\r\n\r\n      return { points: rotatedPoints };\r\n    }\r\n\r\n    // If no rotation, return the points as is\r\n    return {\r\n      points: this.quad.points.map((point) => ({\r\n        x: Math.round(point.x),\r\n        y: Math.round(point.y),\r\n      })),\r\n    };\r\n  }\r\n\r\n  \r\n\r\n  private validateQuad() {\r\n    if (!this.quad.points) return;\r\n\r\n    this.quad.points = this.quad.points.map((point) =>\r\n      this.clampCoordinates(point)\r\n    );\r\n  }\r\n\r\n  // In image-cropper-custom.component.ts\r\n  rotateCoordinates(angle: number) {\r\n    if (!this.quad.points || this.quad.points.length !== 4) {\r\n      console.error('Invalid points array for rotation');\r\n      return;\r\n    }\r\n  \r\n    const centerX = this.currentWidth / 2;\r\n    const centerY = this.currentHeight / 2;\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n  \r\n    // Rotate corner points\r\n    this.quad.points = this.quad.points.map((point) => {\r\n      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {\r\n        console.error('Invalid point coordinates:', point);\r\n        return point;\r\n      }\r\n  \r\n      // Translate point to origin\r\n      const dx = point.x - centerX;\r\n      const dy = point.y - centerY;\r\n  \r\n      // Rotate point\r\n      const newX = centerX + (dx * cos - dy * sin);\r\n      const newY = centerY + (dx * sin + dy * cos);\r\n  \r\n      // Clamp coordinates to image boundaries\r\n      return this.clampCoordinates({\r\n        x: newX,\r\n        y: newY,\r\n      });\r\n    });\r\n  \r\n    // Recalculate midpoints after rotating corner points\r\n    this.calculateMidpoints();\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  private validateCoordinates(points: Coordinates[]): boolean {\r\n    if (!points || points.length !== 4) {\r\n      console.error('Invalid number of points:', points);\r\n      return false;\r\n    }\r\n\r\n    return points.every((point, index) => {\r\n      if (typeof point.x === 'undefined' || typeof point.y === 'undefined') {\r\n        console.error(`Invalid coordinates at index ${index}:`, point);\r\n        return false;\r\n      }\r\n      return true;\r\n    });\r\n  }\r\n\r\n  getMagnifierTransform(): string {\r\n    return ''; // No transform needed for the magnifier group\r\n  }\r\n\r\n  updateMagnifierPosition(point: Coordinates) {\r\n    if (this.activePoint !== null) {\r\n      const activePoint = this.quad.points[this.activePoint];\r\n\r\n      // Apply rotation to the point coordinates\r\n      const centerX = this.currentWidth / 2;\r\n      const centerY = this.currentHeight / 2;\r\n      const rotatedPoint = this.rotatePoint(\r\n        activePoint,\r\n        { x: centerX, y: centerY },\r\n        this.rotation\r\n      );\r\n\r\n      const margin = this.magnifierRadius * 1.2;\r\n\r\n      // Calculate position based on rotated coordinates\r\n      let magnifierX = rotatedPoint.x;\r\n      let magnifierY = rotatedPoint.y;\r\n\r\n      if (rotatedPoint.y < this.currentHeight / 2) {\r\n        magnifierY = rotatedPoint.y + margin;\r\n      } else {\r\n        magnifierY = rotatedPoint.y - margin;\r\n      }\r\n\r\n      if (rotatedPoint.x < this.currentWidth / 2) {\r\n        magnifierX = rotatedPoint.x + margin;\r\n      } else {\r\n        magnifierX = rotatedPoint.x - margin;\r\n      }\r\n\r\n      this.magnifierPosition = {\r\n        x: magnifierX,\r\n        y: magnifierY,\r\n      };\r\n\r\n      this.cdRef.detectChanges();\r\n    }\r\n  }\r\n\r\n  // Helper method to rotate a point\r\n  private rotatePoint(\r\n    point: Coordinates,\r\n    center: Coordinates,\r\n    angleDegrees: number\r\n  ): Coordinates {\r\n    const angleRadians = (angleDegrees * Math.PI) / 180;\r\n    const cos = Math.cos(angleRadians);\r\n    const sin = Math.sin(angleRadians);\r\n\r\n    const dx = point.x - center.x;\r\n    const dy = point.y - center.y;\r\n\r\n    return {\r\n      x: center.x + (dx * cos - dy * sin),\r\n      y: center.y + (dx * sin + dy * cos),\r\n    };\r\n  }\r\n\r\n  // Update the getZoomTransform method to center on the active point\r\n  getZoomTransform(): string {\r\n    if (this.activePoint === null) return '';\r\n\r\n    const activePoint = this.quad.points[this.activePoint];\r\n    const scale = this.zoomFactor;\r\n\r\n    // Calculate center of rotation\r\n    const centerX = this.currentWidth / 2;\r\n    const centerY = this.currentHeight / 2;\r\n\r\n    // Calculate offset to keep the active point centered in the magnifier\r\n    const dx = this.magnifierPosition.x - activePoint.x * scale;\r\n    const dy = this.magnifierPosition.y - activePoint.y * scale;\r\n\r\n    // Include rotation in the transformation\r\n    return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;\r\n  }\r\n\r\n  // getZoomTransform(): string {\r\n  //   if (this.activePoint === null) return '';\r\n  \r\n  //   const activePoint = this.quad.points[this.activePoint];\r\n  //   const scale = this.zoomFactor;\r\n  //   const centerX = this.currentWidth / 2;\r\n  //   const centerY = this.currentHeight / 2;\r\n  //   const dx = this.magnifierPosition.x - activePoint.x * scale;\r\n  //   const dy = this.magnifierPosition.y - activePoint.y * scale;\r\n  \r\n  //   return `translate(${dx}, ${dy}) translate(${centerX * scale}, ${centerY * scale}) rotate(${this.rotation}) translate(${-centerX * scale}, ${-centerY * scale}) scale(${scale})`;\r\n  // }\r\n\r\n  getRotatedIntersectionLines(): { vertical: any, horizontal: any } {\r\n    if (this.activePoint === null) return { vertical: null, horizontal: null };\r\n  \r\n    const point = this.quad.points[this.activePoint];\r\n    const lineLength = 50;\r\n  \r\n    // Calculate unrotated lines first\r\n    const vertical = {\r\n      x1: point.x,\r\n      y1: point.y - lineLength,\r\n      x2: point.x,\r\n      y2: point.y + lineLength\r\n    };\r\n  \r\n    const horizontal = {\r\n      x1: point.x - lineLength,\r\n      y1: point.y,\r\n      x2: point.x + lineLength,\r\n      y2: point.y\r\n    };\r\n  \r\n    // No need to rotate the lines as they will be transformed by the parent group's transform\r\n    return {\r\n      vertical,\r\n      horizontal\r\n    };\r\n  }\r\n  \r\n  private calculateMidpoints() {\r\n    this.midpoints = [];\r\n    for (let i = 0; i < 4; i++) {\r\n      const p1 = this.quad.points[i];\r\n      const p2 = this.quad.points[(i + 1) % 4];\r\n      const midpoint = {\r\n        x: (p1.x + p2.x) / 2,\r\n        y: (p1.y + p2.y) / 2,\r\n      };\r\n      this.midpoints.push(midpoint);\r\n    }\r\n  }\r\n\r\n  startMidpointDragging(event: PointerEvent, index: number) {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    this.isDragging = true;\r\n    this.activeMidpoint = index;\r\n    this.showMagnifier = false;\r\n  \r\n    // Calculate the current angle of the edge\r\n    const edgeAngle = this.getEdgeAngle(index);\r\n    const normalizedAngle = Math.abs(edgeAngle % 180); // Normalize to 0-180°\r\n  \r\n    // Determine if the edge is more horizontal or vertical\r\n    if (normalizedAngle < 45 || normalizedAngle > 135) {\r\n      // Edge is more horizontal, so drag should be vertical\r\n      this.midpointDragConstraint = 'vertical';\r\n    } else {\r\n      // Edge is more vertical, so drag should be horizontal\r\n      this.midpointDragConstraint = 'horizontal';\r\n    }\r\n  \r\n    const coords = this.getSVGCoordinates(event);\r\n    this.updateMagnifierPosition(coords);\r\n    this.lastMousePosition = this.clampCoordinates(coords);\r\n    this.cdRef.detectChanges();\r\n  }\r\n\r\n  private updateCornerPointsFromMidpoint(midpointIndex: number, newPos: Coordinates) {\r\n    const prevIndex = midpointIndex;\r\n    const nextIndex = (midpointIndex + 1) % 4;\r\n    const prevPoint = this.quad.points[prevIndex];\r\n    const nextPoint = this.quad.points[nextIndex];\r\n  \r\n    if (this.midpointDragConstraint === 'horizontal') {\r\n      prevPoint.x = newPos.x;\r\n      nextPoint.x = newPos.x;\r\n    } else if (this.midpointDragConstraint === 'vertical') {\r\n      prevPoint.y = newPos.y;\r\n      nextPoint.y = newPos.y;\r\n    }\r\n  }\r\n\r\n  private getEdgeAngle(index: number): number {\r\n    const p1 = this.quad.points[index];\r\n    const p2 = this.quad.points[(index + 1) % 4];\r\n    const dx = p2.x - p1.x;\r\n    const dy = p2.y - p1.y;\r\n    return Math.atan2(dy, dx) * (180 / Math.PI); // Angle in degrees\r\n  }\r\n\r\n\r\n  getMidpointWidth(index: number): number {\r\n    // Top and bottom midpoints (0 and 2)\r\n    if (index === 0 || index === 2) {\r\n      return 250; // Wider for horizontal handles\r\n    }\r\n    // Left and right midpoints (1 and 3)\r\n    return 70; // Narrower for vertical handles\r\n  }\r\n  \r\n  getMidpointHeight(index: number): number {\r\n    // Top and bottom midpoints (0 and 2)\r\n    if (index === 0 || index === 2) {\r\n      return 70; // Shorter for horizontal handles\r\n    }\r\n    // Left and right midpoints (1 and 3)\r\n    return 250; // Taller for vertical handles\r\n  }\r\n  \r\n  getMidpointX(midpoint: Coordinates, index: number): number {\r\n    const { width } = this.getMidpointDimensions(index);\r\n    return midpoint.x - width / 2;\r\n  }\r\n  \r\n  getMidpointY(midpoint: Coordinates, index: number): number {\r\n    const { height } = this.getMidpointDimensions(index);\r\n    return midpoint.y - height / 2;\r\n  }\r\n  \r\n  getMidpointRadius(index: number): number {\r\n    return 35; // Consistent border radius for all handles\r\n  }\r\n\r\n  getMidpointDimensions(index: number): { width: number; height: number } {\r\n    // Use your existing getEdgeAngle(index):\r\n    const angle = Math.abs(this.getEdgeAngle(index) % 180);\r\n    \r\n    // If angle < 45° or > 135° => Edge is more \"horizontal\"\r\n    // Else => Edge is more \"vertical\"\r\n    if (angle < 45 || angle > 135) {\r\n      // \"Horizontal\" handle\r\n      return { width: 250, height: 70 };\r\n    } else {\r\n      // \"Vertical\" handle\r\n      return { width: 70, height: 250 };\r\n    }\r\n  }\r\n  \r\n}\r\n", "<div class=\"cropper-container\" [style.width]=\"width\" [style.height]=\"height\">\r\n  <svg\r\n    #svgElement\r\n    [attr.viewBox]=\"getViewBox()\"\r\n    (touchmove)=\"handleTouchMove($event)\"\r\n    (touchend)=\"stopDragging()\"\r\n    (touchcancel)=\"stopDragging()\"\r\n    (mouseleave)=\"stopDragging()\"\r\n    class=\"cropper-svg\"\r\n  >\r\n    <!-- Main image -->\r\n    <g [attr.transform]=\"getRotationTransform()\">\r\n      <image\r\n        *ngIf=\"img\"\r\n        [attr.href]=\"img?.src\"\r\n        [attr.width]=\"currentWidth\"\r\n        [attr.height]=\"currentHeight\"\r\n        preserveAspectRatio=\"xMidYMid meet\"\r\n        style=\"image-rendering: optimizeQuality\"\r\n      />\r\n    </g>\r\n\r\n    <!-- Semi-transparent overlay -->\r\n    <path\r\n      [attr.d]=\"getOverlayPath()\"\r\n      class=\"overlay\"\r\n      fill=\"rgba(0, 0, 0, 0.5)\"\r\n    />\r\n\r\n    <!-- Crop area -->\r\n    <g [attr.transform]=\"getCropTransform()\">\r\n      <!-- Polygon outline -->\r\n      <polygon\r\n        [attr.points]=\"getPolygonPoints()\"\r\n        class=\"crop-outline\"\r\n        (touchstart)=\"onPolygonTouchStart($event)\"\r\n      />\r\n\r\n      <!-- Handle points with larger touch areas -->\r\n      <g *ngFor=\"let point of quad?.points; let i = index\">\r\n        <!-- Invisible larger touch area -->\r\n        <circle\r\n          [attr.data-index]=\"i\"\r\n          [attr.cx]=\"point.x\"\r\n          [attr.cy]=\"point.y\"\r\n          r=\"160\"\r\n          class=\"handle-touch-area\"\r\n          [attr.pointer-events]=\"'all'\"\r\n          (pointerdown)=\"startDragging($event, i)\"\r\n        />\r\n\r\n        <!-- Visible point -->\r\n        <circle\r\n          [attr.data-index]=\"i\"\r\n          [attr.cx]=\"point.x\"\r\n          [attr.cy]=\"point.y\"\r\n          r=\"60\"\r\n          [class.touch-active]=\"activePoint === i\"\r\n          class=\"handle-point\"\r\n          [attr.pointer-events]=\"'none'\"\r\n        />\r\n      </g>\r\n\r\n      <g *ngFor=\"let midpoint of midpoints; let j = index\">\r\n        <rect\r\n          [attr.data-index]=\"'mid-' + j\"\r\n          [attr.width]=\"getMidpointDimensions(j).width\"\r\n          [attr.height]=\"getMidpointDimensions(j).height\"\r\n          [attr.x]=\"getMidpointX(midpoint, j)\"\r\n          [attr.y]=\"getMidpointY(midpoint, j)\"\r\n          [attr.rx]=\"getMidpointRadius(j)\"\r\n          [attr.ry]=\"getMidpointRadius(j)\"\r\n          class=\"midpoint-handle\"\r\n          [class.touch-active]=\"activeMidpoint === j\"\r\n          (pointerdown)=\"startMidpointDragging($event, j)\"\r\n        />\r\n      </g>\r\n    </g>\r\n\r\n    <!-- Magnifier -->\r\n    <g *ngIf=\"showMagnifier\" class=\"magnifier\">\r\n      <!-- Background circle -->\r\n      <circle\r\n        [attr.cx]=\"magnifierPosition.x\"\r\n        [attr.cy]=\"magnifierPosition.y\"\r\n        [attr.r]=\"magnifierRadius\"\r\n        class=\"magnifier-background\"\r\n      />\r\n\r\n      <!-- Clipping mask -->\r\n      <clipPath id=\"magnifierClip\">\r\n        <circle\r\n          [attr.cx]=\"magnifierPosition.x\"\r\n          [attr.cy]=\"magnifierPosition.y\"\r\n          [attr.r]=\"magnifierRadius\"\r\n        />\r\n      </clipPath>\r\n\r\n      <!-- Zoomed content -->\r\n      <g clip-path=\"url(#magnifierClip)\">\r\n        <!-- Base image with rotation -->\r\n        <g [attr.transform]=\"getZoomTransform()\">\r\n          <image\r\n            *ngIf=\"img\"\r\n            [attr.href]=\"img?.src\"\r\n            [attr.width]=\"currentWidth\"\r\n            [attr.height]=\"currentHeight\"\r\n            preserveAspectRatio=\"xMidYMid meet\"\r\n          />\r\n\r\n          <!-- Intersection lines -->\r\n          <ng-container *ngIf=\"activePoint !== null\">\r\n            <!-- Vertical line -->\r\n            <line\r\n              [attr.x1]=\"quad.points[activePoint].x\"\r\n              [attr.y1]=\"quad.points[activePoint].y - 50\"\r\n              [attr.x2]=\"quad.points[activePoint].x\"\r\n              [attr.y2]=\"quad.points[activePoint].y + 50\"\r\n              class=\"magnifier-intersection-line\"\r\n            />\r\n\r\n            <!-- Horizontal line -->\r\n            <line\r\n              [attr.x1]=\"quad.points[activePoint].x - 50\"\r\n              [attr.y1]=\"quad.points[activePoint].y\"\r\n              [attr.x2]=\"quad.points[activePoint].x + 50\"\r\n              [attr.y2]=\"quad.points[activePoint].y\"\r\n              class=\"magnifier-intersection-line\"\r\n            />\r\n          </ng-container>\r\n        </g>\r\n      </g>\r\n\r\n      <!-- Border -->\r\n      <circle\r\n        [attr.cx]=\"magnifierPosition.x\"\r\n        [attr.cy]=\"magnifierPosition.y\"\r\n        [attr.r]=\"magnifierRadius\"\r\n        class=\"magnifier-border\"\r\n      />\r\n    </g>\r\n  </svg>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { CropDocPage } from './crop-doc.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: CropDocPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CropDocPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { CropDocPageRoutingModule } from './crop-doc-routing.module';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\nimport { CropDocPage } from './crop-doc.page';\r\nimport { ImageCropperCustomComponent } from '../components/image-cropper-custom/image-cropper-custom.component';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    CropDocPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [CropDocPage, ImageCropperCustomComponent]\r\n})\r\nexport class CropDocPageModule {}\r\n", "// src\\app\\crop-doc\\crop-doc.page.ts\r\nimport { Location } from '@angular/common';\r\nimport { Component, ElementRef, OnInit, ViewChild, inject, Renderer2, ViewContainerRef, TemplateRef, AfterViewInit, HostListener } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { NavController, LoadingController, AlertController  } from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';  // Import the ApiService\r\nimport { Coordinates } from '../../models/coordinates';\r\nimport { SignalService } from '../services/signal.service';\r\nimport { NetworkService } from '../services/network.service';\r\nimport { WebSocketService } from '../services/websocket.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Platform } from '@ionic/angular';\r\nimport { ImageCropperCustomComponent } from '../components/image-cropper-custom/image-cropper-custom.component';\r\n\r\n@Component({\r\n  selector: 'app-crop-doc',\r\n  templateUrl: './crop-doc.page.html',\r\n  styleUrls: ['./crop-doc.page.scss'],\r\n})\r\nexport class CropDocPage implements AfterViewInit  {\r\n  imageUrl: string | any = null;\r\n  coordinates: Coordinates[] = [];\r\n  uuid: string | any = null;\r\n  needs_rotation: boolean = false;\r\n  @ViewChild('cropper', { static: false }) cropper: ElementRef | undefined;\r\n  rotationAngle: number = 0;\r\n  @ViewChild('cropperComponent') cropperComponent!: ImageCropperCustomComponent;\r\n\r\n  navCtrl = inject(NavController)\r\n  apiService = inject(ApiService);  // Inject the ApiService\r\n  signalService = inject(SignalService);\r\n  \r\n  progress = 0;\r\n  isLoading = false;\r\n  isConnected = true; // Track network status\r\n  jobId: string | undefined; // Add this line to store the job ID\r\n\r\n  private currentWidth = 0;\r\n  private currentHeight = 0;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private loadingController: LoadingController, \r\n    private alertController: AlertController, \r\n    private webSocketService: WebSocketService,\r\n    private networkService: NetworkService,\r\n    private platform: Platform,\r\n    private renderer: Renderer2\r\n  ) {\r\n    // this.jobId = this.apiService.generateJobId(); // Generate job ID once\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n\r\n    // Suppose your incoming image is W=3060 by H=4080:\r\n    this.currentWidth = 3060;\r\n    this.currentHeight = 4080;\r\n    \r\n    const params = this.location.getState() as { imageUrl: string; coordinates:  Coordinates[]; uuid: string, needs_rotation: boolean};\r\n    if(!params.imageUrl || !params.coordinates || !params.uuid) {\r\n      this.navCtrl.navigateBack('/scan-bl');\r\n      return;\r\n    }\r\n    this.isValidBlobUrl(params.imageUrl).then((isValid) => {\r\n      if (!isValid) {\r\n        this.navCtrl.navigateBack('/scan-bl');\r\n        return;\r\n      }\r\n      this.imageUrl = params?.imageUrl;\r\n      this.coordinates = params?.coordinates;\r\n      this.uuid = params?.uuid;\r\n      this.needs_rotation = params?.needs_rotation;\r\n\r\n     setTimeout(() => {\r\n      \r\n       this.openCropper( params.imageUrl, params.coordinates);\r\n     }, 500);\r\n     \r\n      console.log(this.location.getState())\r\n    });\r\n    console.log(this.location.getState())\r\n\r\n    if (params?.needs_rotation) {\r\n      this.showRotationAlert();\r\n    }\r\n    \r\n\r\n    // Subscribe to the network status\r\n    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n      this.isConnected = connected;\r\n    });\r\n\r\n    if (this.platform.is('android')) {\r\n      this.renderer.addClass(document.body, 'android-specific');\r\n    } else {\r\n      this.renderer.addClass(document.body, 'other-platform');\r\n    }\r\n\r\n    // // const jobId = this.apiService.generateJobId();  // Get the job ID\r\n    // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n    // this.webSocketService.connect(websocketUrl, this.jobId);\r\n    // this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n    //   // console.log('Received message:', message);\r\n    //   if (message.progress !== undefined) {\r\n    //     this.progress = message.progress;\r\n    //   }\r\n\r\n    //   console.log(\"progress __ :\" , this.progress)\r\n\r\n    // });\r\n  }\r\n\r\n  async showRotationAlert() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Attention',\r\n      message: \"L'image est peut-être pivotée, veuillez la corriger si nécessaire\",\r\n      buttons: ['OK']\r\n    });\r\n  \r\n    await alert.present();\r\n  }\r\n\r\n  // A helper function to rotate a single point by 90° clockwise about (0,0)\r\n  private rotatePoint90Clockwise(pt: Coordinates, oldWidth: number): Coordinates {\r\n    // If pt.x or pt.y can be missing, you can do:\r\n    const x = pt.x ?? 0;  // fallback to 0 if undefined\r\n    const y = pt.y ?? 0;  // fallback to 0 if undefined\r\n  \r\n    console.log({\r\n      x: y,\r\n      y: oldWidth - x\r\n    })\r\n    // Then apply the rotation math\r\n    return {\r\n      x: y,\r\n      y: oldWidth - x\r\n    };\r\n  }\r\n\r\n// rotateImage(angle: number) {\r\n//     // Add animation to the button\r\n//     const button = document.querySelector('.rotate-btn ion-icon');\r\n//     button?.classList.add('rotating');\r\n    \r\n//     // Remove animation class after completion\r\n//     setTimeout(() => {\r\n//         button?.classList.remove('rotating');\r\n//     }, 500);\r\n    \r\n//     // Normalize the angle within 0° to 360°\r\n//     this.rotationAngle = (this.rotationAngle + angle) % 360;\r\n//     if (this.rotationAngle < 0) {\r\n//         this.rotationAngle += 360; // Ensure positive angle\r\n//     }\r\n\r\n//     if (this.coordinates.length !== 4) {\r\n//         console.error(\"Invalid number of coordinates for rotation:\", this.coordinates);\r\n//         return;\r\n//     }\r\n\r\n//     // Validate all points to ensure there are no NaN values\r\n//     this.coordinates.forEach((point, index) => {\r\n//         if (isNaN(point.x) || isNaN(point.y)) {\r\n//             throw new Error(`Point ${index} has invalid coordinates: (${point.x}, ${point.y})`);\r\n//         }\r\n//     });\r\n\r\n//     // Rotate the points according to the current angle\r\n//     switch (this.rotationAngle) {\r\n//         case 90:\r\n//             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));\r\n//             break;\r\n//         case 180:\r\n//             this.coordinates = this.coordinates.map((point) => ({\r\n//                 x: this.currentWidth - point.x,\r\n//                 y: this.currentHeight - point.y,\r\n//             }));\r\n//             break;\r\n//         case 270:\r\n//             this.coordinates = this.coordinates.map((point) => this.rotatePoint90Clockwise(point, this.currentWidth));\r\n//             this.coordinates.reverse(); // Reverse to fix the polygon's point order\r\n//             break;\r\n//         default:\r\n//             break; // No rotation\r\n//     }\r\n    \r\n//       console.log(\"coordinates:\", this.coordinates);\r\n\r\n//     // Update the cropper display\r\n//     if (this.cropper) {\r\n//         const cropperElement = this.cropper.nativeElement.querySelector('app-image-cropper-custom');\r\n//         if (cropperElement) {\r\n//             cropperElement.style.transform = `rotate(${this.rotationAngle}deg)`;\r\n//         }\r\n//     }\r\n// }\r\n\r\n\r\nrotateImage(angle: number) {\r\n  // Add animation to the button\r\n  const button = document.querySelector('.rotate-btn ion-icon');\r\n  button?.classList.add('rotating');\r\n  \r\n  // Remove animation class after completion\r\n  setTimeout(() => {\r\n      button?.classList.remove('rotating');\r\n  }, 500);\r\n  \r\n  // Update rotation angle\r\n  this.rotationAngle = (this.rotationAngle + angle) % 360;\r\n  if (this.rotationAngle < 0) {\r\n      this.rotationAngle += 360;\r\n  }\r\n\r\n  // Update the cropper component's rotation\r\n  if (this.cropperComponent) {\r\n      this.cropperComponent.rotation = this.rotationAngle;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n  /**\r\n * Rotates a single (x,y) corner by a multiple of 90 degrees clockwise.\r\n * Also handles bounding box swaps if angle = 90 or 270.\r\n *\r\n * @param pt    The corner (x,y).\r\n * @param angle  The rotation in degrees (only 90, 180, or 270).\r\n * @returns The rotated corner (x,y).\r\n */\r\nprivate rotateCorners(pt: Coordinates, angle: number): Coordinates {\r\n  const x = pt.x ?? 0; // fallback if undefined\r\n  const y = pt.y ?? 0;\r\n\r\n  // We'll use local copies so we can change them\r\n  let x2 = x;\r\n  let y2 = y;\r\n\r\n  // For convenience in formulas\r\n  const w = this.currentWidth;\r\n  const h = this.currentHeight;\r\n\r\n  // Choose a formula based on angle\r\n  switch (angle) {\r\n    case 90:\r\n      //  x' = y\r\n      //  y' = (oldWidth) - x\r\n      x2 = y;\r\n      y2 = w - x;\r\n      // bounding box becomes (h, w) after 90° rotation\r\n      break;\r\n    case 180:\r\n      //  x' = (oldWidth) - x\r\n      //  y' = (oldHeight) - y\r\n      x2 = w - x;\r\n      y2 = h - y;\r\n      // bounding box remains (w,h) for 180° around top-left\r\n      break;\r\n    case 270:\r\n      //  x' = (oldHeight) - y\r\n      //  y' = x\r\n      x2 = h - y;\r\n      y2 = x;\r\n      // bounding box becomes (h, w) after 270° rotation\r\n      break;\r\n    default:\r\n      // If angle is something else, do nothing or handle error\r\n      console.warn(`rotateCorners() received an unexpected angle: ${angle}`);\r\n      break;\r\n  }\r\n\r\n  return { x: x2, y: y2 };\r\n}\r\n\r\n\r\n\r\n// openCropper(imageUrl: string = '', coordinates: any[] = []) {\r\n//   const cropper = document.querySelector('image-cropper') as any;\r\n//   coordinates = this.convertCoordinates(coordinates);\r\n//   this.coordinates = coordinates;\r\n  \r\n//   if (cropper && this.imageUrl) {\r\n//     const image = new Image();\r\n//     image.src = imageUrl;\r\n    \r\n//     image.onload = () => {\r\n//       // Store dimensions for rotation calculations\r\n//       this.currentWidth = image.width;\r\n//       this.currentHeight = image.height;\r\n      \r\n//       // Update cropper styles based on image dimensions\r\n//       const cropperElement = cropper.parentElement;\r\n//       if (cropperElement) {\r\n//         Object.assign(cropperElement.style, this.getCropperStyles());\r\n//       }\r\n      \r\n//       cropper.img = image;\r\n//       cropper.inactiveSelections = [];\r\n//       cropper.quad = { points: coordinates };\r\n//       cropper.rotation = this.rotationAngle;\r\n//     };\r\n//   }\r\n// }\r\n\r\nasync openCropper(imageUrl: string = '', coordinates: Coordinates[] = []) {\r\n  if (!imageUrl || !coordinates.length) return;\r\n\r\n  console.log('Opening cropper with:', { imageUrl, coordinates });\r\n\r\n  // Convert coordinates if needed\r\n  const convertedCoordinates = this.convertCoordinates(coordinates);\r\n  console.log('Converted coordinates:', convertedCoordinates);\r\n\r\n  // Wait for component to be available\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n  if (this.cropperComponent) {\r\n    // Set image first\r\n    this.cropperComponent.imageUrl = imageUrl;\r\n    \r\n    // Wait for image to load\r\n    await new Promise(resolve => setTimeout(resolve, 200));\r\n    \r\n    // Then set coordinates\r\n    this.cropperComponent.initialCoordinates = convertedCoordinates;\r\n    \r\n    // Update rotation if needed\r\n    this.cropperComponent.rotation = this.rotationAngle;\r\n  } else {\r\n    console.error('Cropper component not found');\r\n  }\r\n}\r\n\r\n\r\n\r\n// async getUpdatedCoordinates() {\r\n\r\n//   this.jobId = this.apiService.generateJobId(); // Generate job ID\r\n//   const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n//   console.log('WebSocket URL crop-doc:', websocketUrl);\r\n//   this.webSocketService.connect(websocketUrl, this.jobId);\r\n\r\n//   this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n//     if (message.progress !== undefined) {\r\n//       this.progress = message.progress;\r\n//       console.log(\"progress __ :\", this.progress);\r\n//     }\r\n//   });\r\n\r\n\r\n//   // const loading = await this.presentLoading(); // Show loading spinner\r\n//   this.isLoading = true;\r\n//   console.log('Request:', this.coordinates);\r\n\r\n//   const cropper = document.querySelector('image-cropper') as any;\r\n//   if (cropper) {\r\n//     const updatedQuad = await cropper.getQuad();\r\n//     const coordinates : Coordinates[] = updatedQuad.points;\r\n//     const that = this;\r\n\r\n//     // Create the request object\r\n//     const model_name =  (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? localStorage.getItem('selectedSupplier') : 'GLOBAL';\r\n//     const request = {\r\n//       coordinates,\r\n//       random_id: this.uuid,\r\n//       model_name: model_name?.toString(),\r\n//       rotation:this.rotationAngle\r\n//     };\r\n\r\n//     console.log('Request:', request);\r\n\r\n//     // Call the processImageSupp API\r\n//     this.apiService.processImageSupp(request, this.jobId).subscribe(\r\n//       (response) => {\r\n//         // loading.dismiss();\r\n//         this.isLoading = false;\r\n//         console.log('API response:', response);\r\n//         // Navigate to the process-doc page with the response data\r\n//         this.signalService.setData(response);\r\n//         this.navCtrl.navigateForward('/process-doc', { state: response });\r\n\r\n//         // Disconnect WebSocket after completion\r\n//         this.webSocketService.close(this.jobId!);\r\n//       },\r\n//       (error) => {\r\n//         // loading.dismiss();\r\n//         this.isLoading = false;\r\n//         const errorMessage = `\r\n//         <h3>Erreur lors le traitement du document</h3>\r\n//         <ul>\r\n//           <li>Verfier les contours du document</li>\r\n//           <li>Verifier la luminosité de l'image</li>\r\n//           <li>Verifier la qualité de l'image</li>\r\n//           <li>Supprimer les objets inutiles dans l'image </li>\r\n//         </ul>\r\n//         `;\r\n//         this.apiService.showErrorAlert( errorMessage);\r\n//         console.error('API error 22:', error.error.message);\r\n//         console.error(errorMessage);\r\n\r\n//         // Disconnect WebSocket after completion\r\n//         this.webSocketService.close(this.jobId!);\r\n//       }\r\n//     );\r\n//   } else {\r\n//     console.log('Cropper not found');\r\n//   }\r\n\r\n//   this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n//     if (message.progress !== undefined) {\r\n//       this.progress = message.progress;\r\n//     }\r\n//   });\r\n// }\r\n\r\nasync getUpdatedCoordinates() {\r\n  if (!this.cropperComponent) {\r\n    console.error('Cropper component not found');\r\n    return;\r\n  }\r\n\r\n  this.isLoading = true;\r\n  this.jobId = this.apiService.generateJobId();\r\n\r\n  try {\r\n    const quad = await this.cropperComponent.getQuad();\r\n    const coordinates = quad.points;\r\n\r\n    console.log('Original coordinates:', this.coordinates);\r\n    console.log('Updated coordinates:', coordinates);\r\n\r\n    const model_name = localStorage.getItem('selectedSupplier') || 'GLOBAL';\r\n    const request = {\r\n      coordinates,\r\n      random_id: this.uuid,\r\n      model_name: model_name === 'AUTRE' || model_name === 'undefined' ? 'GLOBAL' : model_name,\r\n      rotation: this.rotationAngle\r\n    };\r\n\r\n    console.log('Sending request:', request);\r\n\r\n    // Setup WebSocket\r\n    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n    this.webSocketService.connect(websocketUrl, this.jobId);\r\n    \r\n    this.webSocketService.onMessage(this.jobId).subscribe(message => {\r\n      if (message.progress !== undefined) {\r\n        this.progress = message.progress;\r\n      }\r\n    });\r\n\r\n    // Call API\r\n    this.apiService.processImageSupp(request, this.jobId).subscribe(\r\n      response => {\r\n        this.isLoading = false;\r\n        this.signalService.setData(response);\r\n        this.navCtrl.navigateForward('/process-doc', { state: response });\r\n        this.webSocketService.close(this.jobId!);\r\n      },\r\n      error => {\r\n        this.isLoading = false;\r\n        const errorMessage = `\r\n          <h3>Erreur lors le traitement du document</h3>\r\n          <ul>\r\n            <li>Verfier les contours du document</li>\r\n            <li>Verifier la luminosité de l'image</li>\r\n            <li>Verifier la qualité de l'image</li>\r\n            <li>Supprimer les objets inutiles dans l'image </li>\r\n          </ul>\r\n        `;\r\n        this.apiService.showErrorAlert(errorMessage);\r\n        this.webSocketService.close(this.jobId!);\r\n      }\r\n    );\r\n  } catch (error) {\r\n    this.isLoading = false;\r\n    console.error('Error getting coordinates:', error);\r\n  }\r\n}\r\n\r\n\r\n\r\n  // convertCoordinates(arr : any[]) {\r\n  //   return arr.map(item => {\r\n  //     return {\r\n  //       x: parseFloat(item[0].toFixed(1)),\r\n  //       y: parseFloat(item[1].toFixed(1))\r\n  //     };\r\n  //   });\r\n  // }\r\n\r\n  convertCoordinates(arr: any[]): Coordinates[] {\r\n    return arr.map(item => {\r\n      // Handle both array format [x, y] and object format {x, y}\r\n      if (Array.isArray(item)) {\r\n        return {\r\n          x: parseFloat(item[0].toFixed(1)),\r\n          y: parseFloat(item[1].toFixed(1))\r\n        };\r\n      } else {\r\n        return {\r\n          x: parseFloat((item.x || 0).toFixed(1)),\r\n          y: parseFloat((item.y || 0).toFixed(1))\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async isValidBlobUrl(blobUrl: string): Promise<boolean> {\r\n    try {\r\n      const response = await fetch(blobUrl);\r\n      return response.ok; // Returns true if the response is ok (status is in the range 200-299)\r\n    } catch (error) {\r\n      return false; // Returns false if there is an error (e.g., network issue, invalid URL)\r\n    }\r\n  }\r\n\r\n  async presentLoading() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Chargement...',\r\n      spinner: 'circles',\r\n      // duration: 30000 // Optional: specify a timeout for the loading spinner\r\n    });\r\n    await loading.present();\r\n    return loading;\r\n  }\r\n  \r\n  async removeCroppedImage(){\r\n    console.log('Remove cropped image');\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.signalService.removeAllData();\r\n            // redirect to scan-bl\r\n            this.navCtrl.navigateRoot('/scan-bl'); \r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async reTakePhoto() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui',\r\n          cssClass: 'custom-alert-button',\r\n          handler: () => {\r\n            // this.signalService.removeLastIndex();\r\n            // redirect to scan-bl\r\n            this.navCtrl.navigateRoot('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n  clearCroppedImage(){\r\n    console.log('Clear cropped image');\r\n    this.signalService.removeAllData();\r\n    this.navCtrl.navigateRoot('/scan-bl'); \r\n  }\r\n\r\n\r\n  getCropperStyles() {\r\n    if (this.imageUrl) {\r\n      const img = new Image();\r\n      img.src = this.imageUrl;\r\n      \r\n      const viewportHeight = window.innerHeight;\r\n      const availableHeight = viewportHeight - 180; // Subtract header and footer height\r\n      \r\n      return {\r\n        // width: this.calculateCropperWidth(img.width, img.height),\r\n        width: '100%',\r\n        height: `${availableHeight}px`,\r\n        maxHeight: '85vh',\r\n        margin: 'auto',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center'\r\n      };\r\n    }\r\n    return {\r\n      width: '90%',\r\n      height: '100%'\r\n    };\r\n  }\r\n  \r\n  calculateCropperWidth(imgWidth: number, imgHeight: number): string {\r\n    const viewportWidth = window.innerWidth;\r\n    const viewportHeight = window.innerHeight;\r\n    const aspectRatio = imgWidth / imgHeight;\r\n    const isVertical = imgHeight > imgWidth;\r\n    \r\n    if (isVertical) {\r\n      // Increase these values for larger vertical images\r\n      const maxWidth = viewportWidth * 0.98; // Changed from 0.85 to 0.98 (98% of viewport width)\r\n      const calculatedHeight = maxWidth / aspectRatio;\r\n      \r\n      if (calculatedHeight > viewportHeight * 0.90) { // Changed from 0.7 to 0.90 (90% of viewport height)\r\n        const maxHeight = viewportHeight * 0.90; // Changed from 0.7 to 0.90\r\n        return `${maxHeight * aspectRatio}px`;\r\n      }\r\n      \r\n      return `${maxWidth}px`;\r\n    } else {\r\n      // Increase this value for larger horizontal images\r\n      const maxWidth = viewportWidth * 0.98; // Changed from 0.9 to 0.98 (98% of viewport width)\r\n      return `${maxWidth}px`;\r\n    }\r\n  }\r\n\r\n  @HostListener('window:resize')\r\n  onResize() {\r\n    this.openCropper(this.imageUrl, this.coordinates);\r\n  }\r\n\r\n}\r\n", "<ion-header [ngClass]=\"{'loading': isLoading}\">\r\n  <ion-toolbar>\r\n    <ion-title>Scanner votre BL</ion-title>\r\n    <ion-buttons slot=\"start\">\r\n      <ion-button  (click)=\"clearCroppedImage()\">\r\n        <ion-icon name=\"chevron-back-outline\"></ion-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [ngClass]=\"{'loading': isLoading}\" class=\"crop-doc-content\">\r\n  <app-check-network></app-check-network> \r\n  <div #cropper id=\"cropper\" style=\"position:absolute; top:0; left:0; right:0; bottom:0;\">\r\n    <div class=\"rotate-button-container\">\r\n      <button class=\"rotate-btn\" [class.needs-rotation]=\"needs_rotation\" (click)=\"rotateImage(90)\">\r\n        <ion-icon name=\"refresh-outline\"></ion-icon>\r\n      </button>\r\n    </div>\r\n    <!-- <image-cropper \r\n      style=\"--active-stroke:5;--inactive-stroke:4;\" \r\n      [ngStyle]=\"getCropperStyles()\"\r\n      hidefooter=\"\">\r\n    </image-cropper> -->\r\n    <app-image-cropper-custom\r\n    #cropperComponent\r\n    [imageUrl]=\"imageUrl\"\r\n    [initialCoordinates]=\"coordinates\"\r\n    [rotation]=\"rotationAngle\"\r\n    [ngStyle]=\"getCropperStyles()\">\r\n  </app-image-cropper-custom>\r\n  </div>\r\n\r\n</ion-content>\r\n\r\n \r\n\r\n<div class=\"alert-progress\" [ngClass]=\"{'loading': isLoading}\">\r\n  <app-custom-alert [progress]=\"progress\"></app-custom-alert>\r\n</div>\r\n\r\n<ion-footer [ngClass]=\"{'loading': isLoading}\">\r\n  <ion-toolbar>\r\n    <ion-buttons>\r\n      <ion-button class=\"menu-button active\" size=\"small\" (click)=\"removeCroppedImage()\">\r\n        <app-custom-icon name=\"delete\"></app-custom-icon>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button-middle\" (click)=\"getUpdatedCoordinates()\">\r\n        <app-custom-icon name=\"extract\"></app-custom-icon>\r\n        <span>VALIDER</span>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button active\" size=\"small\" (click)=\"reTakePhoto()\">\r\n        <app-custom-icon name=\"re-scan-2\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-footer>\r\n", "import { Injectable } from '@angular/core';\r\nimport { Observable, Subject } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WebSocketService {\r\n  private socket?: WebSocket;\r\n  private subjects: { [jobId: string]: Subject<any> } = {};\r\n\r\n  constructor() {\r\n  }\r\n\r\n  public connect(url: string, jobId: string): void {\r\n    this.socket = new WebSocket(url);\r\n\r\n    this.socket.onopen = (event) => {\r\n      console.log('WebSocket connection established:', event);\r\n    };\r\n\r\n    this.socket.onmessage = (event) => {\r\n      const data = JSON.parse(event.data);\r\n      if (data.job_id && this.subjects[data.job_id]) {\r\n        this.subjects[data.job_id].next(data);\r\n      } else if (this.subjects[jobId]) {\r\n        this.subjects[jobId].next(data);\r\n      }\r\n    };\r\n\r\n    this.socket.onerror = (event) => {\r\n      console.error('WebSocket error observed:', event);\r\n      this.reconnect(url, jobId);\r\n    };\r\n\r\n    this.socket.onclose = (event) => {\r\n      console.log('WebSocket connection closed:', event);\r\n      this.reconnect(url, jobId);\r\n    };\r\n  }\r\n\r\n  private reconnect(url: string, jobId: string): void {\r\n    setTimeout(() => {\r\n      this.connect(url, jobId);\r\n    }, 1000); // Retry connection after 1 second\r\n  }\r\n\r\n  public send(data: any): void {\r\n    if (this.socket?.readyState === WebSocket.OPEN) {\r\n      this.socket?.send(JSON.stringify(data));\r\n    } else {\r\n      console.error('WebSocket connection is not open.');\r\n    }\r\n  }\r\n\r\n  public onMessage(jobId: string): Observable<any> {\r\n    if (!this.subjects[jobId]) {\r\n      this.subjects[jobId] = new Subject<any>();\r\n    }\r\n    return this.subjects[jobId].asObservable();\r\n  }\r\n\r\n  public close(jobId: string): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      delete this.subjects[jobId];\r\n    }\r\n  }\r\n\r\n}\r\n"], "names": ["EventEmitter", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "ImageCropperCustomComponent__svg_g_8_Template_circle_pointerdown_1_listener", "$event", "i_r4", "ɵɵrestoreView", "_r3", "index", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "startDragging", "ɵɵelementEnd", "ɵɵadvance", "ɵɵclassProp", "activePoint", "ImageCropperCustomComponent__svg_g_9_Template_rect_pointerdown_1_listener", "j_r7", "_r6", "startMidpointDragging", "activeMidpoint", "ɵɵelementContainerStart", "ɵɵtemplate", "ImageCropperCustomComponent__svg_g_10__svg_image_6_Template", "ImageCropperCustomComponent__svg_g_10__svg_ng_container_7_Template", "ɵɵproperty", "img", "ImageCropperCustomComponent", "onPointerEnd", "stopDragging", "onPointerMove", "event", "isDragging", "preventDefault", "stopPropagation", "handleMove", "onTouchStart", "touches", "length", "touch", "element", "document", "elementFromPoint", "clientX", "clientY", "classList", "contains", "parseInt", "getAttribute", "onPointTouchStart", "onTouchMove", "onTouchEnd", "onDocumentMouseMove", "imageUrl", "url", "Image", "onload", "currentWidth", "naturalWidth", "currentHeight", "naturalHeight", "_pendingCoordinates", "initialCoordinates", "initializeDefaultPoints", "cdRef", "detectChanges", "src", "coords", "validCoords", "slice", "map", "coord", "x", "y", "push", "quad", "points", "point", "convertCoordinate", "clampCoordinates", "calculateMidpoints", "rotation", "value", "_rotation", "deltaAngle", "validateCoordinates", "rotateCoordinates", "max", "constructor", "width", "height", "coordinatesChange", "isMovingCropArea", "lastMousePosition", "touchStartPos", "lastTouchPos", "isTouchMoving", "touchActive", "isTouchHandled", "showMagnifier", "magnifierPosition", "magnifierRadius", "zoomFactor", "midpoints", "midpointDragConstraint", "ngOnInit", "validateQuad", "margin", "Math", "min", "getTouchPosition", "svg", "svgElement", "nativeElement", "CTM", "getScreenCTM", "e", "a", "f", "d", "inactiveSelections", "calculateRotatedDimensions", "angle", "abs", "rad", "PI", "cos", "sin", "<PERSON><PERSON><PERSON><PERSON>", "rotatedHeight", "scale", "getViewBox", "centerX", "centerY", "viewBoxWidth", "viewBoxHeight", "offsetX", "offsetY", "minX", "maxX", "minY", "maxY", "getRotationTransform", "getPolygonPoints", "join", "getOverlayPath", "getCropTransform", "getMousePosition", "getSVGCoordinates", "updateMagnifierPosition", "pointerType", "startMovingCropArea", "TouchEvent", "console", "log", "handleTouchMove", "currentPos", "onPolygonTouchStart", "handleMouseMove", "handleStart", "input", "clampedPos", "newPoints", "isValidQuadrilateral", "midpoint", "updateCornerPointsFromMidpoint", "minDistance", "i", "j", "distance", "getDistance", "isConvex", "p1", "p2", "dx", "dy", "sqrt", "sign", "p3", "crossProduct", "arePointsWithinBounds", "every", "MouseEvent", "getPointerPosition", "getQuad", "_this", "_asyncToGenerator", "rotatedPoints", "rotatedX", "rotatedY", "round", "error", "newX", "newY", "getMagnifierTransform", "rotatedPoint", "rotatePoint", "magnifierX", "magnifierY", "center", "angleDegrees", "angleRadians", "getZoomTransform", "getRotatedIntersectionLines", "vertical", "horizontal", "lineLength", "x1", "y1", "x2", "y2", "edgeAngle", "getEdgeAngle", "normalizedAngle", "midpointIndex", "newPos", "prevIndex", "nextIndex", "prevPoint", "nextPoint", "atan2", "getMidpointWidth", "getMidpointHeight", "getMidpointX", "getMidpointDimensions", "getMidpointY", "getMidpointRadius", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "viewQuery", "ImageCropperCustomComponent_Query", "rf", "ctx", "ImageCropperCustomComponent_pointerup_HostBindingHandler", "ImageCropperCustomComponent_pointercancel_HostBindingHandler", "ImageCropperCustomComponent_pointermove_HostBindingHandler", "ImageCropperCustomComponent_touchstart_HostBindingHandler", "ImageCropperCustomComponent_touchmove_HostBindingHandler", "ImageCropperCustomComponent_touchend_HostBindingHandler", "ImageCropperCustomComponent_mousemove_HostBindingHandler", "ɵɵresolveDocument", "ImageCropperCustomComponent_Template_svg_touchmove_1_listener", "_r1", "ImageCropperCustomComponent_Template_svg_touchend_1_listener", "ImageCropperCustomComponent_Template_svg_touchcancel_1_listener", "ImageCropperCustomComponent_Template_svg_mouseleave_1_listener", "ImageCropperCustomComponent__svg_image_4_Template", "ImageCropperCustomComponent_Template_polygon_touchstart_7_listener", "ImageCropperCustomComponent__svg_g_8_Template", "ImageCropperCustomComponent__svg_g_9_Template", "ImageCropperCustomComponent__svg_g_10_Template", "ɵɵstyleProp", "RouterModule", "CropDocPage", "routes", "path", "component", "CropDocPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "CropDocPageModule", "declarations", "inject", "NavController", "ApiService", "SignalService", "environment", "route", "location", "loadingController", "alertController", "webSocketService", "networkService", "platform", "renderer", "coordinates", "uuid", "needs_rotation", "rotationAngle", "navCtrl", "apiService", "signalService", "progress", "isLoading", "isConnected", "ngAfterViewInit", "params", "getState", "navigateBack", "isValidBlobUrl", "then", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "openCropper", "showRotationAlert", "getNetworkStatus", "subscribe", "connected", "is", "addClass", "body", "alert", "create", "header", "message", "buttons", "present", "rotatePoint90Clockwise", "pt", "oldWidth", "_pt$x", "_pt$y", "rotateImage", "button", "querySelector", "add", "remove", "cropperComponent", "rotateCorners", "_pt$x2", "_pt$y2", "w", "h", "warn", "_this2", "convertedCoordinates", "convertCoordinates", "Promise", "resolve", "getUpdatedCoordinates", "_this3", "jobId", "generateJobId", "model_name", "localStorage", "getItem", "request", "random_id", "websocketUrl", "webSocketUrl", "connect", "onMessage", "undefined", "processImageSupp", "response", "setData", "navigateForward", "state", "close", "errorMessage", "showError<PERSON><PERSON>t", "arr", "item", "Array", "isArray", "parseFloat", "toFixed", "blobUrl", "fetch", "ok", "presentLoading", "_this4", "loading", "spinner", "removeCroppedImage", "_this5", "text", "role", "cssClass", "handler", "removeAllData", "navigateRoot", "reTakePhoto", "_this6", "clearCroppedImage", "getCropperStyles", "viewportHeight", "window", "innerHeight", "availableHeight", "maxHeight", "display", "justifyContent", "alignItems", "calculateCropperWidth", "imgWidth", "imgHeight", "viewportWidth", "innerWidth", "aspectRatio", "isVertical", "max<PERSON><PERSON><PERSON>", "calculatedHeight", "onResize", "ActivatedRoute", "i2", "Location", "i3", "LoadingController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i4", "WebSocketService", "i5", "NetworkService", "Platform", "Renderer2", "CropDocPage_Query", "CropDocPage_resize_HostBindingHandler", "ɵɵresolveWindow", "ɵɵtext", "CropDocPage_Template_ion_button_click_5_listener", "CropDocPage_Template_button_click_12_listener", "CropDocPage_Template_ion_button_click_21_listener", "CropDocPage_Template_ion_button_click_23_listener", "CropDocPage_Template_ion_button_click_27_listener", "ɵɵpureFunction1", "_c2", "Subject", "subjects", "socket", "WebSocket", "onopen", "onmessage", "data", "JSON", "parse", "job_id", "next", "onerror", "reconnect", "onclose", "send", "_this$socket", "readyState", "OPEN", "_this$socket2", "stringify", "asObservable", "factory", "ɵfac", "providedIn"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}