{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-toggle_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AAClB;AAC/B;AACb;AACiC;AACqB;AAC3B;AACzC;AACJ;AAE7B,MAAMuB,YAAY,GAAG,8+QAA8+Q;AACngR,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,iuOAAiuO;AACrvO,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB7B,qDAAgB,CAAC,IAAI,EAAE4B,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAG5B,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6B,QAAQ,GAAG7B,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8B,OAAO,GAAG9B,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC+B,OAAO,GAAI,UAASC,SAAS,EAAG,EAAC;IACtC,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,YAAY,gBAAAC,6OAAA,CAAG,aAAY;MAC5B,MAAM;QAAEC;MAAY,CAAC,GAAGX,KAAI;MAC5B,IAAIW,WAAW,EAAE;QACbX,KAAI,CAACY,OAAO,GAAG,OAAO,sHAA6B,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,QAAQ;UACrBC,eAAe,EAAE,GAAG;UACpBC,SAAS,EAAE,CAAC;UACZC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEA,CAAA,KAAMnB,KAAI,CAACmB,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAKrB,KAAI,CAACoB,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAKrB,KAAI,CAACsB,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACFrB,KAAI,CAACuB,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAIH,EAAE,IAAK;MACnB,IAAI,IAAI,CAACI,QAAQ,EAAE;QACf;MACJ;MACAJ,EAAE,CAACK,cAAc,CAAC,CAAC;MACnB,IAAI,IAAI,CAACpB,QAAQ,GAAG,GAAG,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAClC,IAAI,CAACC,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC7B,OAAO,CAAC4B,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,kBAAkB,GAAG,CAACC,IAAI,EAAEC,OAAO,KAAK;MACzC,IAAID,IAAI,KAAK,IAAI,EAAE;QACf,OAAOC,OAAO,GAAGjD,iDAAgB,GAAGC,iDAAa;MACrD;MACA,OAAOgD,OAAO,GAAGhD,iDAAa,GAAGE,iDAAc;IACnD,CAAC;IACD,IAAI,CAAC+C,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACnC,OAAO;IACxB,IAAI,CAAC+B,OAAO,GAAG,KAAK;IACpB,IAAI,CAACV,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,iBAAiB,GAAGnD,wDAAM,CAACoD,GAAG,CAAC,mBAAmB,CAAC;IACxD,IAAI,CAACC,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EACAtB,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACX,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACkC,MAAM,CAAC,CAAC,IAAI,CAACrB,QAAQ,CAAC;IACvC;EACJ;EACAI,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEM,OAAO;MAAEK;IAAM,CAAC,GAAG,IAAI;IAC/B,MAAMO,YAAY,GAAG,CAACZ,OAAO;IAC7B,IAAI,CAACA,OAAO,GAAGY,YAAY;IAC3B,IAAI,CAAC9C,SAAS,CAAC8B,IAAI,CAAC;MAChBI,OAAO,EAAEY,YAAY;MACrBP;IACJ,CAAC,CAAC;EACN;EACMQ,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvC,6OAAA;MACtB;AACR;AACA;AACA;AACA;AACA;MACQ,IAAIuC,MAAI,CAACzC,OAAO,EAAE;QACdyC,MAAI,CAACxC,YAAY,CAAC,CAAC;MACvB;IAAC;EACL;EACAyC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACzC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACD,OAAO,GAAG,IAAI;EACvB;EACA2C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACwC,OAAO,CAAC,CAAC;MACtB,IAAI,CAACxC,OAAO,GAAG0B,SAAS;IAC5B;EACJ;EACAe,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC9C,mBAAmB,GAAG+C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5E,uDAAqB,CAAC,IAAI,CAACmC,EAAE,CAAC,CAAC;EAChF;EACAK,OAAOA,CAAA,EAAG;IACN,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACoB,QAAQ,CAAC,CAAC;EACnB;EACApC,MAAMA,CAACqC,MAAM,EAAE;IACX,IAAIC,YAAY,CAAC3E,mDAAK,CAAC,IAAI,CAAC+B,EAAE,CAAC,EAAE,IAAI,CAACqB,OAAO,EAAEsB,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE;MAChE,IAAI,CAAC9B,aAAa,CAAC,CAAC;MACpB/C,sDAAe,CAAC,CAAC;IACrB;EACJ;EACAwC,KAAKA,CAACD,EAAE,EAAE;IACN,IAAI,CAACe,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC9B,QAAQ,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1BP,EAAE,CAACuC,KAAK,CAAClC,cAAc,CAAC,CAAC;IACzBL,EAAE,CAACuC,KAAK,CAACC,wBAAwB,CAAC,CAAC;EACvC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACtB,KAAK,IAAI,EAAE;EAC3B;EACAgB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACO,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACAC,uBAAuBA,CAAC/B,IAAI,EAAEC,OAAO,EAAE;IACnC,MAAM+B,IAAI,GAAG,IAAI,CAACjC,kBAAkB,CAACC,IAAI,EAAEC,OAAO,CAAC;IACnD,OAAQ7D,qDAAC,CAAC,UAAU,EAAE;MAAE6F,KAAK,EAAE;QACvB,oBAAoB,EAAE,IAAI;QAC1B,4BAA4B,EAAEhC;MAClC,CAAC;MAAE+B,IAAI,EAAEA,IAAI;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC;EAC/C;EACAE,mBAAmBA,CAAA,EAAG;IAClB,MAAMlC,IAAI,GAAG1C,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEiD,iBAAiB;MAAEN;IAAQ,CAAC,GAAG,IAAI;IAC3C,OAAQ7D,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,OAAO;MAAEC,GAAG,EAAGxD,EAAE,IAAM,IAAI,CAACH,WAAW,GAAGG;IAAI,CAAC,EAAE2B,iBAAiB,IAC7GP,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC+B,uBAAuB,CAAC/B,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC+B,uBAAuB,CAAC/B,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE5D,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE;IAAsB,CAAC,EAAE7F,qDAAC,CAAC,KAAK,EAAE;MAAE6F,KAAK,EAAE,cAAc;MAAEE,IAAI,EAAE;IAAS,CAAC,EAAE5B,iBAAiB,IAAIP,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC+B,uBAAuB,CAAC/B,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;EACpS;EACA,IAAIoC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzD,EAAE,CAAC0D,WAAW,KAAK,EAAE;EACrC;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAErC,SAAS;MAAEC,KAAK;MAAEF,OAAO;MAAEV,QAAQ;MAAEX,EAAE;MAAE8B,OAAO;MAAED,cAAc;MAAEvC,OAAO;MAAEmC,IAAI;MAAEM;IAAU,CAAC,GAAG,IAAI;IAC3G,MAAMX,IAAI,GAAG1C,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMgD,KAAK,GAAG,IAAI,CAACsB,QAAQ,CAAC,CAAC;IAC7B,MAAMY,GAAG,GAAG3F,mDAAK,CAAC+B,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrClC,uDAAiB,CAAC,IAAI,EAAEkC,EAAE,EAAEyB,IAAI,EAAEJ,OAAO,GAAGK,KAAK,GAAG,EAAE,EAAEf,QAAQ,CAAC;IACjE,OAAQnD,qDAAC,CAACE,iDAAI,EAAE;MAAEmG,GAAG,EAAE,0CAA0C;MAAEnD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE2C,KAAK,EAAEnF,qDAAkB,CAACqD,KAAK,EAAE;QACnH,CAACH,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEjD,qDAAW,CAAC,UAAU,EAAE6B,EAAE,CAAC;QACtC,kBAAkB,EAAEsB,SAAS;QAC7B,gBAAgB,EAAED,OAAO;QACzB,iBAAiB,EAAEV,QAAQ;QAC3B,CAAE,kBAAiBmB,OAAQ,EAAC,GAAG,IAAI;QACnC,CAAE,oBAAmBC,SAAU,EAAC,GAAG,IAAI;QACvC,CAAE,0BAAyBF,cAAe,EAAC,GAAG,IAAI;QAClD,CAAE,UAAS+B,GAAI,EAAC,GAAG;MACvB,CAAC;IAAE,CAAC,EAAEpG,qDAAC,CAAC,OAAO,EAAE;MAAEqG,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;IAAiB,CAAC,EAAE7F,qDAAC,CAAC,OAAO,EAAEgF,MAAM,CAACC,MAAM,CAAC;MAAEoB,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,QAAQ;MAAE,cAAc,EAAG,GAAE1C,OAAQ,EAAC;MAAEA,OAAO,EAAEA,OAAO;MAAEV,QAAQ,EAAEA,QAAQ;MAAEqD,EAAE,EAAE1E,OAAO;MAAE0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEE,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAEsC,GAAG,EAAGP,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAE,IAAI,CAACxD,mBAAmB,CAAC,CAAC,EAAEjC,qDAAC,CAAC,KAAK,EAAE;MAAEqG,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;QACne,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACI;MACvC,CAAC;MAAEF,IAAI,EAAE;IAAQ,CAAC,EAAE/F,qDAAC,CAAC,MAAM,EAAE;MAAEqG,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAErG,qDAAC,CAAC,KAAK,EAAE;MAAEqG,GAAG,EAAE,0CAA0C;MAAER,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;EACjN;EACA,IAAItD,EAAEA,CAAA,EAAG;IAAE,OAAOpC,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWqG,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACD,MAAMrB,YAAY,GAAGA,CAACgB,GAAG,EAAEvC,OAAO,EAAEwB,MAAM,EAAEqB,MAAM,KAAK;EACnD,IAAI7C,OAAO,EAAE;IACT,OAAQ,CAACuC,GAAG,IAAIM,MAAM,GAAGrB,MAAM,IAAMe,GAAG,IAAI,CAACM,MAAM,GAAGrB,MAAO;EACjE,CAAC,MACI;IACD,OAAQ,CAACe,GAAG,IAAI,CAACM,MAAM,GAAGrB,MAAM,IAAMe,GAAG,IAAIM,MAAM,GAAGrB,MAAO;EACjE;AACJ,CAAC;AACD,IAAItD,SAAS,GAAG,CAAC;AACjBR,MAAM,CAACoF,KAAK,GAAG;EACXC,GAAG,EAAExF,kBAAkB;EACvByF,EAAE,EAAEvF;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { c as hapticSelection } from './haptic-ac164e4c.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-e2cf2ceb.js';\nimport { c as config, b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\n\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #0054e9);--border-radius:15.5px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 4px rgba(0, 0, 0, 0.06), 0 3px 8px rgba(0, 0, 0, 0.06);--handle-height:calc(31px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(31px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}.native-wrapper .toggle-icon{width:51px;height:31px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark, #222428)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}\";\nconst IonToggleIosStyle0 = toggleIosCss;\n\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;outline:none;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #0054e9);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}\";\nconst IonToggleMdStyle0 = toggleMdCss;\n\nconst Toggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-tg-${toggleIds++}`;\n        this.lastDrag = 0;\n        this.inheritedAttributes = {};\n        this.didLoad = false;\n        this.setupGesture = async () => {\n            const { toggleTrack } = this;\n            if (toggleTrack) {\n                this.gesture = (await import('./index-39782642.js')).createGesture({\n                    el: toggleTrack,\n                    gestureName: 'toggle',\n                    gesturePriority: 100,\n                    threshold: 5,\n                    passive: false,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.disabledChanged();\n            }\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            ev.preventDefault();\n            if (this.lastDrag + 300 < Date.now()) {\n                this.toggleChecked();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.getSwitchLabelIcon = (mode, checked) => {\n            if (mode === 'md') {\n                return checked ? checkmarkOutline : removeOutline;\n            }\n            return checked ? removeOutline : ellipseOutline;\n        };\n        this.activated = false;\n        this.color = undefined;\n        this.name = this.inputId;\n        this.checked = false;\n        this.disabled = false;\n        this.value = 'on';\n        this.enableOnOffLabels = config.get('toggleOnOffLabels');\n        this.labelPlacement = 'start';\n        this.justify = 'space-between';\n        this.alignment = 'center';\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    toggleChecked() {\n        const { checked, value } = this;\n        const isNowChecked = !checked;\n        this.checked = isNowChecked;\n        this.ionChange.emit({\n            checked: isNowChecked,\n            value,\n        });\n    }\n    async connectedCallback() {\n        /**\n         * If we have not yet rendered\n         * ion-toggle, then toggleTrack is not defined.\n         * But if we are moving ion-toggle via appendChild,\n         * then toggleTrack will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n    }\n    componentDidLoad() {\n        this.setupGesture();\n        this.didLoad = true;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n    onStart() {\n        this.activated = true;\n        // touch-action does not work in iOS\n        this.setFocus();\n    }\n    onMove(detail) {\n        if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n            this.toggleChecked();\n            hapticSelection();\n        }\n    }\n    onEnd(ev) {\n        this.activated = false;\n        this.lastDrag = Date.now();\n        ev.event.preventDefault();\n        ev.event.stopImmediatePropagation();\n    }\n    getValue() {\n        return this.value || '';\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    renderOnOffSwitchLabels(mode, checked) {\n        const icon = this.getSwitchLabelIcon(mode, checked);\n        return (h(\"ion-icon\", { class: {\n                'toggle-switch-icon': true,\n                'toggle-switch-icon-checked': checked,\n            }, icon: icon, \"aria-hidden\": \"true\" }));\n    }\n    renderToggleControl() {\n        const mode = getIonMode(this);\n        const { enableOnOffLabels, checked } = this;\n        return (h(\"div\", { class: \"toggle-icon\", part: \"track\", ref: (el) => (this.toggleTrack = el) }, enableOnOffLabels &&\n            mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", { class: \"toggle-icon-wrapper\" }, h(\"div\", { class: \"toggle-inner\", part: \"handle\" }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked)))));\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    render() {\n        const { activated, color, checked, disabled, el, justify, labelPlacement, inputId, name, alignment } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { key: 'b0d648e071bc8095998b519ce4dcdd0ea91575c0', onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'toggle-activated': activated,\n                'toggle-checked': checked,\n                'toggle-disabled': disabled,\n                [`toggle-justify-${justify}`]: true,\n                [`toggle-alignment-${alignment}`]: true,\n                [`toggle-label-placement-${labelPlacement}`]: true,\n                [`toggle-${rtl}`]: true,\n            }) }, h(\"label\", { key: '9a2fe1d16dba66a9dfef450efebf2e5cbe3dcd00', class: \"toggle-wrapper\" }, h(\"input\", Object.assign({ key: '4358d18cb86da768155c17f9da3aac641296c256', type: \"checkbox\", role: \"switch\", \"aria-checked\": `${checked}`, checked: checked, disabled: disabled, id: inputId, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, this.inheritedAttributes)), h(\"div\", { key: '0ec99e452164d7059cc4f93d09e5b918f82c022a', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, h(\"slot\", { key: '1c5cbc99905a79e842e9487eb3ca654e1bab3c66' })), h(\"div\", { key: 'ca8196423b429899febd11c7337d768ff05df6f4', class: \"native-wrapper\" }, this.renderToggleControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n    if (checked) {\n        return (!rtl && margin > deltaX) || (rtl && -margin < deltaX);\n    }\n    else {\n        return (!rtl && -margin < deltaX) || (rtl && margin > deltaX);\n    }\n};\nlet toggleIds = 0;\nToggle.style = {\n    ios: IonToggleIosStyle0,\n    md: IonToggleMdStyle0\n};\n\nexport { Toggle as ion_toggle };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "inheritAriaAttributes", "renderHiddenInput", "c", "hapticSelection", "isRTL", "createColorClasses", "hostContext", "checkmarkOutline", "removeOutline", "g", "ellipseOutline", "config", "b", "getIonMode", "toggleIosCss", "IonToggleIosStyle0", "toggleMdCss", "IonToggleMdStyle0", "Toggle", "constructor", "hostRef", "_this", "ionChange", "ionFocus", "ionBlur", "inputId", "toggleIds", "lastDrag", "inheritedAttributes", "didLoad", "setupGesture", "_asyncToGenerator", "toggleTrack", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "passive", "onStart", "onMove", "ev", "onEnd", "disabled<PERSON><PERSON>ed", "onClick", "disabled", "preventDefault", "Date", "now", "toggleChecked", "onFocus", "emit", "onBlur", "getSwitchLabelIcon", "mode", "checked", "activated", "color", "undefined", "name", "value", "enableOnOffLabels", "get", "labelPlacement", "justify", "alignment", "enable", "isNowChecked", "connectedCallback", "_this2", "componentDidLoad", "disconnectedCallback", "destroy", "componentWillLoad", "Object", "assign", "setFocus", "detail", "shouldToggle", "deltaX", "event", "stopImmediatePropagation", "getValue", "focusEl", "focus", "renderOnOffSwitchLabels", "icon", "class", "renderToggleControl", "part", "ref", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "render", "rtl", "key", "type", "role", "id", "watchers", "margin", "style", "ios", "md", "ion_toggle"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}