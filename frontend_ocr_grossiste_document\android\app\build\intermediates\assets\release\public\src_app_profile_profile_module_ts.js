"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_profile_profile_module_ts"],{

/***/ 31634:
/*!***************************************************!*\
  !*** ./src/app/profile/profile-routing.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProfilePageRoutingModule: () => (/* binding */ ProfilePageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _profile_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./profile.page */ 76980);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _ProfilePageRoutingModule;




const routes = [{
  path: '',
  component: _profile_page__WEBPACK_IMPORTED_MODULE_0__.ProfilePage
}];
class ProfilePageRoutingModule {}
_ProfilePageRoutingModule = ProfilePageRoutingModule;
_ProfilePageRoutingModule.ɵfac = function ProfilePageRoutingModule_Factory(t) {
  return new (t || _ProfilePageRoutingModule)();
};
_ProfilePageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _ProfilePageRoutingModule
});
_ProfilePageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](ProfilePageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 4219:
/*!*******************************************!*\
  !*** ./src/app/profile/profile.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProfilePageModule: () => (/* binding */ ProfilePageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _profile_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./profile-routing.module */ 31634);
/* harmony import */ var _profile_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./profile.page */ 76980);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
var _ProfilePageModule;






class ProfilePageModule {}
_ProfilePageModule = ProfilePageModule;
_ProfilePageModule.ɵfac = function ProfilePageModule_Factory(t) {
  return new (t || _ProfilePageModule)();
};
_ProfilePageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
  type: _ProfilePageModule
});
_ProfilePageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _profile_routing_module__WEBPACK_IMPORTED_MODULE_0__.ProfilePageRoutingModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](ProfilePageModule, {
    declarations: [_profile_page__WEBPACK_IMPORTED_MODULE_1__.ProfilePage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _profile_routing_module__WEBPACK_IMPORTED_MODULE_0__.ProfilePageRoutingModule]
  });
})();

/***/ }),

/***/ 76980:
/*!*****************************************!*\
  !*** ./src/app/profile/profile.page.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProfilePage: () => (/* binding */ ProfilePage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _services_user_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/user.service */ 29885);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);

var _ProfilePage;






class ProfilePage {
  constructor(navCtrl, apiService, location, userService, alertController) {
    this.navCtrl = navCtrl;
    this.apiService = apiService;
    this.location = location;
    this.userService = userService;
    this.alertController = alertController;
    this.user = null;
    this.darkMode = false;
  }
  ngOnInit() {
    this.forceLightMode();
    this.user = this.userService.user();
  }
  goBack() {
    var _this$location$getSta;
    // check if there is pages before 
    if (this.location.getState() && ((_this$location$getSta = this.location.getState()) === null || _this$location$getSta === void 0 ? void 0 : _this$location$getSta.navigationId) > 1) {
      this.navCtrl.pop();
    } else {
      this.navCtrl.navigateBack('/scan-bl');
    }
    this.navCtrl.pop();
  }
  navigateTo(page) {
    this.navCtrl.navigateForward(`/${page}`);
  }
  navigateToPayments() {
    this.navCtrl.navigateForward('/payments');
  }
  toggleDarkMode() {
    document.body.classList.toggle('dark', this.darkMode);
  }
  // onAbout() {
  //    this.alertController.create({
  //     animated : true,
  //     header:'À propos',
  //     message:'Sophatel Tous droits réservés',
  //     buttons:['OK']
  //    }).then(alert=>alert.present())
  // }
  onLogout() {
    this.alertController.create({
      animated: true,
      header: "Confirmation de déconnection",
      message: "Voulez-vous vraiment se déconnecter",
      buttons: [{
        text: "Oui",
        handler: () => {
          localStorage.removeItem('tokenUser');
          localStorage.removeItem('tokenTenant');
          localStorage.removeItem('token');
          localStorage.removeItem('ocrMode');
          localStorage.removeItem('forceSupplierGlobal');
          localStorage.removeItem('selectedSupplier');
          this.navCtrl.navigateBack('/login');
        }
      }, {
        text: "Non",
        role: 'cancel'
      }]
    }).then(alert => alert.present());
  }
  forceLightMode() {
    // Remove dark mode from body
    document.body.classList.remove('dark');
    document.documentElement.classList.remove('dark');
    // Add light mode
    document.body.classList.add('light');
    document.documentElement.classList.add('light');
    // Set attribute
    document.body.setAttribute('data-theme', 'light');
    // Force color scheme
    const meta = document.createElement('meta');
    meta.name = 'color-scheme';
    meta.content = 'light';
    document.head.appendChild(meta);
  }
  onAbout() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this.alertController.create({
        header: 'À propos de l\'application',
        cssClass: 'custom-alert',
        message: `
        <div class="about-content">
          <h4>Description</h4>
          <p>Avec <b>WinDoc</b>, scannez vos <b>Bons de Livraison</b> en quelques secondes. Les données extraites seront disponibles dans votre espace personnel sur <b>WinPlusPharma</b>.</p>
          <p><small>Menu → Achats → Réception → Import BL → Importer BLs Scannés</small></p>
          
          <h4>Conditions d'utilisation</h4>
          <ul>
            <li>Cette application est réservée à un usage professionnel</li>
            <li>Les données capturées sont traitées de manière confidentielle</li>
            <li>L'utilisateur s'engage à respecter les bonnes pratiques de numérisation</li>
          </ul>
  
          <h4>Version</h4>
          <p>Version 1.0.0</p>
          
          <small class="privacy-link" id="privacy-link">Politique de confidentialité</small>
        </div>
      `,
        buttons: [{
          text: 'Fermer',
          role: 'cancel'
        }]
      });
      yield alert.present();
      // Add click listener for privacy link
      const privacyLink = document.getElementById('privacy-link');
      if (privacyLink) {
        privacyLink.addEventListener('click', () => {
          alert.dismiss();
          _this.showPrivacyPolicy();
        });
      }
    })();
  }
  showPrivacyPolicy() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this2.alertController.create({
        header: 'Politique de confidentialité',
        cssClass: 'custom-alert',
        message: `
        <div class="privacy-content">
          <p>Nous nous engageons à protéger vos données personnelles :</p>
          <ul>
            <li>Les données capturées sont stockées de manière sécurisée</li>
            <li>Aucune information personnelle n'est partagée avec des tiers</li>
            <li>Les documents sont traités conformément au RGPD</li>
            <li>Vous disposez d'un droit d'accès et de rectification de vos données</li>
          </ul>
        </div>
      `,
        buttons: ['Fermer']
      });
      yield alert.present();
    })();
  }
  logout() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      yield _this3.apiService.logout(); // Wait for the confirmation dialog
      _this3.navCtrl.navigateRoot('/login'); // Then navigate to login
    })();
  }
}
_ProfilePage = ProfilePage;
_ProfilePage.ɵfac = function ProfilePage_Factory(t) {
  return new (t || _ProfilePage)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_4__.NavController), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_api_service__WEBPACK_IMPORTED_MODULE_1__.ApiService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_5__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_user_service__WEBPACK_IMPORTED_MODULE_2__.UserService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_6__.AlertController));
};
_ProfilePage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
  type: _ProfilePage,
  selectors: [["app-profile"]],
  decls: 32,
  vars: 3,
  consts: [[2, "margin-left", "10px"], ["slot", "start"], [3, "click"], ["slot", "icon-only", "name", "chevron-back-outline"], ["slot", "end"], ["slot", "icon-only", "name", "log-out-outline"], [1, "profile-container"], [1, "profile-avatar"], ["src", "https://gravatar.com/avatar/placeholder?d=mp", "alt", "profile picture"], ["color", "white", 2, "display", "flex", "flex-direction", "column", "align-items", "center"], [1, "user-name"], ["color", "light"], ["button", "", "detail", "", 3, "click"], ["slot", "start", "name", "information-circle-outline"], ["button", "", "detail", "", "routerLink", "/medicament-ocr"], ["slot", "start", "name", "medkit-outline"], ["slot", "start", "name", "log-out-outline"]],
  template: function ProfilePage_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title", 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Param\u00E9tres");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "ion-buttons", 1)(5, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ProfilePage_Template_ion_button_click_5_listener() {
        return ctx.goBack();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "ion-icon", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "ion-buttons", 4)(8, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ProfilePage_Template_ion_button_click_8_listener() {
        return ctx.logout();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](9, "ion-icon", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "ion-content")(11, "div", 6)(12, "ion-avatar", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](13, "img", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "ion-text", 9)(15, "h2", 10);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](16);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "ion-badge", 11);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](19, "ion-list")(20, "ion-item", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ProfilePage_Template_ion_item_click_20_listener() {
        return ctx.onAbout();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](21, "ion-icon", 13);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "ion-label");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](23, "\u00C0 propos");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](24, "ion-item", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](25, "ion-icon", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "ion-label");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](27, "Recherche de m\u00E9dicaments");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "ion-item", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ProfilePage_Template_ion_item_click_28_listener() {
        return ctx.onLogout();
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](29, "ion-icon", 16);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "ion-label");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](31, "D\u00E9connexion");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](16);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("", ctx.user == null ? null : ctx.user.firstname, " ", ctx.user == null ? null : ctx.user.lastname, "");
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"]((ctx.user == null ? null : ctx.user.mainAuthority) === "ROLE_PHARMACIEN" ? "PHARMACIEN" : "AIDE PHARMACIE");
    }
  },
  dependencies: [_ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonAvatar, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonBadge, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonItem, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonLabel, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonList, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonText, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: hidden !important;\n  --overflow: hidden !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 80px;\n  --border: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 22px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #101010;\n  padding-right: 1rem;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-button[slot=icon-only][_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\n.profile-avatar[_ngcontent-%COMP%] {\n  width: 120px;\n  height: 120px;\n  margin: 20px auto;\n}\n\n.user-name[_ngcontent-%COMP%] {\n  text-align: center;\n  margin: 10px 0 5px;\n  font-weight: bold;\n}\n\n.user-email[_ngcontent-%COMP%] {\n  text-align: center;\n  margin: 0 0 20px;\n  color: var(--ion-color-medium);\n}\n\nion-list[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  border: 1px solid #ece7e7;\n}\n\n\n\nion-list[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 8px;\n  margin: 12px;\n  margin-top: 50px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.04);\n  padding: 4px 0;\n}\n\n\n\nion-item[_ngcontent-%COMP%] {\n  --background: transparent;\n  --padding-start: 12px;\n  --padding-end: 8px;\n  --min-height: 50px;\n  --border-color: transparent;\n  --inner-padding-end: 8px;\n  \n\n  transition: all 0.2s ease;\n}\n\n\n\nion-item[_ngcontent-%COMP%]:hover {\n  --background: #fafafa;\n}\n\n\n\nion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  color: #4b5563;\n  font-size: 18px;\n  margin: 0;\n  padding-right: 10px;\n  transition: color 0.2s ease;\n}\n\n\n\nion-item[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\n  color: #3880ff;\n}\n\n\n\nion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\n  color: #374151;\n  font-weight: 500;\n  font-size: 14px;\n  margin: 0;\n  padding: 8px 0;\n}\n\n\n\nion-item[_ngcontent-%COMP%]::part(detail-icon) {\n  color: #9ca3af;\n  opacity: 0.6;\n  font-size: 14px;\n}\n\n\n\nion-item[_ngcontent-%COMP%]:active {\n  --background: #f5f5f5;\n}\n\n\n\nion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\n  --border-width-bottom: 1;\n  --border-color:rgb(231, 230, 230);\n}\n\n  .custom-alert {\n  --max-width: 90%;\n  --width: 100%;\n}\n  .custom-alert .alert-head,   .custom-alert #alert-1-msg .about-content p,   .custom-alert #alert-1-msg .about-content ul li,   .custom-alert #alert-1-msg .about-content h4,   .custom-alert .alert-2-head,   .custom-alert #alert-2-msg .privacy-content p,   .custom-alert #alert-2-msg .privacy-content ul li {\n  text-align: left !important;\n}\n  .custom-alert .about-content {\n  text-align: left !important;\n}\n  .custom-alert .privacy-content {\n  text-align: left !important;\n}\n  .custom-alert .alert-wrapper {\n  max-width: 90vw;\n}\n  .custom-alert .alert-message {\n  max-height: 70vh;\n  overflow-y: auto;\n  padding: 16px;\n}\n  .custom-alert .about-content h4,   .custom-alert .privacy-content h4 {\n  color: #2f4fcd;\n  margin: 16px 0 8px;\n  font-size: 16px;\n  font-weight: 600;\n}\n  .custom-alert .about-content p,   .custom-alert .privacy-content p {\n  margin: 8px 0;\n  font-size: 14px;\n  line-height: 1.5;\n  color: #4a4a4a;\n}\n  .custom-alert .about-content ul,   .custom-alert .privacy-content ul {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n  .custom-alert .about-content ul li,   .custom-alert .privacy-content ul li {\n  margin: 4px 0;\n  font-size: 14px;\n  color: #4a4a4a;\n}\n  .custom-alert .about-content .privacy-link,   .custom-alert .privacy-content .privacy-link {\n  display: block;\n  margin-top: 16px;\n  color: #2f4fcd;\n  text-decoration: underline;\n  cursor: pointer;\n  font-size: 13px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 29885:
/*!******************************************!*\
  !*** ./src/app/services/user.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserService: () => (/* binding */ UserService)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api.service */ 3366);
var _UserService;




class UserService {
  constructor(alertController, apiService, navCtrl) {
    this.alertController = alertController;
    this.apiService = apiService;
    this.navCtrl = navCtrl;
    this.user = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.signal)(null);
    this.tenant = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.signal)(null);
    this.USER_TOKEN_KEY = 'tokenUser';
    this.TENANT_TOKEN_KEY = 'tokenTenant';
    this.loadAuth();
  }
  loadAuth() {
    const currentUser = this.getCurrentUser();
    const currentTenant = this.getCurrentTenant();
    this.user.set(currentUser);
    this.tenant.set(currentTenant);
  }
  getCurrentUser() {
    const currentUser = localStorage.getItem(this.USER_TOKEN_KEY);
    const parsedUser = this.parseUser(currentUser);
    this.user.set(parsedUser);
    return parsedUser;
  }
  getCurrentTenant() {
    const tenant = localStorage.getItem(this.TENANT_TOKEN_KEY);
    const parsedUser = this.parseUser(tenant);
    return parsedUser;
  }
  parseUser(user) {
    try {
      return JSON.parse(user);
    } catch (e) {
      this.alertController.create({
        animated: true,
        header: 'une erreur est survenue',
        message: 'on ne peut pas récupérer les informations de l\'utilisateur, Merci de vous reconnecter',
        buttons: [{
          text: 'OK',
          handler: () => {
            localStorage.removeItem('tokenUser');
            localStorage.removeItem('tokenTenant');
            localStorage.removeItem('token');
            localStorage.removeItem('ocrMode');
            localStorage.removeItem('forceSupplierGlobal');
            localStorage.removeItem('selectedSupplier');
            this.navCtrl.navigateRoot('/login');
          }
        }]
      }).then(alert => alert.present());
      return null;
    }
  }
}
_UserService = UserService;
_UserService.ɵfac = function UserService_Factory(t) {
  return new (t || _UserService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_2__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_api_service__WEBPACK_IMPORTED_MODULE_0__.ApiService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.NavController));
};
_UserService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
  token: _UserService,
  factory: _UserService.ɵfac,
  providedIn: 'root'
});

/***/ })

}]);
//# sourceMappingURL=src_app_profile_profile_module_ts.js.map