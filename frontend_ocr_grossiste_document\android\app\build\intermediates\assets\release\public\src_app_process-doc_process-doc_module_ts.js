"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_process-doc_process-doc_module_ts"],{

/***/ 36474:
/*!***********************************************************!*\
  !*** ./src/app/process-doc/process-doc-routing.module.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProcessDocPageRoutingModule: () => (/* binding */ ProcessDocPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _process_doc_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./process-doc.page */ 13804);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _ProcessDocPageRoutingModule;




const routes = [{
  path: '',
  component: _process_doc_page__WEBPACK_IMPORTED_MODULE_0__.ProcessDocPage
}];
class ProcessDocPageRoutingModule {}
_ProcessDocPageRoutingModule = ProcessDocPageRoutingModule;
_ProcessDocPageRoutingModule.ɵfac = function ProcessDocPageRoutingModule_Factory(t) {
  return new (t || _ProcessDocPageRoutingModule)();
};
_ProcessDocPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _ProcessDocPageRoutingModule
});
_ProcessDocPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](ProcessDocPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 62787:
/*!***************************************************!*\
  !*** ./src/app/process-doc/process-doc.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProcessDocPageModule: () => (/* binding */ ProcessDocPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _process_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./process-doc-routing.module */ 36474);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _process_doc_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./process-doc.page */ 13804);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _ProcessDocPageModule;




 // Import SharedModule


class ProcessDocPageModule {}
_ProcessDocPageModule = ProcessDocPageModule;
_ProcessDocPageModule.ɵfac = function ProcessDocPageModule_Factory(t) {
  return new (t || _ProcessDocPageModule)();
};
_ProcessDocPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _ProcessDocPageModule
});
_ProcessDocPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _process_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__.ProcessDocPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](ProcessDocPageModule, {
    declarations: [_process_doc_page__WEBPACK_IMPORTED_MODULE_2__.ProcessDocPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _process_doc_routing_module__WEBPACK_IMPORTED_MODULE_0__.ProcessDocPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_1__.SharedModule]
  });
})();

/***/ }),

/***/ 13804:
/*!*************************************************!*\
  !*** ./src/app/process-doc/process-doc.page.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProcessDocPage: () => (/* binding */ ProcessDocPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _services_signal_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/signal.service */ 56658);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/environments/environment */ 45312);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api.service */ 3366);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../custom-icon/custom-icon.component */ 40816);

var _ProcessDocPage;











const _c0 = ["swiper_docs"];
const _c1 = () => ["/scan-bl"];
const _c2 = (a0, a1, a2) => ({
  "filter-animation": true,
  "d-block-img": a0,
  "hide-animation": a1,
  "scanning": a2
});
function ProcessDocPage_ion_select_option_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "ion-select-option", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", option_r2.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](option_r2.label);
  }
}
function ProcessDocPage_swiper_slide_13_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 31);
  }
}
function ProcessDocPage_swiper_slide_13_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "ion-spinner");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ProcessDocPage_swiper_slide_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "swiper-slide", 21)(1, "div", 22)(2, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ProcessDocPage_swiper_slide_13_Template_div_click_2_listener() {
      const i_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r3).index;
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r4.removeDoc(i_r4));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "ion-icon", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](5, "img", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](7, ProcessDocPage_swiper_slide_13_div_7_Template, 1, 0, "div", 28)(8, ProcessDocPage_swiper_slide_13_div_8_Template, 2, 0, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "img", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("load", function ProcessDocPage_swiper_slide_13_Template_img_load_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r3);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r4.onFilteredImageLoad());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const item_r6 = ctx.$implicit;
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", item_r6.cropped_image, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction3"](6, _c2, ctx_r4.isScanning, ctx_r4.hideAnimation, ctx_r4.isScanning));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r4.isScanning);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r4.filteredImageLoaded);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", item_r6.filtered_image, _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("hidden", !ctx_r4.filteredImageLoaded);
  }
}
class ProcessDocPage {
  constructor(route, location, alertController, cd, apiService) {
    this.route = route;
    this.location = location;
    this.alertController = alertController;
    this.cd = cd;
    this.apiService = apiService;
    this.isScanning = false;
    this.hideAnimation = false;
    this.selectedSupplier = '';
    this.croppedImage = '';
    this.filteredImage = '';
    this.forceSupplierGlobal = false; // force the supplier to be global for apply the Advanced OCR 'Mindee'
    this.filteredImageLoaded = false;
    this.suppliers = []; // Initialize suppliers as an empty array
    this.listDocs = [];
    this.navCtrl = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_ionic_angular__WEBPACK_IMPORTED_MODULE_6__.NavController);
    this.signalService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_5__.inject)(_services_signal_service__WEBPACK_IMPORTED_MODULE_1__.SignalService);
    this.swiperClass = src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.platform === 'web' ? 'swiper-small' : 'swiper-large';
  }
  ngOnInit() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      var _localStorage$getItem;
      yield _this.redirectToScanBL();
      // if the value is equal 'GLOBAL' then change it to 'AUTRE'
      _this.suppliers.forEach(supplier => {
        if (supplier.value === 'GLOBAL') {
          supplier.value = 'AUTRE';
        }
      });
      const params = _this.location.getState();
      _this.data = params;
      console.log('Received data:', _this.data);
      setTimeout( /*#__PURE__*/(0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
        yield _this.getAllSuppliers();
      }), 200);
      _this.listDocs = _this.signalService.getData();
      _this.selectedSupplier = (_localStorage$getItem = localStorage.getItem('selectedSupplier')) !== null && _localStorage$getItem !== void 0 ? _localStorage$getItem : '';
      _this.cd.detectChanges(); // Add this line
      // Check if it's the first entry
      if (_this.signalService.firstEntry) {
        if (_this.data.supplier_name && _this.data.supplier_name != 'Unknown') {
          _this.selectedSupplier = _this.data.supplier_name.toUpperCase();
          localStorage.setItem('selectedSupplier', _this.selectedSupplier);
        } else if (_this.selectedSupplier == '' || !_this.selectedSupplier || _this.selectedSupplier == 'UNKNOWN') {
          // conseil the user to take a other photo if the supplier is unknown
          const alert = yield _this.alertController.create({
            header: 'Attention !',
            message: `Le fournisseur n'a pas été reconnu !. Veuillez prendre une autre photo.`,
            buttons: [{
              text: 'Non',
              role: 'cancel',
              cssClass: 'custom-alert-button cancel',
              handler: () => {
                var _localStorage$getItem2;
                _this.selectedSupplier = (_localStorage$getItem2 = localStorage.getItem('selectedSupplier')) !== null && _localStorage$getItem2 !== void 0 ? _localStorage$getItem2 : '';
                console.log('Confirm Cancel');
              }
            }, {
              text: 'Oui',
              cssClass: 'custom-alert-button danger',
              handler: () => {
                var _this$swiper_docs, _this$swiper_docs2;
                const index_currect = (_this$swiper_docs = _this.swiper_docs) === null || _this$swiper_docs === void 0 || (_this$swiper_docs = _this$swiper_docs.nativeElement.swiper) === null || _this$swiper_docs === void 0 ? void 0 : _this$swiper_docs.activeIndex;
                (_this$swiper_docs2 = _this.swiper_docs) === null || _this$swiper_docs2 === void 0 || (_this$swiper_docs2 = _this$swiper_docs2.nativeElement.swiper) === null || _this$swiper_docs2 === void 0 || _this$swiper_docs2.removeSlide(index_currect);
                _this.signalService.removeData(index_currect);
                _this.listDocs = _this.signalService.getData();
                _this.navCtrl.navigateRoot('/scan-bl');
              }
            }]
          });
          // force the supplier to be global for apply the Advanced OCR 'Mindee'
          _this.forceSupplierGlobal = true;
          // set it in the local storage
          localStorage.setItem('forceSupplierGlobal', _this.forceSupplierGlobal.toString());
          yield alert.present();
        } else {
          _this.selectedSupplier = '';
        }
      } else {
        // Check if the supplier came from the backend not the same that the user selected
        if (_this.data.supplier_name && _this.data.supplier_name != 'Unknown' && _this.data.supplier_name.toUpperCase() != _this.selectedSupplier.toUpperCase() && _this.listDocs.length > 0) {
          // alert prompt for attention the user that if change supplier value for this document then all documents will be updated
          const alert = yield _this.alertController.create({
            header: 'Attention !',
            message: '',
            buttons: [{
              text: 'Annuler',
              role: 'cancel',
              cssClass: 'custom-alert-button cancel',
              handler: () => {
                var _localStorage$getItem3;
                console.log('Confirm Cancel');
                // set the selected supplier to the previous value
                _this.selectedSupplier = (_localStorage$getItem3 = localStorage.getItem('selectedSupplier')) !== null && _localStorage$getItem3 !== void 0 ? _localStorage$getItem3 : '';
              }
            }, {
              text: 'Oui, Mettre à jour !',
              cssClass: 'custom-alert-button warning',
              handler: () => {
                console.log('Selected Supplier backend:', _this.data.supplier_name);
                console.log('Selected Supplier select:', _this.selectedSupplier);
                _this.selectedSupplier = _this.data.supplier_name.toUpperCase();
                localStorage.setItem('selectedSupplier', _this.data.supplier_name.toUpperCase());
                console.log('Slide data:', _this.data);
              }
            }]
          });
          yield alert.present();
          // Use a query selector to set the innerHTML of the message
          const alertMessage = document.querySelector('.alert-message');
          if (alertMessage) {
            alertMessage.innerHTML = `Le nom du fournisseur <b>'${_this.selectedSupplier}'</b> ne correspond pas au modèle attendu <b>'${_this.data.supplier_name.toUpperCase()}'</b>. Voulez-vous mettre à jour le fournisseur de ce document ?`;
          }
        }
      }
      setTimeout(() => {
        _this.applyFilter();
        if (localStorage.getItem('selectedSupplier') != null) {
          var _localStorage$getItem4;
          console.log('Selected Supplier:', localStorage.getItem('selectedSupplier'));
          _this.selectedSupplier = (_localStorage$getItem4 = localStorage.getItem('selectedSupplier')) !== null && _localStorage$getItem4 !== void 0 ? _localStorage$getItem4 : '';
        }
      }, 1000);
    })();
  }
  getAllSuppliers() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // Fetch suppliers from the API
      _this2.apiService.getAllSuppliers().subscribe(suppliers => {
        _this2.suppliers = suppliers.map(supplier => {
          if (supplier.value === 'GLOBAL') {
            return {
              value: 'AUTRE',
              label: 'AUTRE'
            };
          }
          // Replace underscores with spaces in supplier.value
          if (supplier.value.includes('_')) {
            return {
              ...supplier,
              value: supplier.value,
              label: supplier.label.replace(/_/g, ' ')
            };
          }
          return supplier;
        });
        // Move "AUTRE" to the end
        _this2.suppliers = _this2.suppliers.sort((a, b) => a.value === 'AUTRE' ? 1 : b.value === 'AUTRE' ? -1 : 0);
      }, error => {
        console.error('Error fetching suppliers:', error);
      });
    })();
  }
  redirectToScanBL() {
    var _this3 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this3.signalService.getData() == null || _this3.signalService.getData() == undefined || _this3.signalService.getData().length == 0) {
        _this3.navCtrl.navigateRoot('/scan-bl');
      }
    })();
  }
  applyFilter() {
    this.isScanning = !this.isScanning;
    if (this.isScanning) {
      setTimeout(() => {
        this.hideAnimation = true;
      }, 2000);
      this.hideAnimation = false;
    }
  }
  isValidBlobUrl(blobUrl) {
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        const response = yield fetch(blobUrl);
        return response.ok; // Returns true if the response is ok (status is in the range 200-299)
      } catch (error) {
        return false; // Returns false if there is an error (e.g., network issue, invalid URL)
      }
    })();
  }
  onSlideChange() {
    console.log('changedslidechange');
    this.initSwipers();
  }
  initSwipers() {
    if (this.swiper_docs && this.swiper_docs.nativeElement) {
      const swiper_docsInstance = this.swiper_docs.nativeElement.swiper;
    }
  }
  removeDoc(index) {
    var _this4 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this4.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer ce document ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            var _this4$swiper_docs;
            _this4.signalService.removeData(index);
            _this4.listDocs = _this4.signalService.getData();
            (_this4$swiper_docs = _this4.swiper_docs) === null || _this4$swiper_docs === void 0 || (_this4$swiper_docs = _this4$swiper_docs.nativeElement.swiper) === null || _this4$swiper_docs === void 0 || _this4$swiper_docs.removeSlide(index);
            if (_this4.signalService.getData().length == 0) {
              localStorage.removeItem('selectedSupplier');
              _this4.navCtrl.navigateRoot('/scan-bl');
            }
          }
        }]
      });
      yield alert.present();
    })();
  }
  removeAllDoc() {
    var _this5 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this5.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui, Supprimer !',
          cssClass: 'custom-alert-button danger',
          handler: () => {
            var _this5$swiper_docs;
            _this5.signalService.removeAllData();
            _this5.listDocs = [];
            (_this5$swiper_docs = _this5.swiper_docs) === null || _this5$swiper_docs === void 0 || (_this5$swiper_docs = _this5$swiper_docs.nativeElement.swiper) === null || _this5$swiper_docs === void 0 || _this5$swiper_docs.removeAllSlides();
            localStorage.removeItem('selectedSupplier');
            // redirect to scan-bl
            _this5.navCtrl.navigateRoot('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  reTakePhoto() {
    var _this6 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const alert = yield _this6.alertController.create({
        header: 'Supprimer le document',
        message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,
        buttons: [{
          text: 'Annuler',
          role: 'cancel',
          cssClass: 'custom-alert-button cancel',
          handler: () => {
            console.log('Confirm Cancel');
          }
        }, {
          text: 'Oui',
          cssClass: 'custom-alert-button',
          handler: () => {
            var _this6$swiper_docs, _this6$swiper_docs2;
            const index_currect = (_this6$swiper_docs = _this6.swiper_docs) === null || _this6$swiper_docs === void 0 || (_this6$swiper_docs = _this6$swiper_docs.nativeElement.swiper) === null || _this6$swiper_docs === void 0 ? void 0 : _this6$swiper_docs.activeIndex;
            // remove currect slide
            (_this6$swiper_docs2 = _this6.swiper_docs) === null || _this6$swiper_docs2 === void 0 || (_this6$swiper_docs2 = _this6$swiper_docs2.nativeElement.swiper) === null || _this6$swiper_docs2 === void 0 || _this6$swiper_docs2.removeSlide(index_currect);
            // remove data
            _this6.signalService.removeData(index_currect);
            _this6.listDocs = _this6.signalService.getData();
            // redirect to scan-bl
            _this6.navCtrl.navigateRoot('/scan-bl');
          }
        }]
      });
      yield alert.present();
    })();
  }
  onSupplierChange($event) {
    var _this7 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Selected Supplier:', $event.detail.value);
      if ($event.detail.value != localStorage.getItem('selectedSupplier')) {
        // alert prompt for attention the user that if change supplier value for this document then all documents will be updated
        const alert = yield _this7.alertController.create({
          header: 'Attention !',
          message: `Êtes-vous sûr de vouloir changer le fournisseur ? Tous les documents seront mis à jour avec le nouveau fournisseur.`,
          buttons: [{
            text: 'Annuler',
            role: 'cancel',
            cssClass: 'custom-alert-button cancel',
            handler: () => {
              var _localStorage$getItem5;
              console.log('Confirm Cancel');
              // set the selected supplier to the previous value
              _this7.selectedSupplier = (_localStorage$getItem5 = localStorage.getItem('selectedSupplier')) !== null && _localStorage$getItem5 !== void 0 ? _localStorage$getItem5 : '';
            }
          }, {
            text: 'Oui, Changer !',
            cssClass: 'custom-alert-button warning',
            handler: () => {
              _this7.selectedSupplier = $event.detail.value;
              _this7.listDocs.forEach(doc => {
                doc.supplier_name = $event.detail.value;
              });
              console.log('Slide data:', _this7.listDocs);
              localStorage.setItem('selectedSupplier', $event.detail.value == 'AUTRE' ? 'GLOBAL' : $event.detail.value);
              console.log('Selected Supplier:', $event.detail.value);
            }
          }]
        });
        yield alert.present();
      }
    })();
  }
  valider() {
    var _this8 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // check if selectedSupplier not empty then redirect to  [routerLink]="['/doc-list']" else show alert message
      if (_this8.selectedSupplier != '') {
        _this8.navCtrl.navigateRoot('/doc-list');
      } else {
        const alert = yield _this8.alertController.create({
          header: 'Attention !',
          message: `Veuillez sélectionner un fournisseur`,
          buttons: ['OK']
        });
        yield alert.present();
      }
    })();
  }
  compressImage(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = event => {
        var _event$target;
        const img = new Image();
        img.src = (_event$target = event.target) === null || _event$target === void 0 ? void 0 : _event$target.result;
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const MAX_WIDTH = 1024;
          const MAX_HEIGHT = 1024;
          let width = img.width;
          let height = img.height;
          if (width > height && width > MAX_WIDTH) {
            height = height * MAX_WIDTH / width;
            width = MAX_WIDTH;
          } else if (height > width && height > MAX_HEIGHT) {
            width = width * MAX_HEIGHT / height;
            height = MAX_HEIGHT;
          }
          canvas.width = width;
          canvas.height = height;
          ctx === null || ctx === void 0 || ctx.drawImage(img, 0, 0, width, height);
          resolve(canvas.toDataURL('image/jpeg', 0.8));
        };
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
  preloadImage(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;
      img.onload = () => resolve();
      img.onerror = err => reject(err);
    });
  }
  showFilteredImage(item) {
    var _this9 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        if (item.filtered_image) {
          yield _this9.preloadImage(item.filtered_image);
          _this9.isScanning = true;
        } else {
          console.error("L'image filtrée n'est pas définie");
        }
      } catch (error) {
        console.error("Impossible de charger l'image filtrée", error);
      }
    })();
  }
  onFilteredImageLoad() {
    this.filteredImageLoaded = true;
  }
  cacheImage(key, imageUrl) {
    localStorage.setItem(key, imageUrl);
  }
  getCachedImage(key) {
    return localStorage.getItem(key);
  }
  scan_bl() {
    this.navCtrl.navigateRoot('/scan-bl');
  }
}
_ProcessDocPage = ProcessDocPage;
_ProcessDocPage.ɵfac = function ProcessDocPage_Factory(t) {
  return new (t || _ProcessDocPage)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_8__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_9__.AlertController), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_5__.ChangeDetectorRef), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_api_service__WEBPACK_IMPORTED_MODULE_3__.ApiService));
};
_ProcessDocPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
  type: _ProcessDocPage,
  selectors: [["app-process-doc"]],
  viewQuery: function ProcessDocPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 7);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.swiper_docs = _t.first);
    }
  },
  decls: 33,
  vars: 5,
  consts: [["swiper_docs", ""], ["slot", "end"], [3, "click"], ["name", "re-scan"], [1, "process-doc-content"], [1, "select-supplier-wrapper"], ["label", "S\u00E9lection du fournisseur", "label-placement", "floating", "fill", "outline", 3, "ngModelChange", "ionChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], ["effect", "coverflow", "grab-cursor", "true", "centered-slides", "false", "slides-per-view", "auto", "coverflow-effect-rotate", "50", "coverflow-effect-stretch", "0", "coverflow-effect-depth", "100", "coverflow-effect-modifier", "1", "coverflow-effect-slide-shadows", "true", 1, "swiper", "top-swiper", 3, "swiperslidechange"], ["class", "swiper-slide", 4, "ngFor", "ngForOf"], [1, "swiper-slide", "last-swiper-slide-scan", 3, "routerLink"], [1, "scan-bl-wrapper"], ["name", "file-import", 1, "file-import-icon"], [1, "content"], ["size", "small", 1, "menu-button", 3, "click"], ["name", "delete"], [1, "menu-button-middle", 3, "click"], ["name", "extract"], ["size", "small", 1, "menu-button", "active", 3, "click"], ["name", "re-scan-2"], [3, "value"], [1, "swiper-slide"], [1, "process-doc-wrapper"], [1, "remove-doc", 3, "click"], ["name", "close-circle-sharp"], [1, "image-container"], ["id", "unfilteredImage", "alt", "Unfiltered Document", "loading", "lazy", 3, "src"], [3, "ngClass"], ["class", "scan-line", 4, "ngIf"], ["class", "loading-placeholder", 4, "ngIf"], ["alt", "Filtered Document", 3, "load", "src", "hidden"], [1, "scan-line"], [1, "loading-placeholder"]],
  template: function ProcessDocPage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Scanner votre BL");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "ion-buttons", 1)(5, "ion-button", 2);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ProcessDocPage_Template_ion_button_click_5_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.scan_bl());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](6, "app-custom-icon", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "ion-content", 4)(8, "div", 5)(9, "ion-select", 6);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayListener"]("ngModelChange", function ProcessDocPage_Template_ion_select_ngModelChange_9_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayBindingSet"](ctx.selectedSupplier, $event) || (ctx.selectedSupplier = $event);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"]($event);
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ionChange", function ProcessDocPage_Template_ion_select_ionChange_9_listener($event) {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.onSupplierChange($event));
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](10, ProcessDocPage_ion_select_option_10_Template, 2, 2, "ion-select-option", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "swiper-container", 8, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("swiperslidechange", function ProcessDocPage_Template_swiper_container_swiperslidechange_11_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.onSlideChange());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](13, ProcessDocPage_swiper_slide_13_Template, 10, 10, "swiper-slide", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "swiper-slide", 10)(15, "div", 11);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](16, "app-custom-icon", 12);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "div", 13)(18, "h2");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](19, "Ajouter une autre page");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](20, "p");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](21, "La page doit \u00EAtre un m\u00EAme Bon Livraison");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "ion-footer")(23, "ion-toolbar")(24, "ion-buttons")(25, "ion-button", 14);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ProcessDocPage_Template_ion_button_click_25_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.removeAllDoc());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](26, "app-custom-icon", 15);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](27, "ion-button", 16);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ProcessDocPage_Template_ion_button_click_27_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.valider());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](28, "app-custom-icon", 17);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](29, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](30, "VALIDER");
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](31, "ion-button", 18);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ProcessDocPage_Template_ion_button_click_31_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx.reTakePhoto());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](32, "app-custom-icon", 19);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtwoWayProperty"]("ngModel", ctx.selectedSupplier);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.suppliers);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx.listDocs);
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpureFunction0"](4, _c1));
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgModel, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonButtons, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonSelect, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonSelectOption, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonSpinner, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.IonToolbar, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.SelectValueAccessor, _ionic_angular__WEBPACK_IMPORTED_MODULE_9__.RouterLinkDelegate, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink, _custom_icon_custom_icon_component__WEBPACK_IMPORTED_MODULE_4__.CustomIconComponent],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];@charset \"UTF-8\";\n*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: hidden !important;\n  --overflow: hidden !important;\n}\n\nion-header[_ngcontent-%COMP%] {\n  height: 80px;\n  --border: 0;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  height: 100%;\n  --border: 0;\n  --border-width: 0;\n  display: flex;\n  justify-content: center;\n  align-items: flex-end;\n  flex-direction: row;\n}\n\nion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%] {\n  font-size: 26px;\n  font-weight: 700;\n  color: #2f4fcd;\n  text-align: left;\n  width: 100%;\n  padding-left: 2rem;\n}\n\n  ion-header ion-toolbar app-custom-icon img {\n  width: 40px !important;\n  height: 40px !important;\n  margin-right: 5px !important;\n}\n\n.process-doc-content[_ngcontent-%COMP%] {\n  background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n  background-size: cover;\n}\n\n.process-doc-wrapper[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.process-doc-content[_ngcontent-%COMP%]   .select-supplier-wrapper[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-grow: 1;\n}\n.process-doc-content[_ngcontent-%COMP%]   .select-supplier-wrapper[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%] {\n  width: 60%;\n  padding: 0 10px;\n  background: #fff;\n  border-radius: 10px;\n  box-shadow: 0px 6px 10px rgba(0, 0, 0, 0.2); \n\n}\n.process-doc-content[_ngcontent-%COMP%]   .select-supplier-wrapper[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]::part(text) {\n  text-align: left;\n}\n\n.process-doc-wrapper[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\n  flex-grow: 8;\n  margin-top: 1rem;\n}\n\n  ion-alert button[aria-checked=true] .alert-button-inner {\n  background-color: rgba(49, 81, 207, 0.8);\n  border-bottom: 4px solid #E98862;\n}\n  ion-alert button[aria-checked=true] .alert-button-inner .alert-radio-label {\n  color: #fff !important;\n}\n  ion-alert button[aria-checked=true] .alert-button-inner .alert-radio-icon .alert-radio-inner {\n  border-color: #fff !important;\n}\n\n  ion-select {\n  --border-width: 0px !important;\n}\n\n  .process-doc-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .process-doc-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.process-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 50px 0 50px;\n}\n\n.process-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.process-doc-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n}\n\n.process-doc-content[_ngcontent-%COMP%] {\n  --offset-top: -10px !important;\n  --background: rgba(0, 0, 0, 0.05);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 100px;\n  color: #c4c4c4;\n}\n\nh2[_ngcontent-%COMP%] {\n  color: #555555;\n  font-size: 18px;\n  margin-top: 20px;\n}\n\np[_ngcontent-%COMP%] {\n  color: #888888;\n  font-size: 14px;\n  margin-top: 10px;\n  margin-bottom: 30px;\n}\n\n.arrow-icon[_ngcontent-%COMP%] {\n  font-size: 30px;\n  color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-evenly;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 160px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 35px !important;\n  height: 35px !important;\n  color: #fff;\n}\n  .menu-button-middle span {\n  color: #fff;\n  font-weight: 500;\n  font-size: 16px;\n  padding-left: 10px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-fab[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  ion-fab-button.menu-button-middle::part(native) {\n  background: none;\n  border: 0;\n  box-shadow: none;\n  width: 100% !important;\n  color: #fff;\n}\n\n  ion-fab ion-fab-list {\n  display: flex;\n  flex-direction: row !important;\n  justify-content: space-around;\n  align-items: flex-end;\n  width: auto !important;\n  padding: 10px 20px;\n  margin-bottom: 100px;\n  height: 100vh;\n  transition: all 0.3s ease;\n}\n  ion-fab ion-fab-list.fab-active {\n  visibility: visible;\n  opacity: 1;\n  pointer-events: all;\n}\n  ion-fab ion-fab-list.fab-hidden {\n  visibility: hidden;\n  opacity: 0;\n  pointer-events: none;\n}\n\n.bg-hide[_ngcontent-%COMP%] {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.initial-bg[_ngcontent-%COMP%] {\n  --background: none;\n}\n\n.content-fab-buttom[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n\n  .content-fab-buttom app-custom-icon img {\n  width: 50px !important;\n  height: 50px !important;\n}\n\n  .content-fab-buttom ion-label {\n  color: #2f4fcd;\n  font-size: 20px;\n}\n\n  ion-fab ion-fab-list ion-fab-button {\n  padding: 10px;\n  width: 200px;\n  height: 135px;\n  transform: translateY(40px);\n  transition: all 0.6s ease-in-out;\n}\n  ion-fab ion-fab-list ion-fab-button.fab-active {\n  transform: translateY(0);\n}\n\n  ion-fab ion-fab-list ion-fab-button::part(native) {\n  border-radius: 16px !important;\n}\n\n\n\n.swiper[_ngcontent-%COMP%] {\n  height: 60vh;\n}\n.swiper.swiper-large[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.swiper.swiper-small[_ngcontent-%COMP%] {\n  width: 25%;\n}\n\n\n\n.swiper[_ngcontent-%COMP%]   .swiper-slide[_ngcontent-%COMP%] {\n  background-position: center;\n  background-size: cover;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.swiper[_ngcontent-%COMP%]   .swiper-slide.swiper-slide-active[_ngcontent-%COMP%]   .process-doc-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n}\n.swiper[_ngcontent-%COMP%]   .swiper-slide.swiper-slide-active[_ngcontent-%COMP%]   .process-doc-wrapper[_ngcontent-%COMP%]   .remove-doc[_ngcontent-%COMP%] {\n  position: absolute;\n  border-radius: 50%;\n  top: 0;\n  left: 0;\n  transition: all 0.3s ease;\n  z-index: 1;\n  cursor: pointer;\n  font-weight: bold;\n}\n.swiper[_ngcontent-%COMP%]   .swiper-slide.swiper-slide-active[_ngcontent-%COMP%]   .process-doc-wrapper[_ngcontent-%COMP%]   .remove-doc[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\n  font-size: 35px;\n  color: #1e2531;\n}\n\n  .swiper .swiper-slide-active .swiper-slide-shadow,   .swiper .swiper-slide-active .swiper-slide-shadow-left,   .swiper .swiper-slide-active .swiper-slide-shadow-right {\n  background: none !important;\n}\n\n  .swiper .swiper-slide-active .process-doc-wrapper {\n  width: 85%;\n}\n\n  .swiper .swiper-slide.swiper-slide-active {\n  transform: translate3d(0px, 0px, 0) rotateX(0deg) rotateY(0) scale(1) !important;\n}\n  .swiper .swiper-slide.swiper-slide-active .image-container {\n  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);\n}\n\n  .swiper .swiper-slide:not(.swiper-slide-active) {\n  transform: translate3d(0px, 0px, 0) rotateX(0deg) rotateY(0) scale(0.7) !important;\n  top: 20px;\n}\n  .swiper .swiper-slide:not(.swiper-slide-active) .image-container {\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.8);\n}\n\n  .swiper .swiper-slide.swiper-slide-prev {\n  left: 110px;\n}\n\n  .swiper .swiper-slide.swiper-slide-next {\n  right: 110px;\n}\n\n  .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow,   .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow-left,   .swiper .swiper-slide:not(.swiper-slide-active) .swiper-slide-shadow-right {\n  background: none !important;\n}\n\n  .swiper .swiper-slide.last-swiper-slide-scan {\n  height: auto;\n  margin-top: 1rem;\n}\n  .swiper .swiper-slide.last-swiper-slide-scan .scan-bl-wrapper {\n  height: 100%;\n  width: 80%;\n  margin-left: 1rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n  background-image: url(\"data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='30' ry='30' stroke='%23333' stroke-width='2' stroke-dasharray='10%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e\");\n  border-radius: 30px;\n}\n\n  .scan-bl-wrapper .file-import-icon img {\n  width: 150px !important;\n  height: 150px !important;\n}\n\n  .scan-bl-wrapper .arrow-bottom-icon img {\n  width: 100px !important;\n  height: 100px !important;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  padding: 10px 10px 0 10px;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 22px;\n  font-weight: 700;\n}\n\n.scan-bl-wrapper[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #9a9a9a;\n  font-size: 12px;\n  text-align: justify;\n  padding: 5px 10px;\n  text-align: center;\n}\n\n  .custom-alert-button, custom-alert-button-rename-doc[_ngcontent-%COMP%] {\n  display: inline-block;\n  text-align: center;\n  font-size: 14px !important;\n  font-weight: bold;\n}\n\n  .custom-alert-button.cancel,   .custom-alert-button-rename-doc.cancel {\n  color: #2563eb;\n  width: 48%;\n}\n\n  .custom-alert-button-rename-doc.cancel {\n  font-size: 16px;\n}\n\n  .custom-alert-button.danger {\n  color: red;\n  width: 50%;\n}\n\n  .custom-alert-button.warning {\n  color: #f6ad55;\n  font-size: 13px;\n  width: 50%;\n}\n\n  .custom-alert-button-rename-doc.rename {\n  font-size: 16px;\n  color: #535353;\n}\n\n\n\n.image-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n  width: 90%;\n}\n\n#unfilteredImage[_ngcontent-%COMP%] {\n  width: 100%;\n  display: block;\n}\n\n.filter-animation[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  overflow: hidden;\n  pointer-events: none;\n}\n\n.filter-animation[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  display: none;\n}\n\n.filter-animation.d-block-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  display: block;\n}\n\n@keyframes _ngcontent-%COMP%_scanEffect {\n  from {\n    clip-path: inset(0 0 100% 0);\n  }\n  to {\n    clip-path: inset(0 0 0 0);\n  }\n}\n.scanning[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_scanEffect 2s forwards;\n}\n\n.scanning-active[_ngcontent-%COMP%] {\n  box-shadow: 0px 0px 20px 10px rgba(0, 128, 0, 0.5);\n}\n\n.scan-line[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 5px;\n  background-color: #63D0C5;\n  box-shadow: 0 0 70px 30px rgba(0, 128, 0, 0.9);\n  animation: _ngcontent-%COMP%_scanLineMove 2s forwards;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.scan-line[_ngcontent-%COMP%]::before {\n  content: \"\u25E5\u25E4\";\n  font-size: 15px;\n  color: green;\n  margin-left: -8px;\n  margin-bottom: 28px;\n  transform: rotate(-90deg);\n}\n\n.scan-line[_ngcontent-%COMP%]::after {\n  content: \"\u25E5\u25E4\";\n  font-size: 15px;\n  color: green;\n  margin-right: -8px;\n  margin-bottom: 28px;\n  transform: rotate(90deg);\n}\n\n.hide-animation[_ngcontent-%COMP%]   .scan-line[_ngcontent-%COMP%] {\n  display: none;\n}\n\n.loading-spinner[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 10;\n}\n\n.hidden[_ngcontent-%COMP%] {\n  visibility: hidden;\n}\n\n.image-container[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n@keyframes _ngcontent-%COMP%_scanLineMove {\n  from {\n    top: 0;\n  }\n  to {\n    top: 100%;\n  }\n}\n@media (prefers-color-scheme: dark) {\n  .process-doc-content[_ngcontent-%COMP%] {\n    background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n    background-size: cover;\n  }\n  ion-content[_ngcontent-%COMP%] {\n    --background: #fff ;\n  }\n  ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n    --background: #fff !important;\n  }\n}\n@media (prefers-color-scheme: light) {\n  .process-doc-content[_ngcontent-%COMP%] {\n    background: url(\"/assets/bg-scan-bl.png\") no-repeat center center fixed;\n    background-size: cover;\n  }\n  ion-content[_ngcontent-%COMP%] {\n    --background: #fff ;\n  }\n  ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n    --background: #fff !important;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_process-doc_process-doc_module_ts.js.map