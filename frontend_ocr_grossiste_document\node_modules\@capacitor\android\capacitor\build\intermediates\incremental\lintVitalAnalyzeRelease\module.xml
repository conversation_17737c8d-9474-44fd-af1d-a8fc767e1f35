<lint-module
    format="1"
    dir="C:\Users\<USER>\Downloads\Work __Abd<PERSON><PERSON>mane_ouhna\OCR_DOCUMENT_GROSSISTE\Frontend ocr grossiste document\frontend_ocr_grossiste_document\node_modules\@capacitor\android\capacitor"
    name=":capacitor-android"
    type="LIBRARY"
    maven="android:capacitor-android:"
    agpVersion="8.2.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      lintConfig="lint.xml"
      abortOnError="true"
      absolutePaths="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
