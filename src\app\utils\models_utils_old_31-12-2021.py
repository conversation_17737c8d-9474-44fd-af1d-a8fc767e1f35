import json
from datetime import datetime
from src.app.utils import constants
from collections import OrderedDict
from src.app.utils.document_model import DocumentModel
from fuzzywuzzy import process, fuzz
import logging
import traceback
from src.app.utils.helpers import *
import re

"""-----******************************    Correct Additional Info in BL    ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """


def is_additional_line(line, terms):
    try:
        words = line.split()
        first_word = correct_number(words[0])
        first_word_contain_number = contains_number(words[0])

        if not words:
            return False

        # Perform fuzzy matching with STABLE_FAMILLE_TARIFAIRE_TVA and terms
        match_count = 0
        for word in words:
            if fuzzywazzy_part_with_array_parameter(word, terms, 80):
                match_count += 1
            if match_count >= 3:
                return True

        # Check if the first word is a number
        if words[0].isdigit():
            return False

        # Check if the line contains a number less than 3 characters long
        if any(first_word_contain_number and len(words[0]) < 3 for word in words):
            return False

        # Check if the line contains any word from STABLE_FAMILLE_TARIFAIRE_TVA
        if not (check_presence(constants.STABLE_FAMILLE_TARIFAIRE_TVA, word) for word in words):
            return False

        # Check if the line contains any word from CORRECT_ADDITIONAL_BL
        if not (check_presence(terms, word) for word in words):
            return False

        # # Perform fuzzy matching with STABLE_FAMILLE_TARIFAIRE_TVA and terms
        for word in words:
            if fuzzywazzy_part_with_array_parameter(word, constants.STABLE_FAMILLE_TARIFAIRE_TVA, 80) and '%' in word:
                return True

        if not fuzzywazzy_part_with_array_parameter(words[0], terms, 80):
            return False

        return True

    except (ValueError, TypeError) as e:
        logging.error(f"Error in is_additional_line: {traceback.format_exc()}")
        raise e


def correct_additional_line(line, terms, percentage_terms, percentage_tva):
    try:
        line = line.replace(',', '.')
        line = line.replace('. ', '.')

        words = line.split()
        corrected_words = []

        for word in words:
            # Apply fuzzy matching for terms correction
            best_match_score = 0
            best_match = None
            if terms:
                best_match, match_score = process.extractOne(word, terms, scorer=fuzz.ratio)
                best_match_score = match_score if match_score is not None else 0
            if best_match_score >= percentage_terms:
                corrected_words.append(best_match)
            else:
                # Check if the word contains '%' and correct the number if needed
                if any(char.isdigit() for char in word) or '%' in word:
                    # Apply fuzzy matching for STABLE_FAMILLE_TARIFAIRE_TVA
                    best_match_tva, match_score_tva = process.extractOne(word, constants.STABLE_FAMILLE_TARIFAIRE_TVA,
                                                                         scorer=fuzz.ratio)

                    if match_score_tva >= percentage_tva:
                        corrected_words.append(best_match_tva)
                    else:
                        corrected_words.append(word)

                else:
                    corrected_words.append(word)
        return ' '.join(corrected_words)

    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_additional_line: {traceback.format_exc()}")
        raise e


def process_model_text_additional_section(model, text_lines):
    try:
        terms = constants.CORRECT_ADDITIONAL_BL.get(model, [])
        corrected_lines = []
        is_additional_section = False
        additional_section_start_index = None

        for index, line in enumerate(text_lines):
            # Check if the line contains any of the terms

            if additional_section_start_index is None:
                if is_additional_line(line, terms):
                    additional_section_start_index = index
            if additional_section_start_index:
                corrected_line = correct_additional_line(line, terms, 80, 70)
                corrected_lines.append(corrected_line)
            else:
                corrected_lines.append(line)

        return corrected_lines, additional_section_start_index

    except (ValueError, TypeError) as e:
        logging.error(f"Error in process_model_text_additional_section: {traceback.format_exc()}")
        raise e


"""-----****************************** Correct Numbers (Qty, PPV, PU, TTC, ...) ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """


def correct_chiffres_calculated(data_to_loop, model, additional_section_start_index):
    try:
        attribute_indexes = {
            'quantity_delivered': [],
            'quantity_ordered': [],
            'quantity_duplicate': [],
            'ppv': [],
            'pph': [],
            'total_ttc': [],
            'gf': [],
            'tva': [],
            'designation': [],
            'word_index': []
        }

        if additional_section_start_index is None:
            check_to_index = len(data_to_loop)
        else:
            check_to_index = additional_section_start_index

        if model == constants.SPR:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)
        elif model == constants.COOPER_PHARMA_CASA:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 7, additional_section_start_index)
        elif model == constants.GPM:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)
        elif model == constants.SOPHADIMS or model == constants.RECAMED:
            data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 2, additional_section_start_index)
        # elif model == constants.SOPHACA:
        #     data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3)

        def clean_numeric_field(value):
            # Remove leading dots, dashes, or spaces
            cleaned = re.sub(r'^[.\-\s]+', '', value)
            # Remove trailing dots or spaces
            cleaned = re.sub(r'[.\s]+$', '', cleaned)
            # Ensure there's only one decimal point
            parts = cleaned.split('.')
            if len(parts) > 2:
                cleaned = parts[0] + '.' + ''.join(parts[1:])
            return cleaned

        numeric_fields = ['quantity_delivered', 'quantity_ordered', 'quantity_duplicate', 'ppv', 'pph', 'total_ttc',
                          'gf']

        for index, line in enumerate(data_to_loop[:check_to_index]):
            parts = line.split()
            if not parts:
                continue

            selected_clean_word, word_index = select_name_root_from_line(line)
            # logging.info("selected_clean_word : ", selected_clean_word)
            # logging.info("word_index : ", word_index)
            # logging.info("line___ : ", line)
            # logging.info("index : ", index)
            # logging.info("length : ", len(data_to_loop))
            # logging.info("check_to_index : ", check_to_index)

            if model == constants.SPR:
                data_to_loop = clean_chars_ocr_lines_last_parts(data_to_loop, 3, additional_section_start_index)

                for index, line in enumerate(data_to_loop[:check_to_index]):
                    # Preprocess the line to remove leading zeros and special characters
                    line = re.sub(r'^[0\s\\]*', '', line).strip()
                    parts = line.split()
                    if not parts:
                        continue

                    # Remove '=' from the beginning of any part
                    parts = [part.lstrip('=') for part in parts]

                    selected_clean_word, word_index = select_name_root_from_line(' '.join(parts))

                    # Process Qty Ordered / Delivered
                    if word_index is not None:
                        if word_index == 0:
                            line = '0 0 ' + ' '.join(parts)
                        elif word_index == 1:
                            if len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(',')):
                                corrected_qty_delivered = correct_number(
                                    ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                                line = '0 ' + corrected_qty_delivered + ' ' + ' '.join(parts[1:])
                            else:
                                line = '0 0 ' + ' '.join(parts)
                        elif word_index >= 2:
                            if (len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(','))) and \
                                    (len(parts[1]) <= 3 or (len(parts[1]) == 4 and parts[1].endswith(','))):
                                corrected_qty_ordered = correct_number(
                                    ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                                corrected_qty_delivered = correct_number(
                                    ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[1]))
                                line = corrected_qty_ordered + ' ' + corrected_qty_delivered + ' ' + ' '.join(parts[2:])
                            elif len(parts[0]) <= 3 or (len(parts[0]) == 4 and parts[0].endswith(',')):
                                corrected_qty_ordered = correct_number(
                                    ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[0]))
                                line = corrected_qty_ordered + ' 0 ' + ' '.join(parts[1:])
                            elif len(parts[1]) <= 3 or (len(parts[1]) == 4 and parts[1].endswith(',')):
                                corrected_qty_delivered = correct_number(
                                    ''.join(constants.quantity_correction_dict.get(c, c) for c in parts[1]))
                                line = '0 ' + corrected_qty_delivered + ' ' + ' '.join(parts[2:])
                            else:
                                line = '0 0 ' + ' '.join(parts)
                    else:
                        line = '0 0 ' + ' '.join(parts)

                    parts = line.split()

                    # Process P.P.M - Price
                    if len(parts) >= 3:
                        price_ppm = normalize_price_string_count_after_coma(parts[-3], 2)
                        parts[-3] = price_ppm

                    # Reassemble the line
                    data_to_loop[index] = ' '.join(parts)

                    # Set attribute indexes
                    attribute_indexes['quantity_ordered'].append(0)
                    attribute_indexes['quantity_delivered'].append(1)
                    attribute_indexes['designation'].append(2)
                    attribute_indexes['pph'].append(-3)

                    # Set other indexes to None
                    attribute_indexes['quantity_duplicate'].append(None)
                    attribute_indexes['ppv'].append(None)
                    attribute_indexes['total_ttc'].append(None)
                    attribute_indexes['gf'].append(None)
                    attribute_indexes['tva'].append(None)
                    attribute_indexes['word_index'].append(word_index if word_index else None)

            elif model == constants.SOPHACA:
                quantity, ppv, pph = None, None, None

                # Correct existing values
                if len(parts) >= 4:  # if len(parts) >= 4 or word_index

                    # Process P.P.H - Price
                    pph = normalize_price_string_count_after_coma(parts[-1], 2)
                    word_index = 1 if not word_index else word_index  # if the system didn't find the root name index

                    if word_index:
                        if len(parts[word_index - 1]) > 3 and word_index >= 2:
                            ppv_index = word_index - 1 if contains_number(parts[word_index - 1]) else word_index - 2
                            ppv = normalize_price_string_count_after_coma(parts[ppv_index], 2)
                        elif word_index == 1 and len(parts[word_index - 1]) >= 3:
                            ppv = normalize_price_string_count_after_coma(parts[0], 2)
                        elif word_index == 1 and len(parts[word_index - 1]) <= 2:
                            ppv = "0"
                        elif word_index == 0:
                            ppv = "0"

                        if word_index >= 2:
                            qty_index = 0 if word_index == 2 else 1
                            quantity = correct_number(parts[qty_index])
                        elif word_index == 1 and len(parts[word_index - 1]) >= 3:
                            quantity = "0"
                        elif word_index == 1 and len(parts[word_index - 1]) <= 2:
                            if len(parts[0]) <= 3:
                                quantity = correct_number(parts[0])
                            else:
                                quantity = "0"
                        elif word_index == 0:
                            quantity = "0"
                    else:
                        ppv = "0"
                        quantity = "0"

                # Build the line with quantity, ppv, and pph
                if quantity is None:
                    quantity = "0"
                if ppv is None:
                    ppv = "0"
                if pph is None:
                    pph = "0"

                description_index_start = word_index if word_index else 2
                description = ' '.join(parts[description_index_start:len(parts) - 1])
                new_line = f"{quantity} {ppv} {description} {pph}"

                data_to_loop[index] = new_line

                attribute_indexes['quantity_delivered'].append(0)
                attribute_indexes['ppv'].append(1)
                attribute_indexes['pph'].append(-1)
                attribute_indexes['designation'].append(2)

                attribute_indexes['quantity_ordered'].append(None)
                attribute_indexes['quantity_duplicate'].append(None)
                attribute_indexes['total_ttc'].append(None)
                attribute_indexes['gf'].append(None)
                attribute_indexes['tva'].append(None)
                attribute_indexes['word_index'].append(word_index) if word_index else attribute_indexes[
                    'word_index'].append(None)

            elif model == constants.COOPER_PHARMA_CASA:
                # Correct quantity
                if word_index and word_index == 0:
                    line = '0 ' + line
                    parts = line.split()
                    attribute_indexes['quantity_delivered'].append(0)

                elif word_index and word_index == 1:
                    if contains_number(parts[0]) and len(parts[0]) <= 3:
                        quantity_part = correct_number(parts[0])
                        parts[0] = quantity_part
                    else:
                        line = '0 ' + line
                        parts = line.split()

                    attribute_indexes['quantity_delivered'].append(0)

                elif word_index and word_index >= 2:
                    index_qty = 0 if word_index == 2 else 1 if word_index == 3 else 3

                    if len(parts[index_qty]) > 3 and index_qty == 0:
                        line = '0 ' + line
                        parts = line.split()
                        index_qty = 0  # Update index_qty after splitting the line
                        attribute_indexes['quantity_delivered'].append(0)

                    else:
                        while index_qty >= 0 and len(parts[index_qty]) <= 3:
                            quantity_part = parts[index_qty]
                            quantity_part = correct_number(quantity_part)
                            parts[index_qty] = quantity_part
                            attribute_indexes['quantity_delivered'].append(index_qty)
                            index_qty -= 1

                        if index_qty >= 0:
                            attribute_indexes['quantity_delivered'].append(index_qty)
                        else:
                            # If index_qty becomes negative, append 0 to indicate the first element is the quantity
                            attribute_indexes['quantity_delivered'].append(0)

                else:
                    line = '0 ' + line
                    parts = line.split()
                    attribute_indexes['quantity_delivered'].append(0)

                logging.info("line : ", line)

                # Pricing information
                if len(parts) >= 6:

                    start_index_of_prices = -4
                    if(is_float(parts[-3]) and not is_float(parts[-5]) and not is_float(parts[-6])):
                        logging.info('parts[-3] : ', parts[-3])
                        logging.info('parts[-4] : ', parts[-4])
                        start_index_of_prices = -3

                    total_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices], 3)
                    pu_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices - 1], 3) if len(parts) > 3 else "0"
                    ppv_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices - 2], 3) if len(parts) > 4 else "0"

                    parts[start_index_of_prices] = total_numeric
                    parts[start_index_of_prices - 1] = pu_numeric
                    parts[start_index_of_prices - 2] = ppv_numeric

                    attribute_indexes['ppv'].append(start_index_of_prices - 2)
                    attribute_indexes['pph'].append(start_index_of_prices - 1)
                    attribute_indexes['total_ttc'].append(start_index_of_prices)
                    attribute_indexes['designation'].append(2)

                    # # Find the index of the prices from the end - 3 of the line (ignore last 3 parts)
                    # start_index_of_prices = len(parts) - 3
                    # for i in range(len(parts) - 4, -1, -1):
                    #     part = parts[i]
                    #     logging.info('part : ', part)
                    #     if is_float(part) and len(part) > 2:
                    #         logging.info('part float: ', part)
                    #         start_index_of_prices = i
                    #         break
                    #
                    # total_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices], 3)
                    # pu_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices - 1], 3) if len(
                    #     parts) > 3 else "0"
                    # ppv_numeric = normalize_price_string_count_after_coma(parts[start_index_of_prices - 2], 3) if len(
                    #     parts) > 4 else "0"
                    #
                    # parts[start_index_of_prices] = total_numeric
                    # parts[start_index_of_prices - 1] = pu_numeric
                    # parts[start_index_of_prices - 2] = ppv_numeric
                    #
                    # attribute_indexes['ppv'].append(start_index_of_prices - 2)
                    # attribute_indexes['pph'].append(start_index_of_prices - 1)
                    # attribute_indexes['total_ttc'].append(start_index_of_prices)
                    # attribute_indexes['designation'].append(2)
                else:
                    attribute_indexes['ppv'].append(None)
                    attribute_indexes['pph'].append(None)
                    attribute_indexes['total_ttc'].append(None)
                    attribute_indexes['designation'].append(None)

                attribute_indexes['quantity_ordered'].append(None)
                attribute_indexes['quantity_duplicate'].append(None)
                attribute_indexes['gf'].append(None)
                attribute_indexes['tva'].append(None)
                attribute_indexes['word_index'].append(word_index) if word_index else attribute_indexes[
                    'word_index'].append(None)

                # Combine corrected parts back into the line
                data_to_loop[index] = ' '.join(parts)

            elif model == constants.GPM:

                """ ............................ to check ................................ """
                # Handle quantity
                if word_index and word_index > 0 and word_index <= len(parts):
                    if word_index >= 2:
                        if len(parts[word_index - 1]) <= 3:
                            quantity_part = parts[word_index - 1]
                            quantity_part = correct_number(quantity_part)
                            parts[word_index - 1] = quantity_part

                            attribute_indexes['quantity_delivered'].append(word_index - 1)
                            attribute_indexes['designation'].append(word_index)
                        else:
                            quantity_part = parts[word_index - 2]
                            quantity_part = correct_number(quantity_part)
                            parts[word_index - 2] = quantity_part

                            attribute_indexes['quantity_delivered'].append(word_index - 2)
                            attribute_indexes['designation'].append(word_index)
                    else:

                        if word_index == 1:
                            quantity_part = parts[word_index - 1]
                            quantity_part = correct_number(quantity_part)
                            parts[word_index - 1] = quantity_part

                            attribute_indexes['quantity_delivered'].append(word_index - 1)
                            attribute_indexes['designation'].append(word_index)
                        else:
                            # Handle quantity
                            line = '0 ' + line
                            parts = line.split()

                            attribute_indexes['quantity_delivered'].append(0)
                            attribute_indexes['designation'].append(1)

                elif word_index and word_index == 0:
                    # Handle quantity
                    line = '0 ' + line
                    parts = line.split()

                    attribute_indexes['quantity_delivered'].append(0)
                    attribute_indexes['designation'].append(1)
                else:
                    # Handle quantity
                    line = '0 ' + line
                    parts = line.split()

                    attribute_indexes['quantity_delivered'].append(0)
                    attribute_indexes['designation'].append(1)

                """ ............................ to check ................................ """


                # Locate TVA and adjust subsequent indices
                tva_index = len(parts) - 1  # parts[-1]
                tva = parts[tva_index]
                try:
                    corrected_tva = correct_number(tva.replace('%', '')) + '%'
                except ValueError:
                    corrected_tva = correct_number(tva) + '%'
                parts[tva_index] = corrected_tva

                attribute_indexes['tva'].append(tva_index)

                # Handle PU
                pu_index = tva_index - 1
                if pu_index >= 4 and not fuzzywazzy_part_with_array(parts[pu_index], constants.CORRECT_WORDS):
                    pu = parts[pu_index]
                    corrected_pu = normalize_price_string_count_after_coma(pu, 2)
                    parts[pu_index] = corrected_pu

                    attribute_indexes['pph'].append(pu_index)
                else:
                    attribute_indexes['pph'].append(None)

                # Handle PPV
                ppv_index = pu_index - 1
                if ppv_index >= 3 and not fuzzywazzy_part_with_array(parts[pu_index], constants.CORRECT_WORDS):
                    ppv = parts[ppv_index]
                    corrected_ppv = normalize_price_string_count_after_coma(ppv, 2)
                    parts[ppv_index] = corrected_ppv

                    attribute_indexes['ppv'].append(ppv_index)
                else:
                    attribute_indexes['ppv'].append(None)

                attribute_indexes['quantity_ordered'].append(None)
                attribute_indexes['quantity_duplicate'].append(None)
                attribute_indexes['total_ttc'].append(None)
                attribute_indexes['gf'].append(None)
                attribute_indexes['word_index'].append(word_index) if word_index else attribute_indexes[
                    'word_index'].append(None)

                # Combine corrected parts back into the line
                data_to_loop[index] = ' '.join(parts)

            elif model == constants.SOPHADIMS or model == constants.RECAMED:

                # Handle quantity
                if word_index and word_index > 0 and word_index <= len(parts):
                    if len(parts[0]) <= 3:
                        quantity_part = parts[0]
                        quantity_part = correct_number(quantity_part)
                        parts[0] = quantity_part
                    else:
                        line = '0 ' + line
                        parts = line.split()
                elif word_index and word_index == 0:
                    line = '0 ' + line
                    parts = line.split()
                else:
                    line = '0 ' + line
                    parts = line.split()

                attribute_indexes['quantity_delivered'].append(0)
                attribute_indexes['designation'].append(1)

                # Check if we have enough parts before processing
                min_required_parts = 7  # We need at least 7 parts for all fields
                if len(parts) < min_required_parts:
                    # Add missing parts with zeros
                    parts.extend(['0'] * (min_required_parts - len(parts)))
                try:
                    # Process quantity duplicate
                    if len(parts) >= 3:
                        qty_duplicate = parts[-3]
                        if not qty_duplicate.isdigit():
                            parts[-3] = correct_number(qty_duplicate)
                            attribute_indexes['quantity_duplicate'].append(-3)
                        else:
                            attribute_indexes['quantity_duplicate'].append(None)
                    else:
                        attribute_indexes['quantity_duplicate'].append(None)

                    # Process PU CLIENT (PPV)
                    if parts and len(parts) >= 1:
                        pu_client = normalize_price_string_count_after_coma(parts[-1], 2)
                        parts[-1] = pu_client
                        attribute_indexes['ppv'].append(-1)
                    else:
                        parts.append('0')
                        attribute_indexes['ppv'].append(None)

                    # Process TVA
                    if len(parts) >= 4:
                        tva = correct_number(parts[-4])
                        parts[-4] = tva
                        attribute_indexes['tva'].append(-4)

                    else:
                        parts.insert(-1, '0')
                        attribute_indexes['tva'].append(None)

                    # Process Total_TTC
                    if len(parts) >= 5:
                        ttc = normalize_price_string_count_after_coma(parts[-5], 2)
                        parts[-5] = ttc
                        attribute_indexes['total_ttc'].append(-5)

                    else:
                        parts.insert(-1, '0')
                        attribute_indexes['total_ttc'].append(None)

                    # Process PPH
                    if len(parts) >= 6:
                        pph = normalize_price_string_count_after_coma(parts[-6], 2)
                        parts[-6] = pph
                        attribute_indexes['pph'].append(-6)

                    else:
                        parts.insert(-1, '0')
                        attribute_indexes['pph'].append(None)

                    # Process GF
                    if len(parts) >= 7:
                        gf = correct_number(parts[-7])
                        parts[-7] = gf
                        attribute_indexes['gf'].append(-7)

                    else:
                        parts.insert(-1, '0')
                        attribute_indexes['gf'].append(None)

                except Exception as e:
                    logging.error(f"Error processing line {index}: {str(e)}")
                    # Fill in missing attribute indexes
                    for attr in ['quantity_duplicate', 'ppv', 'tva', 'total_ttc', 'pph', 'gf']:
                        if attr not in attribute_indexes or len(attribute_indexes[attr]) <= index:
                            attribute_indexes[attr].append(None)

                attribute_indexes['quantity_ordered'].append(None)
                attribute_indexes['word_index'].append(word_index if word_index else None)

                # Combine corrected parts back into the line
                data_to_loop[index] = ' '.join(parts)

            # Clean numeric fields
            for field in numeric_fields:
                if field in attribute_indexes and attribute_indexes[field]:
                    index_to_clean = attribute_indexes[field][-1]
                    if index_to_clean is not None and index_to_clean < len(parts):
                        parts[index_to_clean] = clean_numeric_field(parts[index_to_clean])

            # Reassemble the line
            data_to_loop[index] = ' '.join(parts)
        data_to_loop = replace_double_spaces(data_to_loop)  # was filtered_and_formatted_lines
        return data_to_loop, attribute_indexes
    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_chiffres_calculated: {traceback.format_exc()}")
        # Return default values instead of None
        return [], {key: [] for key in attribute_indexes.keys()}


"""-----******************************  Structuring data to JSON Format  ******************************-----"""
""" ------------------------------------------------------------------------------------------------------------------ """

"""-----************ MODEL BL (SPR) ************ -----"""


def correct_date_SPR(date_str):
    try:
        def correct_date_validate(date):
            parts = date.split('/')
            if len(parts) == 3:
                day, month, year = parts

                # Apply corrections from the dictionary to each part
                day = "".join(constants.quantity_correction_dict.get(char, char) for char in day if
                              char.isdigit() or char in constants.quantity_correction_dict)
                month = "".join(constants.quantity_correction_dict.get(char, char) for char in month if
                                char.isdigit() or char in constants.quantity_correction_dict)
                year = "".join(constants.quantity_correction_dict.get(char, char) for char in year if
                               char.isdigit() or char in constants.quantity_correction_dict)

                # Validate and correct the day
                if day.isdigit() and int(day) > 31:
                    day = str(day)[-1]  # Use the last digit if the day is nonsensically high

                # Validate and correct the month
                if month.isdigit() and int(month) > 12:
                    month = '12'

                if day.isdigit() and month.isdigit() and year.isdigit():
                    # Reconstruct the date string with corrections
                    corrected_date_str = f"{int(day):02}/{int(month):02}/{year}"
                    return corrected_date_str

            return "Invalid Date"  # Return a placeholder if parsing fails

        # Replace common OCR errors
        date_str = date_str.replace('o', '0').replace('O', '0').replace('l', '1').replace('I', '1')

        # Try to parse the date using different expected formats
        for fmt in ("%d/%m/%Y", "%d%m%Y", "%d/%m/%y"):  # You can add more formats as needed
            try:
                # If the date is correctly parsed, return it in a uniform format
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime("%d/%m/%Y")
            except ValueError:
                continue  # Try the next format if the current one fails

        return correct_date_validate(date_str)
        # return "Invalid Date"  # Return a placeholder if all parses fail

    except (ValueError, TypeError) as e:
        logging.error(f"Error in correct_date_SPR: {traceback.format_exc()}")
        return "Invalid Date"  # Return a placeholder if all parses fail


def structure_data_SPR(lines, attribute_indexes, additional_section_start_index):
    try:
        structured_data = []
        tnp_result = []

        # Model of data
        document = DocumentModel()

        # logging.info('attribute_indexes : ', attribute_indexes)
        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = lines[:additional_section_start_index]
            lines_to_append_as_is = lines[additional_section_start_index:]
        else:
            lines_to_check = lines
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):  # This skips the first element of the list
            parts = line.split()
            # parts = clean_ocr_line_last_seven_parts(line.split())
            # logging.info('parts : ', parts)
            if not parts:
                continue

            selected_clean_word, word_index = select_name_root_from_line(line)

            if word_index or parts[attribute_indexes['quantity_delivered'][i]]:
                # selected_clean_word, word_index = select_name_root_from_line(line)

                # Indexes :
                ordered_qty_index = attribute_indexes['quantity_ordered'][i]
                delivered_qty_index = attribute_indexes['quantity_delivered'][i]
                price_ppm_index = attribute_indexes['pph'][i]
                designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                    i] else 1  # must be ' else 2'

                ordered_qty = parts[ordered_qty_index] if ordered_qty_index is not None else None
                delivered_qty = parts[delivered_qty_index] if delivered_qty_index is not None else None

                # Process P.P.M - Price
                price_ppm = parts[price_ppm_index] if price_ppm_index is not None else None

                # Designation
                designation = ' '.join(parts[designation_index:len(parts) - 3])

                if designation != '':
                    structured_data.append({
                        'ordered_quantity': ordered_qty,
                        'delivered_quantity': delivered_qty,
                        'designation': designation,
                        'ppm': price_ppm,
                        'lot_number': parts[-2],
                        'date': parts[-1],
                        'additional_info': "",
                    })
                else:
                    structured_data.append({
                        'ordered_quantity': "",
                        'delivered_quantity': "",
                        'designation': "",
                        'ppm': "",
                        'lot_number': "",
                        'date': "",
                        'additional_info': line,
                    })

                # Generate the new JSON structure for TNP
                TNP = {
                    "ID": "",
                    "product_category_id_label": "",
                    "product_galenic_form_id_label": "",
                    "name": designation,
                    "barcode": "",
                    "barcode_2": "",
                    "prix_vente": price_ppm
                }
                tnp_result.append(TNP)

        for i, line in enumerate(lines_to_append_as_is):
            structured_data.append({
                'ordered_quantity': "",
                'delivered_quantity': "",
                'designation': "",
                'ppm': "",
                'lot_number': "",
                'date': "",
                'additional_info': line,
            })

        for data in structured_data:
            table_row = DocumentModel.Table()
            table_row.qty_ordered = data['ordered_quantity']
            table_row.quantity = data['delivered_quantity']
            table_row.designation = data['designation']
            table_row.pph = str(data['ppm'])
            table_row.num_lot = data['lot_number']
            table_row.date_per = correct_date_SPR(data['date'])
            table_row.additional_info = data['additional_info']

            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)


    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_SPR: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (SOPHACA) ************ -----"""


def structure_data_SOPHACA(lines, attribute_indexes, additional_section_start_index):
    try:
        structured_data = []
        tnp_result = []
        # lines = ocr_data.split('\n')
        # lines = clean_chars_ocr_lines_last_parts(lines, 3)

        # Model of data
        document = DocumentModel()

        price_pph = None
        price_ppv = None
        quantity = None
        description = None
        additional_info = None

        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = lines[:additional_section_start_index]
            lines_to_append_as_is = lines[additional_section_start_index:]
        else:
            lines_to_check = lines
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):  # This skips the first element of the list
            parts = line.split()
            # parts = clean_ocr_line_last_seven_parts(line.split())
            # logging.info('parts : ', parts)
            if not parts:
                continue

            # Indexes :
            quantity_index = attribute_indexes['quantity_delivered'][i]
            price_ppv_index = attribute_indexes['ppv'][i]
            price_pph_index = attribute_indexes['pph'][i]
            designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                i] else 1  # must be ' else 2'

            if len(parts) >= 4:
                # selected_clean_word, word_index = select_name_root_from_line(line)

                # Process Qty
                quantity = parts[quantity_index] if quantity_index is not None else None

                # Process P.P.V - Price
                price_ppv = parts[price_ppv_index] if price_ppv_index is not None else None

                # Process P.P.H - Price
                price_pph = parts[price_pph_index] if price_pph_index is not None else None

                # Process Designation
                description = ' '.join(parts[designation_index:len(parts) - 1])

                structured_data.append({
                    'quantity': quantity,
                    'description': description,
                    'price_pph': price_pph,
                    'price_ppv': price_ppv,
                    'additional_info': "",
                })

                # Generate the new JSON structure for TNP
                TNP = {
                    "ID": "",
                    "product_category_id_label": "",
                    "product_galenic_form_id_label": "",
                    "name": description,
                    "barcode": "",
                    "barcode_2": "",
                    "prix_vente": price_ppv
                }
                tnp_result.append(TNP)

        for i, line in enumerate(lines_to_append_as_is):
            structured_data.append({
                'quantity': "",
                'description': "",
                'price_pph': "",
                'price_ppv': "",
                'additional_info': line,
            })
        for data in structured_data:
            table_row = DocumentModel.Table()
            table_row.quantity = data['quantity']
            table_row.designation = data['description']
            table_row.pph = str(data['price_pph'])
            table_row.ppv = str(data['price_ppv'])
            table_row.additional_info = str(data['additional_info'])

            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)


    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_SOPHACA: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (COOPER_PHARMA_CASA) ************ -----"""


def structure_data_COOPER_PHARMA_CASA(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []

        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        # Model of data
        document = DocumentModel()

        for i, line in enumerate(lines_to_check):  # This skips the first element of the list
            parts = line.split()
            # parts = clean_ocr_line_last_seven_parts(line.split())
            # logging.info('parts : ', parts)
            if not parts:
                continue

            # Indexes :
            quantity_index = attribute_indexes['quantity_delivered'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                i] else 1  # must be ' else 2'

            # Extract quantity
            quantity = parts[quantity_index] if quantity_index is not None else None

            # Pricing information
            if len(parts) >= 6:
                code_produit = parts[-1]
                date_per = parts[-2]
                num_lot = parts[-3]

                total_numeric = parts[total_ttc_index] if total_ttc_index is not None else None
                pu_numeric = parts[pph_index] if pph_index is not None else None
                ppv_numeric = parts[ppv_index] if ppv_index is not None else None

            else:
                # total_numeric = parts[-4]
                # pu_numeric = parts[-5]
                # ppv_numeric = parts[-6]
                total_numeric = pu_numeric = ppv_numeric = "0.000"
                code_produit = date_per = num_lot = ""

            # Determine where pricing information starts (it should be the last three parts)
            if len(parts) >= 6:
                description_end_index = len(
                    parts) - 6  # 6 is the sum of parts after description (ppv, pph, ttc, code_produit, lot, date)
            else:
                description_end_index = len(parts)  # Fallback if not enough parts

            # Code and Description
            if attribute_indexes['word_index'][i] and len(parts) > attribute_indexes['word_index'][i]:
                code = parts[1] if len(parts[1]) >= 2 else (parts[2] if len(parts) > 2 else None)
                description = ' '.join(parts[designation_index:description_end_index])
            else:
                code = None
                description = None

            # Store the data as table_row in the document model, then append it in the table model
            table_row = DocumentModel.Table()

            table_row.quantity = quantity
            table_row.forme_galenique = code
            table_row.designation = description
            table_row.ppv = ppv_numeric
            table_row.pph = pu_numeric
            table_row.total_ttc = total_numeric
            table_row.num_lot = num_lot
            table_row.code_produit = code_produit
            table_row.date_per = date_per

            document.table.append(table_row)

            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": code,
                "name": description,
                "barcode": "",
                "barcode_2": "",
                "prix_vente": ppv_numeric
            }
            tnp_result.append(TNP)

        # Store Additional Lines
        for i, line in enumerate(lines_to_append_as_is):
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_COOPER_PHARMA_CASA: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (GPM) ************ -----"""


def structure_data_GPM(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []

        # Model of data
        document = DocumentModel()

        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue
            data = {}

            # Indexes :
            quantity_index = attribute_indexes['quantity_delivered'][i]
            tva_index = attribute_indexes['tva'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                i] else 1  # must be ' else 2'

            word_index = attribute_indexes['word_index'][i]

            # check_qty_exist = None
            # if quantity_index:
            #     check_qty_exist = parts[check_qty_exist]

            # Handle quantity
            # if word_index and word_index <= len(parts):  # and word_index > 0

            # Locate Quantity
            data['Quantity'] = parts[quantity_index] if quantity_index is not None else None

            # Locate TVA
            data['TVA'] = parts[tva_index] if tva_index is not None else None

            # Handle PU
            data['PU'] = parts[pph_index] if pph_index is not None else None

            # Handle PPV
            data['PPV'] = parts[ppv_index] if ppv_index is not None else None

            # Handle Code
            code_index = ppv_index - 1 if (ppv_index and ppv_index > 0) else None
            data['Code'] = parts[code_index] if code_index is not None else None

            # Extract Description
            # description_index_end = max(word_index, 0) if word_index is not None else 0

            # Determine where code information starts (it should be the last three parts)
            description_start_index = designation_index
            description_end_index = code_index if code_index else len(parts)
            data['Description'] = ' '.join(parts[description_start_index:description_end_index])

            # if word_index == 0 and check_qty_exist is None:  # To remove
            #
            #     description_end = -2  # Start assuming the second last word ends the description.
            #     pu_index = -1  # Assume last word is PU.
            #
            #     # Use a while loop to find a suitable PU that has at least two characters.
            #     while -pu_index <= len(parts) and len(parts[pu_index]) < 2:
            #         pu_index -= 1  # Move one word further in the parts list.
            #         description_end -= 1  # Adjust description end as well.
            #
            #     # If the loop finds all words are less than 2 characters long, adjust accordingly.
            #     if -pu_index > len(parts):  # This means no valid PU was found
            #         pu_index = -1  # Reset to last word
            #         data['PU'] = None  # Set PU as None since no valid PU exists
            #     else:
            #         # Try to extract and format the PU
            #         try:
            #             data['PU'] = f"{float(parts[pu_index].replace(',', '.')):.2f}"
            #         except ValueError:
            #             data['PU'] = None  # Set to None if conversion fails
            #
            #     # Join parts to form the Description, excluding the part used for PU
            #     data['Description'] = ' '.join(parts[:description_end + 1])
            #
            #     # Other fields are set to 'NULL' or similar placeholders
            #     data['TVA'] = 'NULL'
            #     data['TVA'] = 'NULL'
            #     data['PPV'] = 'NULL'
            #     data['Code'] = 'NULL'
            #     data['Quantity'] = 'NULL'
            #     data['word_index'] = word_index

            # Store the data as table_row in the document model, then append it in the table model
            table_row = DocumentModel.Table()

            table_row.quantity = data['Quantity']
            table_row.forme_galenique = data['Code']
            table_row.designation = data['Description']
            table_row.ppv = str(data['PPV'])
            table_row.pph = str(data['PU'])
            table_row.tva = data['TVA']

            document.table.append(table_row)

            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": data['Code'],
                "name": data['Description'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['PPV']
            }
            tnp_result.append(TNP)

        # Store Additional Lines
        for i, line in enumerate(lines_to_append_as_is):
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_GPM: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (Sophadims) ************ -----"""


def structure_data_SOPHADIMS(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []

        # Model of data
        document = DocumentModel()

        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            # word_index = parts[attribute_indexes['word_index'][i]]

            # Indexes :
            quantity_index = attribute_indexes['quantity_delivered'][i]
            quantity_duplicate_index = attribute_indexes['quantity_duplicate'][i]
            tva_index = attribute_indexes['tva'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i]
            gf_index = attribute_indexes['gf'][i]
            designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                i] else 1  # must be ' else 1'

            data = OrderedDict([
                ('quantity', None),
                ('Description', None),
                ('GF', None),
                ('PPH', None),
                ('Total_TTC', None),
                ('TVA', None),
                ('QTY_duplicate', None),
                ('CODE_PRODUIT', None),
                ('PU_CLIENT', None),
                ('PPV', None)
            ])

            # Extract Quantity
            quantity = parts[quantity_index] if quantity_index is not None else None

            qty_duplicate = parts[quantity_duplicate_index] if quantity_duplicate_index is not None else None

            if quantity and quantity.isdigit():
                data['quantity'] = quantity
                data['QTY_duplicate'] = qty_duplicate
            elif qty_duplicate and qty_duplicate.isdigit():
                data['quantity'] = qty_duplicate
                data['QTY_duplicate'] = qty_duplicate
            else:
                data['quantity'] = quantity
                data['QTY_duplicate'] = qty_duplicate

            # Process PU CLIENT ??? (PPV)
            data['PU_CLIENT'] = parts[ppv_index] if ppv_index is not None else None
            data['PPV'] = parts[ppv_index] if ppv_index is not None else None

            # Process CODE_PRODUIT
            data['CODE_PRODUIT'] = parts[-2]

            # Process TVA
            data['TVA'] = parts[tva_index] if tva_index is not None else None

            # Process Total_TTC
            data['Total_TTC'] = parts[total_ttc_index] if total_ttc_index is not None else None

            # Process PPH
            data['PPH'] = parts[pph_index] if pph_index is not None else None

            # Process GF
            data['GF'] = parts[gf_index] if gf_index is not None else None

            # Compile Description
            description_parts = parts[designation_index:-7]  # From after quantity to before PPH
            data['Description'] = ' '.join(description_parts)

            # Store the data as table_row in the document model, then append it in the table model

            table_row = DocumentModel.Table()

            table_row.quantity = data['quantity']
            table_row.qty_duplicate = data['QTY_duplicate']
            table_row.designation = data['Description']
            table_row.pph = str(data['PPH'])
            table_row.ppv = str(data['PPV'])
            table_row.total_ttc = str(data['Total_TTC'])
            table_row.tva = data['TVA']
            table_row.gf = data['GF']
            table_row.code_produit = data['CODE_PRODUIT']
            table_row.pu_client = str(data['PU_CLIENT'])

            document.table.append(table_row)

            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['Description'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['PPV']
            }
            tnp_result.append(TNP)

        # Store Additional Lines
        for i, line in enumerate(lines_to_append_as_is):
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_SOPHADIMS: {traceback.format_exc()}")
        return None, None


"""-----************ MODEL BL (RECAMED) ************ -----"""


def structure_data_RECAMED(data_to_loop, attribute_indexes, additional_section_start_index):
    try:
        tnp_result = []

        # Model of data
        document = DocumentModel()

        # Determine the lines to check based on additional_section_start_index
        if additional_section_start_index is not None:
            lines_to_check = data_to_loop[:additional_section_start_index]
            lines_to_append_as_is = data_to_loop[additional_section_start_index:]
        else:
            lines_to_check = data_to_loop
            lines_to_append_as_is = []

        for i, line in enumerate(lines_to_check):
            parts = line.split()
            if not parts:
                continue

            # word_index = parts[attribute_indexes['word_index'][i]]

            # Indexes :
            quantity_index = attribute_indexes['quantity_delivered'][i]
            quantity_duplicate_index = attribute_indexes['quantity_duplicate'][i]
            tva_index = attribute_indexes['tva'][i]
            pph_index = attribute_indexes['pph'][i]
            ppv_index = attribute_indexes['ppv'][i]
            total_ttc_index = attribute_indexes['total_ttc'][i]
            gf_index = attribute_indexes['gf'][i]
            designation_index = attribute_indexes['designation'][i] if attribute_indexes['designation'][
                i] else 1  # must be ' else 1'

            data = OrderedDict([
                ('quantity', None),
                ('Description', None),
                ('GF', None),
                ('PPH', None),
                ('Total_TTC', None),
                ('TVA', None),
                ('QTY_duplicate', None),
                ('CODE_PRODUIT', None),
                ('PU_CLIENT', None),
                ('PPV', None)
            ])

            # Extract Quantity
            quantity = parts[quantity_index] if quantity_index is not None else None

            qty_duplicate = parts[quantity_duplicate_index] if quantity_duplicate_index is not None else None

            if quantity and quantity.isdigit():
                data['quantity'] = quantity
                data['QTY_duplicate'] = qty_duplicate
            elif qty_duplicate and qty_duplicate.isdigit():
                data['quantity'] = qty_duplicate
                data['QTY_duplicate'] = qty_duplicate
            else:
                data['quantity'] = quantity
                data['QTY_duplicate'] = qty_duplicate

            # Process PU CLIENT ??? (PPV)
            data['PU_CLIENT'] = parts[ppv_index] if ppv_index is not None else None
            data['PPV'] = parts[ppv_index] if ppv_index is not None else None

            # Process CODE_PRODUIT
            data['CODE_PRODUIT'] = parts[-2]

            # Process TVA
            data['TVA'] = parts[tva_index] if tva_index is not None else None

            # Process Total_TTC
            data['Total_TTC'] = parts[total_ttc_index] if total_ttc_index is not None else None

            # Process PPH
            data['PPH'] = parts[pph_index] if pph_index is not None else None

            # Process GF
            data['GF'] = parts[gf_index] if gf_index is not None else None

            # Compile Description
            description_parts = parts[designation_index:-7]  # From after quantity to before PPH
            data['Description'] = ' '.join(description_parts)

            # Store the data as table_row in the document model, then append it in the table model

            table_row = DocumentModel.Table()

            table_row.quantity = data['quantity']
            table_row.qty_duplicate = data['QTY_duplicate']
            table_row.designation = data['Description']
            table_row.pph = str(data['PPH'])
            table_row.ppv = str(data['PPV'])
            table_row.total_ttc = str(data['Total_TTC'])
            table_row.tva = data['TVA']
            table_row.gf = data['GF']
            table_row.code_produit = data['CODE_PRODUIT']
            table_row.pu_client = str(data['PU_CLIENT'])

            document.table.append(table_row)

            # Generate the new JSON structure for TNP
            TNP = {
                "ID": "",
                "product_category_id_label": "",
                "product_galenic_form_id_label": "",
                "name": data['Description'],
                "barcode": "",
                "barcode_2": "",
                "prix_vente": data['PPV']
            }
            tnp_result.append(TNP)

        # Store Additional Lines
        for i, line in enumerate(lines_to_append_as_is):
            table_row = DocumentModel.Table()
            table_row.additional_info = line
            document.table.append(table_row)

        return document, json.dumps(tnp_result, indent=4, ensure_ascii=False)

    except (IndexError, ValueError, AttributeError) as e:
        logging.error(f"Error in structure_data_RECAMED: {traceback.format_exc()}")
        return None, None


"""----- ************************************ FORMAT TAP ************************************ -----"""


def Export_Format_TAP(documentModel: DocumentModel):
    try:
        # Get data of table
        document = documentModel
        data_tap = []

        for row in document.table:
            # Only exclude rows where designation is exactly an empty string ""
            # Keep rows where designation is None/null or has actual content
            if row.designation != "":
                # Generate the new JSON structure for TNP
                TNP = {
                    "ID": "",
                    "categorie": "",
                    "forme_galenique": "",
                    "designation": row.designation,
                    "barcode": "",
                    "barcode_2": "",
                    "prix_vente": row.ppv if row.ppv else "",
                }
                data_tap.append(TNP)

        return data_tap

    except (ValueError, TypeError) as e:
        logging.error(f"Error in Export_Format_TAP: {traceback.format_exc()}")
        return None
