{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-range_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACU;AACI;AAChE;AACZ;AACiC;AACnB;AAE7D,SAAS0B,gBAAgBA,CAACC,CAAC,EAAE;EACzB,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,EACX,OAAO,CAAC;EACZ,OAAOA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACJ,CAAC,EAAE,GAAGK,UAAU,EAAE;EAC/C,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGH,UAAU,CAACI,GAAG,CAAEpC,CAAC,IAAK0B,gBAAgB,CAAC1B,CAAC,CAAC,CAAC,CAAC;EACzE,OAAOqC,MAAM,CAACV,CAAC,CAACW,OAAO,CAACL,SAAS,CAAC,CAAC;AACvC;AAEA,MAAMM,WAAW,GAAG,2tOAA2tO;AAC/uO,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,mjUAAmjU;AACtkU,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB7C,qDAAgB,CAAC,IAAI,EAAE4C,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAG5C,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC6C,QAAQ,GAAG7C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8C,QAAQ,GAAG9C,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC+C,OAAO,GAAG/C,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACgD,gBAAgB,GAAGhD,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACiD,cAAc,GAAGjD,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACkD,OAAO,GAAI,SAAQC,QAAQ,EAAG,EAAC;IACpC,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,WAAW,GAAIC,KAAK,IAAK;MAC1B,OAAOjD,uDAAK,CAAC,IAAI,CAACkD,GAAG,EAAED,KAAK,EAAE,IAAI,CAAC3B,GAAG,CAAC;IAC3C,CAAC;IACD,IAAI,CAAC6B,mBAAmB,GAAIF,KAAK,IAAK;MAClC,IAAI,IAAI,CAACG,SAAS,EAAE;QAChB,OAAO;UACHC,KAAK,EAAE,IAAI,CAACL,WAAW,CAACC,KAAK,CAACI,KAAK,CAAC;UACpCC,KAAK,EAAE,IAAI,CAACN,WAAW,CAACC,KAAK,CAACK,KAAK;QACvC,CAAC;MACL,CAAC,MACI;QACD,OAAO,IAAI,CAACN,WAAW,CAACC,KAAK,CAAC;MAClC;IACJ,CAAC;IACD,IAAI,CAACM,YAAY,gBAAAC,6OAAA,CAAG,aAAY;MAC5B,MAAMC,WAAW,GAAGxB,KAAI,CAACwB,WAAW;MACpC,IAAIA,WAAW,EAAE;QACbxB,KAAI,CAACyB,OAAO,GAAG,OAAO,sHAA6B,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,OAAO;UACpBC,eAAe,EAAE,GAAG;UACpB;AACpB;AACA;AACA;AACA;AACA;UACoBC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAEA,CAAA,KAAM/B,KAAI,CAAC+B,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAKjC,KAAI,CAACgC,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAKjC,KAAI,CAACkC,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACFjC,KAAI,CAACyB,OAAO,CAACU,MAAM,CAAC,CAACnC,KAAI,CAACoC,QAAQ,CAAC;MACvC;IACJ,CAAC;IACD,IAAI,CAACC,cAAc,GAAG,CAACC,IAAI,EAAEC,UAAU,KAAK;MACxC,MAAM;QAAErB;MAAoB,CAAC,GAAG,IAAI;MACpC,IAAIsB,IAAI,GAAG,IAAI,CAACA,IAAI;MACpBA,IAAI,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC;MAC1BA,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACnD,GAAG,GAAG,IAAI,CAAC4B,GAAG,CAAC;MACnC,IAAI,CAACsB,UAAU,EAAE;QACbC,IAAI,IAAI,CAAC,CAAC;MACd;MACA,IAAIF,IAAI,KAAK,GAAG,EAAE;QACd,IAAI,CAACG,MAAM,GAAG1E,uDAAK,CAAC,CAAC,EAAE,IAAI,CAAC0E,MAAM,GAAGD,IAAI,EAAE,CAAC,CAAC;MACjD,CAAC,MACI;QACD,IAAI,CAACE,MAAM,GAAG3E,uDAAK,CAAC,CAAC,EAAE,IAAI,CAAC2E,MAAM,GAAGF,IAAI,EAAE,CAAC,CAAC;MACjD;MACA,IAAI,CAACnC,gBAAgB,CAACsC,IAAI,CAAC;QAAE3B,KAAK,EAAEE,mBAAmB,CAAC,IAAI,CAACF,KAAK;MAAE,CAAC,CAAC;MACtE,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACvC,cAAc,CAACqC,IAAI,CAAC;QAAE3B,KAAK,EAAEE,mBAAmB,CAAC,IAAI,CAACF,KAAK;MAAE,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,CAAC8B,MAAM,GAAG,MAAM;MAChB,IAAI,IAAI,CAACnC,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACP,OAAO,CAACuC,IAAI,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC,IAAI,CAACpC,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACR,QAAQ,CAACwC,IAAI,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACF,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACM,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACC,KAAK,GAAGD,SAAS;IACtB,IAAI,CAACE,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACG,IAAI,GAAG,IAAI,CAAC7C,OAAO;IACxB,IAAI,CAAC8C,KAAK,GAAGJ,SAAS;IACtB,IAAI,CAAC9B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACF,GAAG,GAAG,CAAC;IACZ,IAAI,CAAC5B,GAAG,GAAG,GAAG;IACd,IAAI,CAACiE,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,YAAY,GAAIvC,KAAK,IAAK5B,IAAI,CAACoE,KAAK,CAACxC,KAAK,CAAC;IAChD,IAAI,CAACyC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACjB,IAAI,GAAG,CAAC;IACb,IAAI,CAACkB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,cAAc,GAAGV,SAAS;IAC/B,IAAI,CAACb,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACpB,KAAK,GAAG,CAAC;IACd,IAAI,CAAC4C,cAAc,GAAG,OAAO;EACjC;EACAC,eAAeA,CAAA,EAAG;IACd,MAAM;MAAE3D,QAAQ;MAAEiD,QAAQ;MAAEW;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAC5D,QAAQ,GAAGiD,QAAQ,KAAKF,SAAS,GAAGa,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG5D,QAAQ,GAAGjC,uDAAa,CAACiC,QAAQ,EAAEiD,QAAQ,CAAC;EACvK;EACAY,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACrD,QAAQ,EAAE;MAChB,IAAI,CAACsD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACvD,QAAQ,EAAE;MAChB,IAAI,CAACsD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEP;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,KAAKV,SAAS,EAAE;MAC9B,IAAIU,cAAc,GAAG,IAAI,CAACtE,GAAG,EAAE;QAC3BhB,qDAAe,CAAE,uCAAsCsF,cAAe,8BAA6B,IAAI,CAACtE,GAAI,qGAAoG,EAAE,IAAI,CAACsC,EAAE,CAAC;QAC1N,IAAI,CAACgC,cAAc,GAAG,IAAI,CAACtE,GAAG;MAClC,CAAC,MACI,IAAIsE,cAAc,GAAG,IAAI,CAAC1C,GAAG,EAAE;QAChC5C,qDAAe,CAAE,uCAAsCsF,cAAe,2BAA0B,IAAI,CAAC1C,GAAI,qGAAoG,EAAE,IAAI,CAACU,EAAE,CAAC;QACvN,IAAI,CAACgC,cAAc,GAAG,IAAI,CAAC1C,GAAG;MAClC;IACJ;EACJ;EACAkD,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC1C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACU,MAAM,CAAC,CAAC,IAAI,CAACC,QAAQ,CAAC;IACvC;EACJ;EACAgC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;MAChB,IAAI,CAACsD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAK,iBAAiBA,CAAA,EAAG;IAChB;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC1C,EAAE,CAAC2C,YAAY,CAAC,IAAI,CAAC,EAAE;MAC5B,IAAI,CAAC/D,OAAO,GAAG,IAAI,CAACoB,EAAE,CAAC4C,YAAY,CAAC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC3D,mBAAmB,GAAG1C,uDAAqB,CAAC,IAAI,CAACyD,EAAE,CAAC;EAC7D;EACA6C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAAC5D,QAAQ;IACrC,IAAI,CAACoB,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC0C,WAAW,CAAC,CAAC;IAClB,IAAI,CAACvD,OAAO,GAAG,IAAI;EACvB;EACAgE,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACT,WAAW,CAAC,CAAC;IAClB,IAAI,CAACH,eAAe,CAAC,CAAC;IACtB,IAAI,CAACM,eAAe,CAAC,CAAC;IACtB,IAAI,CAACD,qBAAqB,CAAC,CAAC;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACzD,OAAO,EAAE;MACd,IAAI,CAACa,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,CAACT,SAAS,GAAGlD,qDAAqB,CAAC,IAAI,CAACgE,EAAE,CAAC;EACnD;EACA+C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACjD,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACkD,OAAO,CAAC,CAAC;MACtB,IAAI,CAAClD,OAAO,GAAGwB,SAAS;IAC5B;EACJ;EACA2B,QAAQA,CAAA,EAAG;IACP,IAAIC,EAAE;IACN,MAAM7D,KAAK,GAAG,CAAC6D,EAAE,GAAG,IAAI,CAAC7D,KAAK,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAClE,IAAI,IAAI,CAAC1D,SAAS,EAAE;MAChB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK;MAChB;MACA,OAAO;QACHI,KAAK,EAAE,CAAC;QACRC,KAAK,EAAEL;MACX,CAAC;IACL,CAAC,MACI;MACD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK,CAACK,KAAK;MACtB;MACA,OAAOL,KAAK;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6B,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7B,KAAK,GAAG,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK,CAAC;IACjD,IAAI,CAACf,SAAS,CAAC0C,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIe,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC1B,gBAAgB,CAACsC,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgB,MAAMA,CAAC8C,MAAM,EAAE;IACX,MAAM;MAAEjE,SAAS;MAAEmC;IAAY,CAAC,GAAG,IAAI;IACvC,MAAM+B,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAIlE,SAAS,IAAI,IAAI,CAACC,qBAAqB,KAAKmC,SAAS,EAAE;MACvD,IAAI,CAACnC,qBAAqB,GAAGlD,qDAAqB,CAACiD,SAAS,CAAC;IACjE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAImC,WAAW,KAAKC,SAAS,EAAE;MAC3B,IAAI,CAAC+B,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA,IAAI,CAACE,MAAM,CAACF,QAAQ,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7C,KAAKA,CAAC4C,MAAM,EAAE;IACV,MAAM;MAAEjE,SAAS;MAAEC;IAAsB,CAAC,GAAG,IAAI;IACjD,MAAMiE,QAAQ,GAAGD,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACI,OAAO;IAClD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAClC,WAAW,KAAKC,SAAS,EAAE;MAChC,IAAI,CAAC+B,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIlE,SAAS,IAAIC,qBAAqB,KAAKmC,SAAS,EAAE;MAClDpF,qDAAmB,CAACgD,SAAS,EAAEC,qBAAqB,CAAC;IACzD;IACA;IACA,IAAI,CAACmE,MAAM,CAACF,QAAQ,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAAC/B,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACJ,eAAe,CAAC,CAAC;IACtB,IAAI,CAACvC,cAAc,CAACqC,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC;EAC7E;EACAiE,MAAMA,CAACF,QAAQ,EAAE;IACb;IACA;IACA,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIC,KAAK,GAAGrH,uDAAK,CAAC,CAAC,EAAE,CAACgH,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAIhH,mDAAK,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MAChByD,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,IAAI,CAAC3B,KAAK,EAAE;MACZ;MACA2B,KAAK,GAAGG,YAAY,CAACC,YAAY,CAACJ,KAAK,EAAE,IAAI,CAACnE,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACmD,IAAI,CAAC,EAAE,IAAI,CAACvB,GAAG,EAAE,IAAI,CAAC5B,GAAG,CAAC;IAChG;IACA;IACA,IAAI,IAAI,CAAC2D,WAAW,KAAK,GAAG,EAAE;MAC1B,IAAI,CAACP,MAAM,GAAG2C,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAAC1C,MAAM,GAAG0C,KAAK;IACvB;IACA;IACA,IAAI,CAACxC,WAAW,CAAC,CAAC;EACtB;EACAoC,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAMI,IAAI,GAAI,IAAI,CAACA,IAAI,GAAG,IAAI,CAAC3D,WAAW,CAACiE,qBAAqB,CAAC,CAAE;IACnE;IACA,IAAIL,KAAK,GAAGrH,uDAAK,CAAC,CAAC,EAAE,CAACgH,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAIhH,mDAAK,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MAChByD,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,CAACpC,WAAW,GAAG,CAAC,IAAI,CAAC7B,SAAS,IAAI/B,IAAI,CAACsG,GAAG,CAAC,IAAI,CAACjD,MAAM,GAAG2C,KAAK,CAAC,GAAGhG,IAAI,CAACsG,GAAG,CAAC,IAAI,CAAChD,MAAM,GAAG0C,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC3C,WAAW,CAAC;EACnC;EACA,IAAI4C,IAAIA,CAAA,EAAG;IACP,OAAOJ,YAAY,CAAC,IAAI,CAAC/C,MAAM,EAAE,IAAI,CAACxB,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACmD,IAAI,CAAC;EACnE;EACA,IAAIqD,IAAIA,CAAA,EAAG;IACP,OAAOL,YAAY,CAAC,IAAI,CAAC9C,MAAM,EAAE,IAAI,CAACzB,GAAG,EAAE,IAAI,CAAC5B,GAAG,EAAE,IAAI,CAACmD,IAAI,CAAC;EACnE;EACA,IAAIsD,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC3E,SAAS,EAAE;MAChB,OAAO/B,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACwB,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,MAAM;MAAEiB;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,IAAI,IAAI,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,OAAO4B,YAAY,CAAC5B,cAAc,EAAE,IAAI,CAAC1C,GAAG,EAAE,IAAI,CAAC5B,GAAG,CAAC;EAC3D;EACA,IAAI0G,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC5E,SAAS,EAAE;MAChB,OAAO/B,IAAI,CAACC,GAAG,CAAC,IAAI,CAACoD,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,OAAO,IAAI,CAACD,MAAM;EACtB;EACAuB,WAAWA,CAAA,EAAG;IACV,MAAMhD,KAAK,GAAG,IAAI,CAAC4D,QAAQ,CAAC,CAAC;IAC7B,MAAM;MAAE3D,GAAG;MAAE5B;IAAI,CAAC,GAAG,IAAI;IACzB,IAAI,IAAI,CAAC8B,SAAS,EAAE;MAChB,IAAI,CAACsB,MAAM,GAAG8C,YAAY,CAACvE,KAAK,CAACI,KAAK,EAAEH,GAAG,EAAE5B,GAAG,CAAC;MACjD,IAAI,CAACqD,MAAM,GAAG6C,YAAY,CAACvE,KAAK,CAACK,KAAK,EAAEJ,GAAG,EAAE5B,GAAG,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACoD,MAAM,GAAG8C,YAAY,CAACvE,KAAK,EAAEC,GAAG,EAAE5B,GAAG,CAAC;IAC/C;EACJ;EACAuD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAEkF,IAAI;MAAEC;IAAK,CAAC,GAAG,IAAI;IAC3B,IAAI,CAAC7E,KAAK,GAAG,CAAC,IAAI,CAACG,SAAS,GACtByE,IAAI,GACJ;MACExE,KAAK,EAAEhC,IAAI,CAAC6B,GAAG,CAAC2E,IAAI,EAAEC,IAAI,CAAC;MAC3BxE,KAAK,EAAEjC,IAAI,CAACC,GAAG,CAACuG,IAAI,EAAEC,IAAI;IAC9B,CAAC;IACL,IAAI,CAAC3F,QAAQ,CAACyC,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;IACzC,IAAI,CAACN,QAAQ,GAAG,KAAK;EACzB;EACAiF,QAAQA,CAACrD,IAAI,EAAE;IACX,IAAI,IAAI,CAACX,EAAE,CAACqE,UAAU,EAAE;MACpB,MAAMC,MAAM,GAAG,IAAI,CAACtE,EAAE,CAACqE,UAAU,CAACE,aAAa,CAAC5D,IAAI,KAAK,GAAG,GAAG,eAAe,GAAG,eAAe,CAAC;MACjG,IAAI2D,MAAM,EAAE;QACRA,MAAM,CAACE,KAAK,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;AACJ;AACA;EACI,IAAIC,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACzE,EAAE,CAACuE,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EAC3D;EACA;AACJ;AACA;EACI,IAAIG,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC1E,EAAE,CAACuE,aAAa,CAAC,cAAc,CAAC,KAAK,IAAI;EACzD;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjD,KAAK,KAAKJ,SAAS,IAAI,IAAI,CAACtB,EAAE,CAACuE,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EACvF;EACAK,iBAAiBA,CAAA,EAAG;IAChB,IAAI1B,EAAE;IACN,MAAM;MAAE5D,GAAG;MAAE5B,GAAG;MAAEmD,IAAI;MAAEH,cAAc;MAAEW,WAAW;MAAEZ,QAAQ;MAAEkB,GAAG;MAAEwC,UAAU;MAAEC,UAAU;MAAExC,YAAY;MAAE3C;IAAqB,CAAC,GAAG,IAAI;IACvI,IAAI4F,QAAQ,GAAI,GAAEV,UAAU,GAAG,GAAI,GAAE;IACrC,IAAIW,MAAM,GAAI,GAAE,GAAG,GAAGV,UAAU,GAAG,GAAI,GAAE;IACzC,MAAMW,GAAG,GAAGpI,mDAAK,CAAC,IAAI,CAACqD,EAAE,CAAC;IAC1B,MAAMgF,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;IACpC,MAAME,GAAG,GAAGF,GAAG,GAAG,MAAM,GAAG,OAAO;IAClC,MAAMG,SAAS,GAAIC,IAAI,IAAK;MACxB,OAAO;QACH,CAACH,KAAK,GAAGG,IAAI,CAACH,KAAK;MACvB,CAAC;IACL,CAAC;IACD,IAAI,IAAI,CAACxF,SAAS,KAAK,KAAK,EAAE;MAC1B;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACyE,IAAI,IAAI,CAACf,EAAE,GAAG,IAAI,CAAClB,cAAc,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC5D,GAAG,CAAC,EAAE;QACpF;AAChB;AACA;AACA;AACA;AACA;AACA;QACgBuF,QAAQ,GAAI,GAAET,UAAU,GAAG,GAAI,GAAE;QACjCU,MAAM,GAAI,GAAE,GAAG,GAAGX,UAAU,GAAG,GAAI,GAAE;MACzC,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;QACgBU,QAAQ,GAAI,GAAEV,UAAU,GAAG,GAAI,GAAE;QACjCW,MAAM,GAAI,GAAE,GAAG,GAAGV,UAAU,GAAG,GAAI,GAAE;MACzC;IACJ;IACA,MAAMgB,QAAQ,GAAG;MACb,CAACJ,KAAK,GAAGH,QAAQ;MACjB,CAACI,GAAG,GAAGH;IACX,CAAC;IACD,MAAM/C,KAAK,GAAG,EAAE;IAChB,IAAI,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,EAAE;MAC1B,KAAK,IAAI1C,KAAK,GAAGC,GAAG,EAAED,KAAK,IAAI3B,GAAG,EAAE2B,KAAK,IAAIwB,IAAI,EAAE;QAC/C,MAAM4C,KAAK,GAAGG,YAAY,CAACvE,KAAK,EAAEC,GAAG,EAAE5B,GAAG,CAAC;QAC3C,MAAM2H,QAAQ,GAAG5H,IAAI,CAAC6B,GAAG,CAAC6E,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAMkB,QAAQ,GAAG7H,IAAI,CAACC,GAAG,CAACyG,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAMe,IAAI,GAAG;UACT1B,KAAK;UACL;AACpB;AACA;AACA;UACoB8B,MAAM,EAAE9B,KAAK,IAAI4B,QAAQ,IAAI5B,KAAK,IAAI6B;QAC1C,CAAC;QACDH,IAAI,CAACH,KAAK,CAAC,GAAI,GAAEvB,KAAK,GAAG,GAAI,GAAE;QAC/B1B,KAAK,CAACyD,IAAI,CAACL,IAAI,CAAC;MACpB;IACJ;IACA,OAAQxJ,qDAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE,cAAc;MAAEC,GAAG,EAAGC,OAAO,IAAM,IAAI,CAAC9F,WAAW,GAAG8F,OAAQ;MACpF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYC,WAAW,EAAGtF,EAAE,IAAK;QACjB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACe,WAAW,KAAKC,SAAS,EAAE;UAChC,IAAI,CAAClB,OAAO,CAAC,CAAC;UACd,IAAI,CAACG,KAAK,CAACD,EAAE,CAAC;QAClB;MACJ;IAAE,CAAC,EAAEyB,KAAK,CAACpE,GAAG,CAAEwH,IAAI,IAAMxJ,qDAAC,CAAC,KAAK,EAAE;MAAEkK,KAAK,EAAEX,SAAS,CAACC,IAAI,CAAC;MAAEW,IAAI,EAAE,cAAc;MAAEL,KAAK,EAAE;QACtF,YAAY,EAAE,IAAI;QAClB,mBAAmB,EAAEN,IAAI,CAACI;MAC9B,CAAC;MAAEQ,IAAI,EAAEZ,IAAI,CAACI,MAAM,GAAG,aAAa,GAAG;IAAO,CAAC,CAAE,CAAC,EAAE5J,qDAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE;IAAsB,CAAC,EAAE9J,qDAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE,WAAW;MAAEK,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAEpK,qDAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE;QAClL,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,IAAI;QACxB,WAAW,EAAE1D,KAAK,CAAC1E,MAAM,GAAG;MAChC,CAAC;MAAEyI,IAAI,EAAE,cAAc;MAAED,KAAK,EAAET,QAAQ;MAAEW,IAAI,EAAE;IAAa,CAAC,CAAC,CAAC,EAAEC,UAAU,CAACjB,GAAG,EAAE;MAClFpE,IAAI,EAAE,GAAG;MACTsF,OAAO,EAAE5E,WAAW,KAAK,GAAG;MAC5BhC,KAAK,EAAE,IAAI,CAAC4E,IAAI;MAChBR,KAAK,EAAE,IAAI,CAAC3C,MAAM;MAClBa,GAAG;MACHC,YAAY;MACZnB,QAAQ;MACRC,cAAc;MACdpB,GAAG;MACH5B,GAAG;MACHuB;IACJ,CAAC,CAAC,EAAE,IAAI,CAACO,SAAS,IACdwG,UAAU,CAACjB,GAAG,EAAE;MACZpE,IAAI,EAAE,GAAG;MACTsF,OAAO,EAAE5E,WAAW,KAAK,GAAG;MAC5BhC,KAAK,EAAE,IAAI,CAAC6E,IAAI;MAChBT,KAAK,EAAE,IAAI,CAAC1C,MAAM;MAClBY,GAAG;MACHC,YAAY;MACZnB,QAAQ;MACRC,cAAc;MACdpB,GAAG;MACH5B,GAAG;MACHuB;IACJ,CAAC,CAAC,CAAC;EACX;EACAiH,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEzF,QAAQ;MAAET,EAAE;MAAE2E,QAAQ;MAAE/F,OAAO;MAAE+C,GAAG;MAAEN,WAAW;MAAEY,cAAc;MAAEP;IAAM,CAAC,GAAG,IAAI;IACzF,MAAMyE,MAAM,GAAGvJ,qDAAW,CAAC,UAAU,EAAEoD,EAAE,CAAC;IAC1C;AACR;AACA;AACA;IACQ,MAAMoG,eAAe,GAAIzB,QAAQ,KAAK1C,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,OAAO,CAAC,IAAK,IAAI,CAACwC,mBAAmB;IAC5H,MAAM4B,oBAAoB,GAAGF,MAAM,IAAI,CAACC,eAAe;IACvD;AACR;AACA;AACA;IACQ,MAAME,aAAa,GAAI3B,QAAQ,IAAI1C,cAAc,KAAK,KAAK,IAAK,IAAI,CAACyC,iBAAiB;IACtF,MAAM6B,kBAAkB,GAAGJ,MAAM,IAAI,CAACG,aAAa;IACnD,MAAME,IAAI,GAAGxJ,4DAAU,CAAC,IAAI,CAAC;IAC7BR,uDAAiB,CAAC,IAAI,EAAEwD,EAAE,EAAE,IAAI,CAACyB,IAAI,EAAEgF,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzD,QAAQ,CAAC,CAAC,CAAC,EAAExC,QAAQ,CAAC;IACjF,OAAQ9E,qDAAC,CAACE,iDAAI,EAAE;MAAE8K,GAAG,EAAE,0CAA0C;MAAEC,SAAS,EAAE,IAAI,CAACxF,OAAO;MAAEyF,UAAU,EAAE,IAAI,CAAC1F,MAAM;MAAE2F,EAAE,EAAElI,OAAO;MAAE6G,KAAK,EAAE3I,qDAAkB,CAAC,IAAI,CAACyE,KAAK,EAAE;QAChK,CAACiF,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEL,MAAM;QACjB,gBAAgB,EAAE1F,QAAQ;QAC1B,eAAe,EAAEY,WAAW,KAAKC,SAAS;QAC1C,eAAe,EAAEK,GAAG;QACpB,CAAE,yBAAwBM,cAAe,EAAC,GAAG,IAAI;QACjD,6BAA6B,EAAEoE,oBAAoB;QACnD,2BAA2B,EAAEE;MACjC,CAAC;IAAE,CAAC,EAAE5K,qDAAC,CAAC,OAAO,EAAE;MAAEgL,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE,eAAe;MAAEqB,EAAE,EAAE;IAAc,CAAC,EAAEnL,qDAAC,CAAC,KAAK,EAAE;MAAEgL,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE;QAChL,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACd;MAClC,CAAC;MAAEoB,IAAI,EAAE;IAAQ,CAAC,EAAErE,KAAK,KAAKJ,SAAS,GAAG3F,qDAAC,CAAC,KAAK,EAAE;MAAE8J,KAAK,EAAE;IAAa,CAAC,EAAE/D,KAAK,CAAC,GAAG/F,qDAAC,CAAC,MAAM,EAAE;MAAE8F,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAE9F,qDAAC,CAAC,KAAK,EAAE;MAAEgL,GAAG,EAAE,0CAA0C;MAAElB,KAAK,EAAE;IAAiB,CAAC,EAAE9J,qDAAC,CAAC,MAAM,EAAE;MAAEgL,GAAG,EAAE,0CAA0C;MAAElF,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE,IAAI,CAACmD,iBAAiB,CAAC,CAAC,EAAEjJ,qDAAC,CAAC,MAAM,EAAE;MAAEgL,GAAG,EAAE,0CAA0C;MAAElF,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1Y;EACA,IAAIzB,EAAEA,CAAA,EAAG;IAAE,OAAOjE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWgL,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,gBAAgB,EAAE,CAAC,uBAAuB,CAAC;MAC3C,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAMf,UAAU,GAAGA,CAACjB,GAAG,EAAE;EAAEpE,IAAI;EAAEtB,KAAK;EAAEoE,KAAK;EAAEnE,GAAG;EAAE5B,GAAG;EAAE+C,QAAQ;EAAEwF,OAAO;EAAEtE,GAAG;EAAEjB,cAAc;EAAEkB,YAAY;EAAE3C;AAAoB,CAAC,KAAK;EACrI,MAAM+F,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;EACpC,MAAMiC,SAAS,GAAGA,CAAA,KAAM;IACpB,MAAMnB,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACb,KAAK,CAAC,GAAI,GAAEvB,KAAK,GAAG,GAAI,GAAE;IAChC,OAAOoC,KAAK;EAChB,CAAC;EACD;EACA,MAAMoB,SAAS,GAAGhI,mBAAmB,CAAC,YAAY,CAAC;EACnD,OAAQtD,qDAAC,CAAC,KAAK,EAAE;IAAEuL,SAAS,EAAG5G,EAAE,IAAK;MAC9B,MAAMqG,GAAG,GAAGrG,EAAE,CAACqG,GAAG;MAClB,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,WAAW,EAAE;QAC5CjG,cAAc,CAACC,IAAI,EAAE,KAAK,CAAC;QAC3BL,EAAE,CAAC6G,cAAc,CAAC,CAAC;QACnB7G,EAAE,CAAC8G,eAAe,CAAC,CAAC;MACxB,CAAC,MACI,IAAIT,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,SAAS,EAAE;QAChDjG,cAAc,CAACC,IAAI,EAAE,IAAI,CAAC;QAC1BL,EAAE,CAAC6G,cAAc,CAAC,CAAC;QACnB7G,EAAE,CAAC8G,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;IAAE3B,KAAK,EAAE;MACN,mBAAmB,EAAE,IAAI;MACzB,cAAc,EAAE9E,IAAI,KAAK,GAAG;MAC5B,cAAc,EAAEA,IAAI,KAAK,GAAG;MAC5B,oBAAoB,EAAEsF,OAAO;MAC7B,gBAAgB,EAAE5G,KAAK,KAAKC,GAAG;MAC/B,gBAAgB,EAAED,KAAK,KAAK3B,GAAG;MAC/B,iBAAiB,EAAE,IAAI;MACvB,eAAe,EAAE;IACrB,CAAC;IAAEmI,KAAK,EAAEmB,SAAS,CAAC,CAAC;IAAElB,IAAI,EAAE,QAAQ;IAAEuB,QAAQ,EAAE5G,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAAE,YAAY,EAAEwG,SAAS,KAAK3F,SAAS,GAAG2F,SAAS,GAAG,IAAI;IAAE,iBAAiB,EAAEA,SAAS,KAAK3F,SAAS,GAAG,aAAa,GAAG,IAAI;IAAE,eAAe,EAAEhC,GAAG;IAAE,eAAe,EAAE5B,GAAG;IAAE,eAAe,EAAE+C,QAAQ,GAAG,MAAM,GAAG,IAAI;IAAE,eAAe,EAAEpB;EAAM,CAAC,EAAEsC,GAAG,IAAKhG,qDAAC,CAAC,KAAK,EAAE;IAAE8J,KAAK,EAAE,WAAW;IAAEK,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAM,CAAC,EAAEnE,YAAY,CAACvC,KAAK,CAAC,CAAE,EAAE1D,qDAAC,CAAC,KAAK,EAAE;IAAE8J,KAAK,EAAE,YAAY;IAAEK,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAO,CAAC,CAAC,CAAC;AACne,CAAC;AACD,MAAMlC,YAAY,GAAGA,CAACJ,KAAK,EAAEnE,GAAG,EAAE5B,GAAG,EAAEmD,IAAI,KAAK;EAC5C,IAAIxB,KAAK,GAAG,CAAC3B,GAAG,GAAG4B,GAAG,IAAImE,KAAK;EAC/B,IAAI5C,IAAI,GAAG,CAAC,EAAE;IACV;IACAxB,KAAK,GAAG5B,IAAI,CAACoE,KAAK,CAACxC,KAAK,GAAGwB,IAAI,CAAC,GAAGA,IAAI,GAAGvB,GAAG;EACjD;EACA,MAAMgI,YAAY,GAAGlL,uDAAK,CAACkD,GAAG,EAAED,KAAK,EAAE3B,GAAG,CAAC;EAC3C,OAAOJ,uBAAuB,CAACgK,YAAY,EAAEhI,GAAG,EAAE5B,GAAG,EAAEmD,IAAI,CAAC;AAChE,CAAC;AACD,MAAM+C,YAAY,GAAGA,CAACvE,KAAK,EAAEC,GAAG,EAAE5B,GAAG,KAAK;EACtC,OAAOtB,uDAAK,CAAC,CAAC,EAAE,CAACiD,KAAK,GAAGC,GAAG,KAAK5B,GAAG,GAAG4B,GAAG,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC;AACD,IAAIT,QAAQ,GAAG,CAAC;AAChBX,KAAK,CAAC2H,KAAK,GAAG;EACV0B,GAAG,EAAExJ,iBAAiB;EACtByJ,EAAE,EAAEvJ;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-range.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-5cc724f3.js';\nimport { j as clamp, e as debounceEvent, i as inheritAriaAttributes, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nfunction getDecimalPlaces(n) {\n    if (n % 1 === 0)\n        return 0;\n    return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n    const maxPlaces = Math.max(...references.map((r) => getDecimalPlaces(r)));\n    return Number(n.toFixed(maxPlaces));\n}\n\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:2px;--height:42px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, var(--ion-background-color-step-900, #e6e6e6));pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\nconst IonRangeIosStyle0 = rangeIosCss;\n\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}.range-knob-handle{inset-inline-start:0}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}.range-bar-container{inset-inline-start:0}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.26);--bar-background-active:var(--ion-color-primary, #0054e9);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #0054e9);--pin-color:var(--ion-color-primary-contrast, #fff)}::slotted(:not(ion-icon)[slot=start]),::slotted(:not(ion-icon)[slot=end]),.native-wrapper{font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}.range-knob::before{inset-inline-start:0}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}.range-pin::before{inset-inline-start:50%}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, var(--ion-background-color-step-250, #bfbfbf))}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\nconst IonRangeMdStyle0 = rangeMdCss;\n\nconst Range = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n        this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n        this.rangeId = `ion-r-${rangeIds++}`;\n        this.didLoad = false;\n        this.noUpdate = false;\n        this.hasFocus = false;\n        this.inheritedAttributes = {};\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        this.clampBounds = (value) => {\n            return clamp(this.min, value, this.max);\n        };\n        this.ensureValueInBounds = (value) => {\n            if (this.dualKnobs) {\n                return {\n                    lower: this.clampBounds(value.lower),\n                    upper: this.clampBounds(value.upper),\n                };\n            }\n            else {\n                return this.clampBounds(value);\n            }\n        };\n        this.setupGesture = async () => {\n            const rangeSlider = this.rangeSlider;\n            if (rangeSlider) {\n                this.gesture = (await import('./index-39782642.js')).createGesture({\n                    el: rangeSlider,\n                    gestureName: 'range',\n                    gesturePriority: 100,\n                    /**\n                     * Provide a threshold since the drag movement\n                     * might be a user scrolling the view.\n                     * If this is true, then the range\n                     * should not move.\n                     */\n                    threshold: 10,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.gesture.enable(!this.disabled);\n            }\n        };\n        this.handleKeyboard = (knob, isIncrease) => {\n            const { ensureValueInBounds } = this;\n            let step = this.step;\n            step = step > 0 ? step : 1;\n            step = step / (this.max - this.min);\n            if (!isIncrease) {\n                step *= -1;\n            }\n            if (knob === 'A') {\n                this.ratioA = clamp(0, this.ratioA + step, 1);\n            }\n            else {\n                this.ratioB = clamp(0, this.ratioB + step, 1);\n            }\n            this.ionKnobMoveStart.emit({ value: ensureValueInBounds(this.value) });\n            this.updateValue();\n            this.emitValueChange();\n            this.ionKnobMoveEnd.emit({ value: ensureValueInBounds(this.value) });\n        };\n        this.onBlur = () => {\n            if (this.hasFocus) {\n                this.hasFocus = false;\n                this.ionBlur.emit();\n            }\n        };\n        this.onFocus = () => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n            }\n        };\n        this.ratioA = 0;\n        this.ratioB = 0;\n        this.pressedKnob = undefined;\n        this.color = undefined;\n        this.debounce = undefined;\n        this.name = this.rangeId;\n        this.label = undefined;\n        this.dualKnobs = false;\n        this.min = 0;\n        this.max = 100;\n        this.pin = false;\n        this.pinFormatter = (value) => Math.round(value);\n        this.snaps = false;\n        this.step = 1;\n        this.ticks = true;\n        this.activeBarStart = undefined;\n        this.disabled = false;\n        this.value = 0;\n        this.labelPlacement = 'start';\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    minChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    maxChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    activeBarStartChanged() {\n        const { activeBarStart } = this;\n        if (activeBarStart !== undefined) {\n            if (activeBarStart > this.max) {\n                printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.max;\n            }\n            else if (activeBarStart < this.min) {\n                printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.min;\n            }\n        }\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    valueChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    componentWillLoad() {\n        /**\n         * If user has custom ID set then we should\n         * not assign the default incrementing ID.\n         */\n        if (this.el.hasAttribute('id')) {\n            this.rangeId = this.el.getAttribute('id');\n        }\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.setupGesture();\n        this.updateRatio();\n        this.didLoad = true;\n    }\n    connectedCallback() {\n        this.updateRatio();\n        this.debounceChanged();\n        this.disabledChanged();\n        this.activeBarStartChanged();\n        /**\n         * If we have not yet rendered\n         * ion-range, then rangeSlider is not defined.\n         * But if we are moving ion-range via appendChild,\n         * then rangeSlider will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n        this.contentEl = findClosestIonContent(this.el);\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    getValue() {\n        var _a;\n        const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n        if (this.dualKnobs) {\n            if (typeof value === 'object') {\n                return value;\n            }\n            return {\n                lower: 0,\n                upper: value,\n            };\n        }\n        else {\n            if (typeof value === 'object') {\n                return value.upper;\n            }\n            return value;\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange() {\n        this.value = this.ensureValueInBounds(this.value);\n        this.ionChange.emit({ value: this.value });\n    }\n    /**\n     * The value should be updated on touch end or\n     * when the component is being dragged.\n     * This follows the native behavior of mobile devices.\n     *\n     * For example: When the user lifts their finger from the\n     * screen after tapping the bar or dragging the bar or knob.\n     */\n    onStart() {\n        this.ionKnobMoveStart.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    /**\n     * The value should be updated while dragging the\n     * bar or knob.\n     *\n     * While the user is dragging, the view\n     * should not scroll. This is to prevent the user from\n     * feeling disoriented while dragging.\n     *\n     * The user can scroll on the view if the knob or\n     * bar is not being dragged.\n     *\n     * @param detail The details of the gesture event.\n     */\n    onMove(detail) {\n        const { contentEl, pressedKnob } = this;\n        const currentX = detail.currentX;\n        /**\n         * Since the user is dragging on the bar or knob, the view should not scroll.\n         *\n         * This only needs to be done once.\n         */\n        if (contentEl && this.initialContentScrollY === undefined) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        /**\n         * The `pressedKnob` can be undefined if the user just\n         * started dragging the knob.\n         *\n         * This is necessary to determine which knob the user is dragging,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         *\n         * This only needs to be done once since the knob won't change\n         * while the user is dragging.\n         */\n        if (pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        this.update(currentX);\n    }\n    /**\n     * The value should be updated on touch end:\n     * - When the user lifts their finger from the screen after\n     * tapping the bar.\n     *\n     * @param detail The details of the gesture or mouse event.\n     */\n    onEnd(detail) {\n        const { contentEl, initialContentScrollY } = this;\n        const currentX = detail.currentX || detail.clientX;\n        /**\n         * The `pressedKnob` can be undefined if the user never\n         * dragged the knob. They just tapped on the bar.\n         *\n         * This is necessary to determine which knob the user is changing,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         */\n        if (this.pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        /**\n         * The user is no longer dragging the bar or\n         * knob (if they were dragging it).\n         *\n         * The user can now scroll on the view in the next gesture event.\n         */\n        if (contentEl && initialContentScrollY !== undefined) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        // update the active knob's position\n        this.update(currentX);\n        /**\n         * Reset the pressed knob to undefined since the user\n         * may start dragging a different knob in the next gesture event.\n         */\n        this.pressedKnob = undefined;\n        this.emitValueChange();\n        this.ionKnobMoveEnd.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    update(currentX) {\n        // figure out where the pointer is currently at\n        // update the knob being interacted with\n        const rect = this.rect;\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        if (this.snaps) {\n            // snaps the ratio to the current value\n            ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n        }\n        // update which knob is pressed\n        if (this.pressedKnob === 'A') {\n            this.ratioA = ratio;\n        }\n        else {\n            this.ratioB = ratio;\n        }\n        // Update input value\n        this.updateValue();\n    }\n    setPressedKnob(currentX) {\n        const rect = (this.rect = this.rangeSlider.getBoundingClientRect());\n        // figure out which knob they started closer to\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n        this.setFocus(this.pressedKnob);\n    }\n    get valA() {\n        return ratioToValue(this.ratioA, this.min, this.max, this.step);\n    }\n    get valB() {\n        return ratioToValue(this.ratioB, this.min, this.max, this.step);\n    }\n    get ratioLower() {\n        if (this.dualKnobs) {\n            return Math.min(this.ratioA, this.ratioB);\n        }\n        const { activeBarStart } = this;\n        if (activeBarStart == null) {\n            return 0;\n        }\n        return valueToRatio(activeBarStart, this.min, this.max);\n    }\n    get ratioUpper() {\n        if (this.dualKnobs) {\n            return Math.max(this.ratioA, this.ratioB);\n        }\n        return this.ratioA;\n    }\n    updateRatio() {\n        const value = this.getValue();\n        const { min, max } = this;\n        if (this.dualKnobs) {\n            this.ratioA = valueToRatio(value.lower, min, max);\n            this.ratioB = valueToRatio(value.upper, min, max);\n        }\n        else {\n            this.ratioA = valueToRatio(value, min, max);\n        }\n    }\n    updateValue() {\n        this.noUpdate = true;\n        const { valA, valB } = this;\n        this.value = !this.dualKnobs\n            ? valA\n            : {\n                lower: Math.min(valA, valB),\n                upper: Math.max(valA, valB),\n            };\n        this.ionInput.emit({ value: this.value });\n        this.noUpdate = false;\n    }\n    setFocus(knob) {\n        if (this.el.shadowRoot) {\n            const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n            if (knobEl) {\n                knobEl.focus();\n            }\n        }\n    }\n    /**\n     * Returns true if content was passed to the \"start\" slot\n     */\n    get hasStartSlotContent() {\n        return this.el.querySelector('[slot=\"start\"]') !== null;\n    }\n    /**\n     * Returns true if content was passed to the \"end\" slot\n     */\n    get hasEndSlotContent() {\n        return this.el.querySelector('[slot=\"end\"]') !== null;\n    }\n    get hasLabel() {\n        return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n    }\n    renderRangeSlider() {\n        var _a;\n        const { min, max, step, handleKeyboard, pressedKnob, disabled, pin, ratioLower, ratioUpper, pinFormatter, inheritedAttributes, } = this;\n        let barStart = `${ratioLower * 100}%`;\n        let barEnd = `${100 - ratioUpper * 100}%`;\n        const rtl = isRTL(this.el);\n        const start = rtl ? 'right' : 'left';\n        const end = rtl ? 'left' : 'right';\n        const tickStyle = (tick) => {\n            return {\n                [start]: tick[start],\n            };\n        };\n        if (this.dualKnobs === false) {\n            /**\n             * When the value is less than the activeBarStart or the min value,\n             * the knob will display at the start of the active bar.\n             */\n            if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n                /**\n                 * Sets the bar positions relative to the upper and lower limits.\n                 * Converts the ratio values into percentages, used as offsets for left/right styles.\n                 *\n                 * The ratioUpper refers to the knob position on the bar.\n                 * The ratioLower refers to the end position of the active bar (the value).\n                 */\n                barStart = `${ratioUpper * 100}%`;\n                barEnd = `${100 - ratioLower * 100}%`;\n            }\n            else {\n                /**\n                 * Otherwise, the knob will display at the end of the active bar.\n                 *\n                 * The ratioLower refers to the start position of the active bar (the value).\n                 * The ratioUpper refers to the knob position on the bar.\n                 */\n                barStart = `${ratioLower * 100}%`;\n                barEnd = `${100 - ratioUpper * 100}%`;\n            }\n        }\n        const barStyle = {\n            [start]: barStart,\n            [end]: barEnd,\n        };\n        const ticks = [];\n        if (this.snaps && this.ticks) {\n            for (let value = min; value <= max; value += step) {\n                const ratio = valueToRatio(value, min, max);\n                const ratioMin = Math.min(ratioLower, ratioUpper);\n                const ratioMax = Math.max(ratioLower, ratioUpper);\n                const tick = {\n                    ratio,\n                    /**\n                     * Sets the tick mark as active when the tick is between the min bounds and the knob.\n                     * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n                     */\n                    active: ratio >= ratioMin && ratio <= ratioMax,\n                };\n                tick[start] = `${ratio * 100}%`;\n                ticks.push(tick);\n            }\n        }\n        return (h(\"div\", { class: \"range-slider\", ref: (rangeEl) => (this.rangeSlider = rangeEl),\n            /**\n             * Since the gesture has a threshold, the value\n             * won't change until the user has dragged past\n             * the threshold. This is to prevent the range\n             * from moving when the user is scrolling.\n             *\n             * This results in the value not being updated\n             * and the event emitters not being triggered\n             * if the user taps on the range. This is why\n             * we need to listen for the \"pointerUp\" event.\n             */\n            onPointerUp: (ev) => {\n                /**\n                 * If the user drags the knob on the web\n                 * version (does not occur on mobile),\n                 * the \"pointerUp\" event will be triggered\n                 * along with the gesture's events.\n                 * This leads to duplicate events.\n                 *\n                 * By checking if the pressedKnob is undefined,\n                 * we can determine if the \"pointerUp\" event was\n                 * triggered by a tap or a drag. If it was\n                 * dragged, the pressedKnob will be defined.\n                 */\n                if (this.pressedKnob === undefined) {\n                    this.onStart();\n                    this.onEnd(ev);\n                }\n            } }, ticks.map((tick) => (h(\"div\", { style: tickStyle(tick), role: \"presentation\", class: {\n                'range-tick': true,\n                'range-tick-active': tick.active,\n            }, part: tick.active ? 'tick-active' : 'tick' }))), h(\"div\", { class: \"range-bar-container\" }, h(\"div\", { class: \"range-bar\", role: \"presentation\", part: \"bar\" }), h(\"div\", { class: {\n                'range-bar': true,\n                'range-bar-active': true,\n                'has-ticks': ticks.length > 0,\n            }, role: \"presentation\", style: barStyle, part: \"bar-active\" })), renderKnob(rtl, {\n            knob: 'A',\n            pressed: pressedKnob === 'A',\n            value: this.valA,\n            ratio: this.ratioA,\n            pin,\n            pinFormatter,\n            disabled,\n            handleKeyboard,\n            min,\n            max,\n            inheritedAttributes,\n        }), this.dualKnobs &&\n            renderKnob(rtl, {\n                knob: 'B',\n                pressed: pressedKnob === 'B',\n                value: this.valB,\n                ratio: this.ratioB,\n                pin,\n                pinFormatter,\n                disabled,\n                handleKeyboard,\n                min,\n                max,\n                inheritedAttributes,\n            })));\n    }\n    render() {\n        const { disabled, el, hasLabel, rangeId, pin, pressedKnob, labelPlacement, label } = this;\n        const inItem = hostContext('ion-item', el);\n        /**\n         * If there is no start content then the knob at\n         * the min value will be cut off by the item margin.\n         */\n        const hasStartContent = (hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed')) || this.hasStartSlotContent;\n        const needsStartAdjustment = inItem && !hasStartContent;\n        /**\n         * If there is no end content then the knob at\n         * the max value will be cut off by the item margin.\n         */\n        const hasEndContent = (hasLabel && labelPlacement === 'end') || this.hasEndSlotContent;\n        const needsEndAdjustment = inItem && !hasEndContent;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n        return (h(Host, { key: '7ad5ca892fd46c64ce5583e91e6812410a869921', onFocusin: this.onFocus, onFocusout: this.onBlur, id: rangeId, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'range-disabled': disabled,\n                'range-pressed': pressedKnob !== undefined,\n                'range-has-pin': pin,\n                [`range-label-placement-${labelPlacement}`]: true,\n                'range-item-start-adjustment': needsStartAdjustment,\n                'range-item-end-adjustment': needsEndAdjustment,\n            }) }, h(\"label\", { key: 'eb8d46a9572341b4cc3c91d8a5de0667bd166ca4', class: \"range-wrapper\", id: \"range-label\" }, h(\"div\", { key: '21acd6ff6eba17bf6923bf723fea055b95fd29ca', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, label !== undefined ? h(\"div\", { class: \"label-text\" }, label) : h(\"slot\", { name: \"label\" })), h(\"div\", { key: '7644c86a6ad3edf2c27f02dd2a14795b3720ea0d', class: \"native-wrapper\" }, h(\"slot\", { key: '1bbeccee57addc126b36de395e28ba655248695b', name: \"start\" }), this.renderRangeSlider(), h(\"slot\", { key: '2963dd84e4116fc3956c304eb53deefd0d1291df', name: \"end\" })))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"activeBarStart\": [\"activeBarStartChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nconst renderKnob = (rtl, { knob, value, ratio, min, max, disabled, pressed, pin, handleKeyboard, pinFormatter, inheritedAttributes }) => {\n    const start = rtl ? 'right' : 'left';\n    const knobStyle = () => {\n        const style = {};\n        style[start] = `${ratio * 100}%`;\n        return style;\n    };\n    // The aria label should be preferred over visible text if both are specified\n    const ariaLabel = inheritedAttributes['aria-label'];\n    return (h(\"div\", { onKeyDown: (ev) => {\n            const key = ev.key;\n            if (key === 'ArrowLeft' || key === 'ArrowDown') {\n                handleKeyboard(knob, false);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            else if (key === 'ArrowRight' || key === 'ArrowUp') {\n                handleKeyboard(knob, true);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n        }, class: {\n            'range-knob-handle': true,\n            'range-knob-a': knob === 'A',\n            'range-knob-b': knob === 'B',\n            'range-knob-pressed': pressed,\n            'range-knob-min': value === min,\n            'range-knob-max': value === max,\n            'ion-activatable': true,\n            'ion-focusable': true,\n        }, style: knobStyle(), role: \"slider\", tabindex: disabled ? -1 : 0, \"aria-label\": ariaLabel !== undefined ? ariaLabel : null, \"aria-labelledby\": ariaLabel === undefined ? 'range-label' : null, \"aria-valuemin\": min, \"aria-valuemax\": max, \"aria-disabled\": disabled ? 'true' : null, \"aria-valuenow\": value }, pin && (h(\"div\", { class: \"range-pin\", role: \"presentation\", part: \"pin\" }, pinFormatter(value))), h(\"div\", { class: \"range-knob\", role: \"presentation\", part: \"knob\" })));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n    let value = (max - min) * ratio;\n    if (step > 0) {\n        // round to nearest multiple of step, then add min\n        value = Math.round(value / step) * step + min;\n    }\n    const clampedValue = clamp(min, value, max);\n    return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n    return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n    ios: IonRangeIosStyle0,\n    md: IonRangeMdStyle0\n};\n\nexport { Range as ion_range };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "findClosestIonContent", "disableContentScrollY", "resetContentScrollY", "j", "clamp", "e", "debounceEvent", "inheritAriaAttributes", "renderHiddenInput", "p", "printIonWarning", "isRTL", "hostContext", "c", "createColorClasses", "b", "getIonMode", "getDecimalPlaces", "n", "toString", "split", "length", "roundToMaxDecimalPlaces", "references", "maxPlaces", "Math", "max", "map", "Number", "toFixed", "rangeIosCss", "IonRangeIosStyle0", "rangeMdCss", "IonRangeMdStyle0", "Range", "constructor", "hostRef", "_this", "ionChange", "ionInput", "ionFocus", "ionBlur", "ionKnobMoveStart", "ionKnobMoveEnd", "rangeId", "rangeIds", "didLoad", "noUpdate", "hasFocus", "inheritedAttributes", "contentEl", "initialContentScrollY", "clampBounds", "value", "min", "ensureValueInBounds", "dualKnobs", "lower", "upper", "setupGesture", "_asyncToGenerator", "rangeSlider", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "onStart", "onMove", "ev", "onEnd", "enable", "disabled", "handleKeyboard", "knob", "isIncrease", "step", "ratioA", "ratioB", "emit", "updateValue", "emitValueChange", "onBlur", "onFocus", "pressedKnob", "undefined", "color", "debounce", "name", "label", "pin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "round", "snaps", "ticks", "activeBarStart", "labelPlacement", "debounce<PERSON><PERSON>ed", "originalIonInput", "minC<PERSON>ed", "updateRatio", "max<PERSON><PERSON>ed", "activeBarStartChanged", "disabled<PERSON><PERSON>ed", "valueChanged", "componentWillLoad", "hasAttribute", "getAttribute", "componentDidLoad", "connectedCallback", "disconnectedCallback", "destroy", "getValue", "_a", "detail", "currentX", "setPressedKnob", "update", "clientX", "rect", "ratio", "left", "width", "valueToRatio", "ratioToValue", "getBoundingClientRect", "abs", "setFocus", "valA", "valB", "ratioLower", "ratioUpper", "shadowRoot", "knobEl", "querySelector", "focus", "hasStartSlotContent", "hasEndSlotContent", "<PERSON><PERSON><PERSON><PERSON>", "renderRangeSlider", "barStart", "barEnd", "rtl", "start", "end", "tickStyle", "tick", "barStyle", "ratioMin", "ratioMax", "active", "push", "class", "ref", "rangeEl", "onPointerUp", "style", "role", "part", "renderKnob", "pressed", "render", "inItem", "hasStartContent", "needsStartAdjustment", "hasEndContent", "needsEndAdjustment", "mode", "JSON", "stringify", "key", "onFocusin", "onFocusout", "id", "watchers", "knobStyle", "aria<PERSON><PERSON><PERSON>", "onKeyDown", "preventDefault", "stopPropagation", "tabindex", "clampedValue", "ios", "md", "ion_range"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}