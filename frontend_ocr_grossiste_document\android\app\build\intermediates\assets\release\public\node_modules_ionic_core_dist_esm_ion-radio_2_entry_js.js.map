{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-radio_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACG;AACvC;AACO;AACnB;AAE7D,MAAMmB,WAAW,GAAG,ohKAAohK;AACxiK,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,uwLAAuwL;AAC1xL,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBxB,qDAAgB,CAAC,IAAI,EAAEwB,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAGvB,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACwB,OAAO,GAAGxB,qDAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACyB,OAAO,GAAI,UAASC,cAAc,EAAG,EAAC;IAC3C,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,IAAI,CAACD,UAAU,EAAE;QACjB,MAAM;UAAEE,WAAW;UAAEC,KAAK,EAAEC;QAAgB,CAAC,GAAG,IAAI,CAACJ,UAAU;QAC/D,IAAI,CAACK,OAAO,GAAGrB,kEAAgB,CAACoB,eAAe,EAAE,IAAI,CAACD,KAAK,EAAED,WAAW,CAAC;MAC7E;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,MAAM;QAAEN,UAAU;QAAEK,OAAO;QAAEE;MAAS,CAAC,GAAG,IAAI;MAC9C,IAAIA,QAAQ,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIF,OAAO,KAAKL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACQ,mBAAmB,CAAC,EAAE;QACrG,IAAI,CAACH,OAAO,GAAG,KAAK;MACxB,CAAC,MACI;QACD,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;IACJ,CAAC;IACD,IAAI,CAACI,OAAO,GAAG,MAAM;MACjB,IAAI,CAACb,QAAQ,CAACc,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACd,OAAO,CAACa,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACL,OAAO,GAAG,KAAK;IACpB,IAAI,CAACO,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACjB,OAAO;IACxB,IAAI,CAACS,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,KAAK,GAAGW,SAAS;IACtB,IAAI,CAACE,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;EAC7B;EACAC,YAAYA,CAAA,EAAG;IACX;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAClB,WAAW,CAAC,CAAC;EACtB;EACAmB,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACnB,WAAW,CAAC,CAAC;EACtB;EACA;EACMoB,QAAQA,CAACC,EAAE,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACfF,EAAE,CAACG,eAAe,CAAC,CAAC;MACpBH,EAAE,CAACI,cAAc,CAAC,CAAC;MACnBH,KAAI,CAACI,EAAE,CAACC,KAAK,CAAC,CAAC;IAAC;EACpB;EACA;EACMC,iBAAiBA,CAAC1B,KAAK,EAAE;IAAA,IAAA2B,MAAA;IAAA,OAAAN,6OAAA;MAC3BM,MAAI,CAAClB,cAAc,GAAGT,KAAK;IAAC;EAChC;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC5B,KAAK,KAAKW,SAAS,EAAE;MAC1B,IAAI,CAACX,KAAK,GAAG,IAAI,CAACL,OAAO;IAC7B;IACA,MAAME,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG,IAAI,CAAC2B,EAAE,CAACK,OAAO,CAAC,iBAAiB,CAAE;IACzE,IAAIhC,UAAU,EAAE;MACZ,IAAI,CAACC,WAAW,CAAC,CAAC;MAClBrB,uDAAgB,CAACoB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACC,WAAW,CAAC;IACpE;EACJ;EACAgC,oBAAoBA,CAAA,EAAG;IACnB,MAAMjC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAIA,UAAU,EAAE;MACZlB,uDAAmB,CAACkB,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAACC,WAAW,CAAC;MACnE,IAAI,CAACD,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA,IAAIkC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,EAAE,CAACQ,WAAW,KAAK,EAAE;EACrC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,OAAQ9D,qDAAC,CAAC,KAAK,EAAE;MAAE+D,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAY,CAAC,EAAEhE,qDAAC,CAAC,KAAK,EAAE;MAAE+D,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC,EAAEhE,qDAAC,CAAC,KAAK,EAAE;MAAE+D,KAAK,EAAE;IAAe,CAAC,CAAC,CAAC;EACvJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElC,OAAO;MAAEE,QAAQ;MAAEM,KAAK;MAAEc,EAAE;MAAEV,OAAO;MAAED,cAAc;MAAEkB,QAAQ;MAAEtB,cAAc;MAAEM;IAAU,CAAC,GAAG,IAAI;IAC3G,MAAMsB,IAAI,GAAGpD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMqD,MAAM,GAAGxD,qDAAW,CAAC,UAAU,EAAE0C,EAAE,CAAC;IAC1C,OAAQrD,qDAAC,CAACE,iDAAI,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEjC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE+B,KAAK,EAAElD,qDAAkB,CAAC0B,KAAK,EAAE;QAC/J,CAAC2B,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEC,MAAM;QACjB,eAAe,EAAEpC,OAAO;QACxB,gBAAgB,EAAEE,QAAQ;QAC1B,CAAE,iBAAgBU,OAAQ,EAAC,GAAG,IAAI;QAClC,CAAE,mBAAkBC,SAAU,EAAC,GAAG,IAAI;QACtC,CAAE,yBAAwBF,cAAe,EAAC,GAAG,IAAI;QACjD;QACA,iBAAiB,EAAE,CAACyB,MAAM;QAC1B,eAAe,EAAE,CAACA;MACtB,CAAC,CAAC;MAAEE,IAAI,EAAE,OAAO;MAAE,cAAc,EAAEtC,OAAO,GAAG,MAAM,GAAG,OAAO;MAAE,eAAe,EAAEE,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEqC,QAAQ,EAAEhC;IAAe,CAAC,EAAEtC,qDAAC,CAAC,OAAO,EAAE;MAAEoE,GAAG,EAAE,0CAA0C;MAAEL,KAAK,EAAE;IAAgB,CAAC,EAAE/D,qDAAC,CAAC,KAAK,EAAE;MAAEoE,GAAG,EAAE,0CAA0C;MAAEL,KAAK,EAAE;QAC7R,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACH;MAClC,CAAC;MAAEI,IAAI,EAAE;IAAQ,CAAC,EAAEhE,qDAAC,CAAC,MAAM,EAAE;MAAEoE,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAEpE,qDAAC,CAAC,KAAK,EAAE;MAAEoE,GAAG,EAAE,0CAA0C;MAAEL,KAAK,EAAE;IAAiB,CAAC,EAAE,IAAI,CAACD,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAChN;EACA,IAAIT,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWmE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAI9C,cAAc,GAAG,CAAC;AACtBN,KAAK,CAACqD,KAAK,GAAG;EACVC,GAAG,EAAEzD,iBAAiB;EACtB0D,EAAE,EAAExD;AACR,CAAC;AAED,MAAMyD,UAAU,GAAG,MAAM;EACrBvD,WAAWA,CAACC,OAAO,EAAE;IACjBxB,qDAAgB,CAAC,IAAI,EAAEwB,OAAO,CAAC;IAC/B,IAAI,CAACuD,SAAS,GAAG7E,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC8E,cAAc,GAAG9E,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACyB,OAAO,GAAI,UAASsD,aAAa,EAAG,EAAC;IAC1C,IAAI,CAACC,OAAO,GAAI,GAAE,IAAI,CAACvD,OAAQ,MAAK;IACpC,IAAI,CAACwD,gBAAgB,GAAInD,KAAK,IAAK;MAC/B,MAAMoD,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAC/B;MACA,MAAMC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAK,CAACA,KAAK,CAACpD,QAAQ,CAAC;MACrD,MAAMF,OAAO,GAAGkD,MAAM,CAACG,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACxD,KAAK,KAAKA,KAAK,IAAI,CAACwD,KAAK,CAACpD,QAAQ,CAAC;MAChF,IAAI,CAACkD,KAAK,IAAI,CAACpD,OAAO,EAAE;QACpB;MACJ;MACA;MACA;MACA,MAAMuD,SAAS,GAAGvD,OAAO,IAAIoD,KAAK;MAClC,KAAK,MAAME,KAAK,IAAIJ,MAAM,EAAE;QACxB,MAAMX,QAAQ,GAAGe,KAAK,KAAKC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7CD,KAAK,CAAC9B,iBAAiB,CAACe,QAAQ,CAAC;MACrC;IACJ,CAAC;IACD,IAAI,CAACtC,OAAO,GAAIgB,EAAE,IAAK;MACnBA,EAAE,CAACI,cAAc,CAAC,CAAC;MACnB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMmC,aAAa,GAAGvC,EAAE,CAACwC,MAAM,IAAIxC,EAAE,CAACwC,MAAM,CAAC9B,OAAO,CAAC,WAAW,CAAC;MACjE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI6B,aAAa,IAAI,CAACA,aAAa,CAACtD,QAAQ,EAAE;QAC1C,MAAMwD,YAAY,GAAG,IAAI,CAAC5D,KAAK;QAC/B,MAAM6D,QAAQ,GAAGH,aAAa,CAAC1D,KAAK;QACpC,IAAI6D,QAAQ,KAAKD,YAAY,EAAE;UAC3B,IAAI,CAAC5D,KAAK,GAAG6D,QAAQ;UACrB,IAAI,CAACC,eAAe,CAAC3C,EAAE,CAAC;QAC5B,CAAC,MACI,IAAI,IAAI,CAACd,mBAAmB,EAAE;UAC/B,IAAI,CAACL,KAAK,GAAGW,SAAS;UACtB,IAAI,CAACmD,eAAe,CAAC3C,EAAE,CAAC;QAC5B;MACJ;IACJ,CAAC;IACD,IAAI,CAACd,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACN,WAAW,GAAGY,SAAS;IAC5B,IAAI,CAACC,IAAI,GAAG,IAAI,CAACjB,OAAO;IACxB,IAAI,CAACK,KAAK,GAAGW,SAAS;EAC1B;EACAK,YAAYA,CAAChB,KAAK,EAAE;IAChB,IAAI,CAACmD,gBAAgB,CAACnD,KAAK,CAAC;IAC5B,IAAI,CAACgD,cAAc,CAACzC,IAAI,CAAC;MAAEP;IAAM,CAAC,CAAC;EACvC;EACAiB,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACD,YAAY,CAAC,IAAI,CAAChB,KAAK,CAAC;EACjC;EACM4B,iBAAiBA,CAAA,EAAG;IAAA,IAAAmC,MAAA;IAAA,OAAA1C,6OAAA;MACtB;MACA;MACA,MAAM2C,MAAM,GAAGD,MAAI,CAACvC,EAAE,CAACyC,aAAa,CAAC,iBAAiB,CAAC,IAAIF,MAAI,CAACvC,EAAE,CAACyC,aAAa,CAAC,kBAAkB,CAAC;MACpG,IAAID,MAAM,EAAE;QACR,MAAME,KAAK,GAAIH,MAAI,CAACG,KAAK,GAAGF,MAAM,CAACC,aAAa,CAAC,WAAW,CAAE;QAC9D,IAAIC,KAAK,EAAE;UACPH,MAAI,CAACb,OAAO,GAAGgB,KAAK,CAACC,EAAE,GAAGJ,MAAI,CAACnD,IAAI,GAAG,MAAM;QAChD;MACJ;IAAC;EACL;EACAyC,SAASA,CAAA,EAAG;IACR,OAAOe,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7C,EAAE,CAAC8C,gBAAgB,CAAC,WAAW,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,eAAeA,CAACS,KAAK,EAAE;IACnB,MAAM;MAAEvE;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC+C,SAAS,CAACxC,IAAI,CAAC;MAAEP,KAAK;MAAEuE;IAAM,CAAC,CAAC;EACzC;EACAC,SAASA,CAACrD,EAAE,EAAE;IACV,MAAMsD,eAAe,GAAG,CAAC,CAAC,IAAI,CAACjD,EAAE,CAACK,OAAO,CAAC,oBAAoB,CAAC;IAC/D,IAAIV,EAAE,CAACwC,MAAM,IAAI,CAAC,IAAI,CAACnC,EAAE,CAACkD,QAAQ,CAACvD,EAAE,CAACwC,MAAM,CAAC,EAAE;MAC3C;IACJ;IACA;IACA;IACA,MAAMP,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAACsB,MAAM,CAAEnB,KAAK,IAAK,CAACA,KAAK,CAACpD,QAAQ,CAAC;IAClE;IACA,IAAIe,EAAE,CAACwC,MAAM,IAAIP,MAAM,CAACwB,QAAQ,CAACzD,EAAE,CAACwC,MAAM,CAAC,EAAE;MACzC,MAAMkB,KAAK,GAAGzB,MAAM,CAAC0B,SAAS,CAAEtB,KAAK,IAAKA,KAAK,KAAKrC,EAAE,CAACwC,MAAM,CAAC;MAC9D,MAAMoB,OAAO,GAAG3B,MAAM,CAACyB,KAAK,CAAC;MAC7B,IAAIG,IAAI;MACR;MACA;MACA,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACJ,QAAQ,CAACzD,EAAE,CAACoB,GAAG,CAAC,EAAE;QAC9CyC,IAAI,GAAGH,KAAK,KAAKzB,MAAM,CAAC6B,MAAM,GAAG,CAAC,GAAG7B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAACyB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACD,QAAQ,CAACzD,EAAE,CAACoB,GAAG,CAAC,EAAE;QAC3CyC,IAAI,GAAGH,KAAK,KAAK,CAAC,GAAGzB,MAAM,CAACA,MAAM,CAAC6B,MAAM,GAAG,CAAC,CAAC,GAAG7B,MAAM,CAACyB,KAAK,GAAG,CAAC,CAAC;MACtE;MACA,IAAIG,IAAI,IAAI5B,MAAM,CAACwB,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC/BA,IAAI,CAAC9D,QAAQ,CAACC,EAAE,CAAC;QACjB,IAAI,CAACsD,eAAe,EAAE;UAClB,IAAI,CAACzE,KAAK,GAAGgF,IAAI,CAAChF,KAAK;UACvB,IAAI,CAAC8D,eAAe,CAAC3C,EAAE,CAAC;QAC5B;MACJ;MACA;MACA;MACA,IAAI,CAAC,GAAG,CAAC,CAACyD,QAAQ,CAACzD,EAAE,CAACoB,GAAG,CAAC,EAAE;QACxB,MAAM2C,aAAa,GAAG,IAAI,CAAClF,KAAK;QAChC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACK,mBAAmB,IAAI,IAAI,CAACL,KAAK,KAAKW,SAAS,GAAGA,SAAS,GAAGoE,OAAO,CAAC/E,KAAK;QAC7F,IAAIkF,aAAa,KAAK,IAAI,CAAClF,KAAK,IAAI,IAAI,CAACK,mBAAmB,EAAE;UAC1D;AACpB;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACyD,eAAe,CAAC3C,EAAE,CAAC;QAC5B;QACA;QACA;QACAA,EAAE,CAACI,cAAc,CAAC,CAAC;MACvB;IACJ;EACJ;EACAa,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE8B,KAAK;MAAEhB,OAAO;MAAE1B,EAAE;MAAEZ,IAAI;MAAEZ;IAAM,CAAC,GAAG,IAAI;IAChD,MAAMqC,IAAI,GAAGpD,4DAAU,CAAC,IAAI,CAAC;IAC7BL,uDAAiB,CAAC,IAAI,EAAE4C,EAAE,EAAEZ,IAAI,EAAEZ,KAAK,EAAE,KAAK,CAAC;IAC/C,OAAO7B,qDAAC,CAACE,iDAAI,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,YAAY;MAAE,iBAAiB,EAAE0B,KAAK,GAAGhB,OAAO,GAAG,IAAI;MAAE/C,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE+B,KAAK,EAAEG;IAAK,CAAC,CAAC;EAC1K;EACA,IAAIb,EAAEA,CAAA,EAAG;IAAE,OAAOjD,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWmE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIO,aAAa,GAAG,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-radio_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-c71c5417.js';\nimport { a as addEventListener, b as removeEventListener, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { i as isOptionSelected } from './compare-with-utils-a96ff2ea.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\n\nconst radioIosCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color-checked:var(--ion-color-primary, #0054e9)}:host(.ion-color.radio-checked) .radio-inner{border-color:var(--ion-color-base)}.item-radio.item-ios ion-label{-webkit-margin-start:0;margin-inline-start:0}.radio-inner{width:33%;height:50%}:host(.radio-checked) .radio-inner{-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--color-checked)}:host(.radio-disabled){opacity:0.3}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);top:-8px;display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}:host(.ion-focused) .radio-icon::after{inset-inline-start:-9px}.native-wrapper .radio-icon{width:0.9375rem;height:1.5rem}\";\nconst IonRadioIosStyle0 = radioIosCss;\n\nconst radioMdCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--border-width:0.125rem;--border-style:solid;--border-radius:50%}:host(.ion-color) .radio-inner{background:var(--ion-color-base)}:host(.ion-color.radio-checked) .radio-icon{border-color:var(--ion-color-base)}.radio-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--color)}.radio-inner{border-radius:var(--inner-border-radius);width:calc(50% + var(--border-width));height:calc(50% + var(--border-width));-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background:var(--color-checked)}:host(.radio-checked) .radio-icon{border-color:var(--color-checked)}:host(.radio-checked) .radio-inner{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}:host(.radio-disabled) .label-text-wrapper{opacity:0.38}:host(.radio-disabled) .native-wrapper{opacity:0.63}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}.native-wrapper .radio-icon{width:1.25rem;height:1.25rem}\";\nconst IonRadioMdStyle0 = radioMdCss;\n\nconst Radio = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-rb-${radioButtonIds++}`;\n        this.radioGroup = null;\n        this.updateState = () => {\n            if (this.radioGroup) {\n                const { compareWith, value: radioGroupValue } = this.radioGroup;\n                this.checked = isOptionSelected(radioGroupValue, this.value, compareWith);\n            }\n        };\n        this.onClick = () => {\n            const { radioGroup, checked, disabled } = this;\n            if (disabled) {\n                return;\n            }\n            /**\n             * The modern control does not use a native input\n             * inside of the radio host, so we cannot rely on the\n             * ev.preventDefault() behavior above. If the radio\n             * is checked and the parent radio group allows for empty\n             * selection, then we can set the checked state to false.\n             * Otherwise, the checked state should always be set\n             * to true because the checked state cannot be toggled.\n             */\n            if (checked && (radioGroup === null || radioGroup === void 0 ? void 0 : radioGroup.allowEmptySelection)) {\n                this.checked = false;\n            }\n            else {\n                this.checked = true;\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.checked = false;\n        this.buttonTabindex = -1;\n        this.color = undefined;\n        this.name = this.inputId;\n        this.disabled = false;\n        this.value = undefined;\n        this.labelPlacement = 'start';\n        this.justify = 'space-between';\n        this.alignment = 'center';\n    }\n    valueChanged() {\n        /**\n         * The new value of the radio may\n         * match the radio group's value,\n         * so we see if it should be checked.\n         */\n        this.updateState();\n    }\n    componentDidLoad() {\n        /**\n         * The value may be `undefined` if it\n         * gets set before the radio is\n         * rendered. This ensures that the radio\n         * is checked if the value matches. This\n         * happens most often when Angular is\n         * rendering the radio.\n         */\n        this.updateState();\n    }\n    /** @internal */\n    async setFocus(ev) {\n        ev.stopPropagation();\n        ev.preventDefault();\n        this.el.focus();\n    }\n    /** @internal */\n    async setButtonTabindex(value) {\n        this.buttonTabindex = value;\n    }\n    connectedCallback() {\n        if (this.value === undefined) {\n            this.value = this.inputId;\n        }\n        const radioGroup = (this.radioGroup = this.el.closest('ion-radio-group'));\n        if (radioGroup) {\n            this.updateState();\n            addEventListener(radioGroup, 'ionValueChange', this.updateState);\n        }\n    }\n    disconnectedCallback() {\n        const radioGroup = this.radioGroup;\n        if (radioGroup) {\n            removeEventListener(radioGroup, 'ionValueChange', this.updateState);\n            this.radioGroup = null;\n        }\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    renderRadioControl() {\n        return (h(\"div\", { class: \"radio-icon\", part: \"container\" }, h(\"div\", { class: \"radio-inner\", part: \"mark\" }), h(\"div\", { class: \"radio-ripple\" })));\n    }\n    render() {\n        const { checked, disabled, color, el, justify, labelPlacement, hasLabel, buttonTabindex, alignment } = this;\n        const mode = getIonMode(this);\n        const inItem = hostContext('ion-item', el);\n        return (h(Host, { key: '43c95effb6acb119733270e8a5e02fe18b47ee4b', onFocus: this.onFocus, onBlur: this.onBlur, onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': inItem,\n                'radio-checked': checked,\n                'radio-disabled': disabled,\n                [`radio-justify-${justify}`]: true,\n                [`radio-alignment-${alignment}`]: true,\n                [`radio-label-placement-${labelPlacement}`]: true,\n                // Focus and active styling should not apply when the radio is in an item\n                'ion-activatable': !inItem,\n                'ion-focusable': !inItem,\n            }), role: \"radio\", \"aria-checked\": checked ? 'true' : 'false', \"aria-disabled\": disabled ? 'true' : null, tabindex: buttonTabindex }, h(\"label\", { key: '168ea77c08fde9832077d617662b93dc9986b544', class: \"radio-wrapper\" }, h(\"div\", { key: 'ce1e6eee63fbab1e5a66b18f767f4eecc2a227f1', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, h(\"slot\", { key: '7d849e43d4356b5c3978c2bba9a6fba5d21ca69d' })), h(\"div\", { key: 'e741f7822364958b050a232bdfccdc413095c244', class: \"native-wrapper\" }, this.renderRadioControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioButtonIds = 0;\nRadio.style = {\n    ios: IonRadioIosStyle0,\n    md: IonRadioMdStyle0\n};\n\nconst RadioGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.inputId = `ion-rg-${radioGroupIds++}`;\n        this.labelId = `${this.inputId}-lbl`;\n        this.setRadioTabindex = (value) => {\n            const radios = this.getRadios();\n            // Get the first radio that is not disabled and the checked one\n            const first = radios.find((radio) => !radio.disabled);\n            const checked = radios.find((radio) => radio.value === value && !radio.disabled);\n            if (!first && !checked) {\n                return;\n            }\n            // If an enabled checked radio exists, set it to be the focusable radio\n            // otherwise we default to focus the first radio\n            const focusable = checked || first;\n            for (const radio of radios) {\n                const tabindex = radio === focusable ? 0 : -1;\n                radio.setButtonTabindex(tabindex);\n            }\n        };\n        this.onClick = (ev) => {\n            ev.preventDefault();\n            /**\n             * The Radio Group component mandates that only one radio button\n             * within the group can be selected at any given time. Since `ion-radio`\n             * is a shadow DOM component, it cannot natively perform this behavior\n             * using the `name` attribute.\n             */\n            const selectedRadio = ev.target && ev.target.closest('ion-radio');\n            /**\n             * Our current disabled prop definition causes Stencil to mark it\n             * as optional. While this is not desired, fixing this behavior\n             * in Stencil is a significant breaking change, so this effort is\n             * being de-risked in STENCIL-917. Until then, we compromise\n             * here by checking for falsy `disabled` values instead of strictly\n             * checking `disabled === false`.\n             */\n            if (selectedRadio && !selectedRadio.disabled) {\n                const currentValue = this.value;\n                const newValue = selectedRadio.value;\n                if (newValue !== currentValue) {\n                    this.value = newValue;\n                    this.emitValueChange(ev);\n                }\n                else if (this.allowEmptySelection) {\n                    this.value = undefined;\n                    this.emitValueChange(ev);\n                }\n            }\n        };\n        this.allowEmptySelection = false;\n        this.compareWith = undefined;\n        this.name = this.inputId;\n        this.value = undefined;\n    }\n    valueChanged(value) {\n        this.setRadioTabindex(value);\n        this.ionValueChange.emit({ value });\n    }\n    componentDidLoad() {\n        /**\n         * There's an issue when assigning a value to the radio group\n         * within the Angular primary content (rendering within the\n         * app component template). When the template is isolated to a route,\n         * the value is assigned correctly.\n         * To address this issue, we need to ensure that the watcher is\n         * called after the component has finished loading,\n         * allowing the emit to be dispatched correctly.\n         */\n        this.valueChanged(this.value);\n    }\n    async connectedCallback() {\n        // Get the list header if it exists and set the id\n        // this is used to set aria-labelledby\n        const header = this.el.querySelector('ion-list-header') || this.el.querySelector('ion-item-divider');\n        if (header) {\n            const label = (this.label = header.querySelector('ion-label'));\n            if (label) {\n                this.labelId = label.id = this.name + '-lbl';\n            }\n        }\n    }\n    getRadios() {\n        return Array.from(this.el.querySelectorAll('ion-radio'));\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        this.ionChange.emit({ value, event });\n    }\n    onKeydown(ev) {\n        const inSelectPopover = !!this.el.closest('ion-select-popover');\n        if (ev.target && !this.el.contains(ev.target)) {\n            return;\n        }\n        // Get all radios inside of the radio group and then\n        // filter out disabled radios since we need to skip those\n        const radios = this.getRadios().filter((radio) => !radio.disabled);\n        // Only move the radio if the current focus is in the radio group\n        if (ev.target && radios.includes(ev.target)) {\n            const index = radios.findIndex((radio) => radio === ev.target);\n            const current = radios[index];\n            let next;\n            // If hitting arrow down or arrow right, move to the next radio\n            // If we're on the last radio, move to the first radio\n            if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n                next = index === radios.length - 1 ? radios[0] : radios[index + 1];\n            }\n            // If hitting arrow up or arrow left, move to the previous radio\n            // If we're on the first radio, move to the last radio\n            if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n                next = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n            }\n            if (next && radios.includes(next)) {\n                next.setFocus(ev);\n                if (!inSelectPopover) {\n                    this.value = next.value;\n                    this.emitValueChange(ev);\n                }\n            }\n            // Update the radio group value when a user presses the\n            // space bar on top of a selected radio\n            if ([' '].includes(ev.key)) {\n                const previousValue = this.value;\n                this.value = this.allowEmptySelection && this.value !== undefined ? undefined : current.value;\n                if (previousValue !== this.value || this.allowEmptySelection) {\n                    /**\n                     * Value change should only be emitted if the value is different,\n                     * such as selecting a new radio with the space bar or if\n                     * the radio group allows for empty selection and the user\n                     * is deselecting a checked radio.\n                     */\n                    this.emitValueChange(ev);\n                }\n                // Prevent browsers from jumping\n                // to the bottom of the screen\n                ev.preventDefault();\n            }\n        }\n    }\n    render() {\n        const { label, labelId, el, name, value } = this;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, name, value, false);\n        return h(Host, { key: '7a8ad7eb6a05c6f96a348dcf30fd0c610a95688c', role: \"radiogroup\", \"aria-labelledby\": label ? labelId : null, onClick: this.onClick, class: mode });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioGroupIds = 0;\n\nexport { Radio as ion_radio, RadioGroup as ion_radio_group };\n"], "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "a", "addEventListener", "b", "removeEventListener", "renderHiddenInput", "isOptionSelected", "hostContext", "c", "createColorClasses", "getIonMode", "radioIosCss", "IonRadioIosStyle0", "radioMdCss", "IonRadioMdStyle0", "Radio", "constructor", "hostRef", "ionFocus", "ionBlur", "inputId", "radioButtonIds", "radioGroup", "updateState", "compareWith", "value", "radioGroupValue", "checked", "onClick", "disabled", "allowEmptySelection", "onFocus", "emit", "onBlur", "buttonTabindex", "color", "undefined", "name", "labelPlacement", "justify", "alignment", "valueChanged", "componentDidLoad", "setFocus", "ev", "_this", "_asyncToGenerator", "stopPropagation", "preventDefault", "el", "focus", "setButtonTabindex", "_this2", "connectedCallback", "closest", "disconnectedCallback", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "renderRadioControl", "class", "part", "render", "mode", "inItem", "key", "role", "tabindex", "watchers", "style", "ios", "md", "RadioGroup", "ionChange", "ionValueChange", "radioGroupIds", "labelId", "setRadioTabindex", "radios", "getRadios", "first", "find", "radio", "focusable", "selectedRadio", "target", "currentValue", "newValue", "emitValueChange", "_this3", "header", "querySelector", "label", "id", "Array", "from", "querySelectorAll", "event", "onKeydown", "inSelectPopover", "contains", "filter", "includes", "index", "findIndex", "current", "next", "length", "previousValue", "ion_radio", "ion_radio_group"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}