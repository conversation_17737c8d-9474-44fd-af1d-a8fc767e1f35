{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-tab_2_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC6G;AACrC;AACzC;AAE/B,MAAMW,MAAM,GAAG,6CAA6C;AAC5D,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAGC,SAAS;IACzB,IAAI,CAACC,GAAG,GAAGD,SAAS;IACpB,IAAI,CAACE,SAAS,GAAGF,SAAS;EAC9B;EACMG,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACtB,IAAID,KAAI,CAACN,MAAM,EAAE;QACb,MAAMM,KAAI,CAACE,SAAS,CAAC,CAAC;MAC1B;IAAC;EACL;EACA;EACMA,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAF,6OAAA;MACd,MAAME,MAAI,CAACC,iBAAiB,CAAC,CAAC;MAC9BD,MAAI,CAACT,MAAM,GAAG,IAAI;IAAC;EACvB;EACAW,YAAYA,CAACC,QAAQ,EAAE;IACnB,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACF,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACX,MAAM,IAAI,IAAI,CAACK,SAAS,IAAI,IAAI,EAAE;MACxC,IAAI,CAACL,MAAM,GAAG,IAAI;MAClB,IAAI;QACA,OAAON,kEAAe,CAAC,IAAI,CAACQ,QAAQ,EAAE,IAAI,CAACY,EAAE,EAAE,IAAI,CAACT,SAAS,EAAE,CAAC,UAAU,CAAC,CAAC;MAChF,CAAC,CACD,OAAOU,CAAC,EAAE;QACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MACpB;IACJ;IACA,OAAOG,OAAO,CAACC,OAAO,CAAChB,SAAS,CAAC;EACrC;EACAiB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEhB,GAAG;MAAEH,MAAM;MAAEI;IAAU,CAAC,GAAG,IAAI;IACvC,OAAQnB,qDAAC,CAACE,iDAAI,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEC,IAAI,EAAE,UAAU;MAAE,aAAa,EAAE,CAACrB,MAAM,GAAG,MAAM,GAAG,IAAI;MAAE,iBAAiB,EAAG,cAAaG,GAAI,EAAC;MAAEmB,KAAK,EAAE;QACpK,UAAU,EAAElB,SAAS,KAAKF,SAAS;QACnC,YAAY,EAAE,CAACF;MACnB;IAAE,CAAC,EAAEf,qDAAC,CAAC,MAAM,EAAE;MAAEmC,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIP,EAAEA,CAAA,EAAG;IAAE,OAAOxB,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkC,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,cAAc;IAC7B,CAAC;EAAE;AACP,CAAC;AACD3B,GAAG,CAAC4B,KAAK,GAAG7B,YAAY;AAExB,MAAM8B,OAAO,GAAG,8QAA8Q;AAC9R,MAAMC,aAAa,GAAGD,OAAO;AAE7B,MAAME,IAAI,GAAG,MAAM;EACf9B,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAAC8B,cAAc,GAAGrC,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACsC,iBAAiB,GAAGtC,qDAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAACuC,gBAAgB,GAAGvC,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACwC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAIC,EAAE,IAAK;MACxB,MAAM;QAAEC,IAAI;QAAE/B;MAAI,CAAC,GAAG8B,EAAE,CAACE,MAAM;MAC/B,IAAI,IAAI,CAACC,SAAS,IAAIF,IAAI,KAAKhC,SAAS,EAAE;QACtC,MAAMmC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;QACnD,IAAIF,MAAM,EAAE;UACRA,MAAM,CAACG,IAAI,CAACN,IAAI,CAAC;QACrB;MACJ,CAAC,MACI;QACD,IAAI,CAACO,MAAM,CAACtC,GAAG,CAAC;MACpB;IACJ,CAAC;IACD,IAAI,CAACuC,WAAW,GAAGxC,SAAS;IAC5B,IAAI,CAACkC,SAAS,GAAG,KAAK;EAC1B;EACM/B,iBAAiBA,CAAA,EAAG;IAAA,IAAAsC,MAAA;IAAA,OAAApC,6OAAA;MACtB,IAAI,CAACoC,MAAI,CAACP,SAAS,EAAE;QACjBO,MAAI,CAACP,SAAS,GAAG,CAAC,CAACE,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,IAAI,CAACI,MAAI,CAAC9B,EAAE,CAAC+B,OAAO,CAAC,aAAa,CAAC;MAC9F;MACA,IAAI,CAACD,MAAI,CAACP,SAAS,EAAE;QACjB,MAAMS,IAAI,GAAGF,MAAI,CAACE,IAAI;QACtB,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UACjB,MAAMH,MAAI,CAACF,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B;MACJ;MACAF,MAAI,CAACf,cAAc,CAACmB,IAAI,CAAC,CAAC;IAAC;EAC/B;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,MAAM,GAAG,IAAI,CAACpC,EAAE,CAAC0B,aAAa,CAAC,aAAa,CAAC;IACnD,IAAIU,MAAM,EAAE;MACR,MAAM9C,GAAG,GAAG,IAAI,CAACuC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACvC,GAAG,GAAGD,SAAS;MAC/D+C,MAAM,CAACP,WAAW,GAAGvC,GAAG;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACUsC,MAAMA,CAACtC,GAAG,EAAE;IAAA,IAAA+C,MAAA;IAAA,OAAA3C,6OAAA;MACd,MAAMmC,WAAW,GAAGS,MAAM,CAACD,MAAI,CAACL,IAAI,EAAE1C,GAAG,CAAC;MAC1C,IAAI,CAAC+C,MAAI,CAACE,YAAY,CAACV,WAAW,CAAC,EAAE;QACjC,OAAO,KAAK;MAChB;MACA,MAAMQ,MAAI,CAAC1C,SAAS,CAACkC,WAAW,CAAC;MACjC,MAAMQ,MAAI,CAACG,YAAY,CAAC,CAAC;MACzBH,MAAI,CAACI,SAAS,CAAC,CAAC;MAChB,OAAO,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;EACUH,MAAMA,CAAChD,GAAG,EAAE;IAAA,IAAAoD,MAAA;IAAA,OAAAhD,6OAAA;MACd,OAAO4C,MAAM,CAACI,MAAI,CAACV,IAAI,EAAE1C,GAAG,CAAC;IAAC;EAClC;EACA;AACJ;AACA;EACIqD,WAAWA,CAAA,EAAG;IACV,OAAOvC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACwB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACvC,GAAG,GAAGD,SAAS,CAAC;EAC/E;EACA;EACMuD,UAAUA,CAACC,EAAE,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApD,6OAAA;MACjB,MAAMmC,WAAW,GAAGS,MAAM,CAACQ,MAAI,CAACd,IAAI,EAAEa,EAAE,CAAC;MACzC,IAAI,CAACC,MAAI,CAACP,YAAY,CAACV,WAAW,CAAC,EAAE;QACjC,OAAO;UAAEkB,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAEF,MAAI,CAACjB;QAAY,CAAC;MACxD;MACA,MAAMiB,MAAI,CAACnD,SAAS,CAACkC,WAAW,CAAC;MACjC,OAAO;QACHkB,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEF,MAAI,CAACjB,WAAW;QACzBoB,WAAW,EAAEA,CAAA,KAAMH,MAAI,CAACL,SAAS,CAAC;MACtC,CAAC;IAAC;EACN;EACA;EACMS,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzD,6OAAA;MACf,IAAI0D,EAAE;MACN,MAAMC,KAAK,GAAG,CAACD,EAAE,GAAGD,MAAI,CAACtB,WAAW,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9D,GAAG;MACjF,OAAO+D,KAAK,KAAKhE,SAAS,GAAG;QAAEwD,EAAE,EAAEQ,KAAK;QAAEL,OAAO,EAAEG,MAAI,CAACtB;MAAY,CAAC,GAAGxC,SAAS;IAAC;EACtF;EACAM,SAASA,CAACkC,WAAW,EAAE;IACnB,IAAI,IAAI,CAACX,aAAa,EAAE;MACpB,OAAOd,OAAO,CAACkD,MAAM,CAAC,iCAAiC,CAAC;IAC5D;IACA,IAAI,CAACpC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACqC,UAAU,GAAG,IAAI,CAAC1B,WAAW;IAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACb,iBAAiB,CAACkB,IAAI,CAAC;MAAE5C,GAAG,EAAEuC,WAAW,CAACvC;IAAI,CAAC,CAAC;IACrDuC,WAAW,CAAC1C,MAAM,GAAG,IAAI;IACzB,OAAOiB,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACAoC,SAASA,CAAA,EAAG;IACR,MAAMZ,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM0B,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACA,UAAU,GAAGlE,SAAS;IAC3B,IAAI,CAAC6B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACW,WAAW,EAAE;MACd;IACJ;IACA,IAAI0B,UAAU,KAAK1B,WAAW,EAAE;MAC5B,IAAI0B,UAAU,EAAE;QACZA,UAAU,CAACpE,MAAM,GAAG,KAAK;MAC7B;MACA,IAAI,CAAC8B,gBAAgB,CAACiB,IAAI,CAAC;QAAE5C,GAAG,EAAEuC,WAAW,CAACvC;MAAI,CAAC,CAAC;IACxD;EACJ;EACAkD,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACR,OAAOA,MAAM,CAACgC,UAAU,CAAC,SAAS,CAAC;MACvC;IACJ;IACA,OAAOpD,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;EACjC;EACAkC,YAAYA,CAACV,WAAW,EAAE;IACtB,MAAM0B,UAAU,GAAG,IAAI,CAAC1B,WAAW;IACnC,OAAOA,WAAW,KAAKxC,SAAS,IAAIwC,WAAW,KAAK0B,UAAU,IAAI,CAAC,IAAI,CAACrC,aAAa;EACzF;EACA,IAAIc,IAAIA,CAAA,EAAG;IACP,OAAOyB,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,EAAE,CAAC2D,gBAAgB,CAAC,SAAS,CAAC,CAAC;EAC1D;EACArD,MAAMA,CAAA,EAAG;IACL,OAAQlC,qDAAC,CAACE,iDAAI,EAAE;MAAEiC,GAAG,EAAE,0CAA0C;MAAEqD,mBAAmB,EAAE,IAAI,CAACzC;IAAa,CAAC,EAAE/C,qDAAC,CAAC,MAAM,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAEsD,IAAI,EAAE;IAAM,CAAC,CAAC,EAAEzF,qDAAC,CAAC,KAAK,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAEE,KAAK,EAAE;IAAa,CAAC,EAAErC,qDAAC,CAAC,MAAM,EAAE;MAAEmC,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAEnC,qDAAC,CAAC,MAAM,EAAE;MAAEmC,GAAG,EAAE,0CAA0C;MAAEsD,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC;EACja;EACA,IAAI7D,EAAEA,CAAA,EAAG;IAAE,OAAOxB,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAM8D,MAAM,GAAGA,CAACN,IAAI,EAAE1C,GAAG,KAAK;EAC1B,MAAMwE,KAAK,GAAG,OAAOxE,GAAG,KAAK,QAAQ,GAAG0C,IAAI,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC1E,GAAG,KAAKA,GAAG,CAAC,GAAGA,GAAG;EAC7E,IAAI,CAACwE,KAAK,EAAE;IACR5D,OAAO,CAACC,KAAK,CAAE,iBAAgB2D,KAAM,kBAAiB,CAAC;EAC3D;EACA,OAAOA,KAAK;AAChB,CAAC;AACDhD,IAAI,CAACH,KAAK,GAAGE,aAAa", "sources": ["./node_modules/@ionic/core/dist/esm/ion-tab_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement, d as createEvent } from './index-c71c5417.js';\nimport { a as attachComponent } from './framework-delegate-63d1a679.js';\nimport './helpers-da915de8.js';\n\nconst tabCss = \":host(.tab-hidden){display:none !important}\";\nconst IonTabStyle0 = tabCss;\n\nconst Tab = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.loaded = false;\n        this.active = false;\n        this.delegate = undefined;\n        this.tab = undefined;\n        this.component = undefined;\n    }\n    async componentWillLoad() {\n        if (this.active) {\n            await this.setActive();\n        }\n    }\n    /** Set the active component for the tab */\n    async setActive() {\n        await this.prepareLazyLoaded();\n        this.active = true;\n    }\n    changeActive(isActive) {\n        if (isActive) {\n            this.prepareLazyLoaded();\n        }\n    }\n    prepareLazyLoaded() {\n        if (!this.loaded && this.component != null) {\n            this.loaded = true;\n            try {\n                return attachComponent(this.delegate, this.el, this.component, ['ion-page']);\n            }\n            catch (e) {\n                console.error(e);\n            }\n        }\n        return Promise.resolve(undefined);\n    }\n    render() {\n        const { tab, active, component } = this;\n        return (h(Host, { key: '46d5498418f5379861c3d7465e8021dec45f1200', role: \"tabpanel\", \"aria-hidden\": !active ? 'true' : null, \"aria-labelledby\": `tab-button-${tab}`, class: {\n                'ion-page': component === undefined,\n                'tab-hidden': !active,\n            } }, h(\"slot\", { key: 'b45045dccb87dfe75e3f5a5a474bb48b6c98e922' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"active\": [\"changeActive\"]\n    }; }\n};\nTab.style = IonTabStyle0;\n\nconst tabsCss = \":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}\";\nconst IonTabsStyle0 = tabsCss;\n\nconst Tabs = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionTabsWillChange = createEvent(this, \"ionTabsWillChange\", 3);\n        this.ionTabsDidChange = createEvent(this, \"ionTabsDidChange\", 3);\n        this.transitioning = false;\n        this.onTabClicked = (ev) => {\n            const { href, tab } = ev.detail;\n            if (this.useRouter && href !== undefined) {\n                const router = document.querySelector('ion-router');\n                if (router) {\n                    router.push(href);\n                }\n            }\n            else {\n                this.select(tab);\n            }\n        };\n        this.selectedTab = undefined;\n        this.useRouter = false;\n    }\n    async componentWillLoad() {\n        if (!this.useRouter) {\n            this.useRouter = !!document.querySelector('ion-router') && !this.el.closest('[no-router]');\n        }\n        if (!this.useRouter) {\n            const tabs = this.tabs;\n            if (tabs.length > 0) {\n                await this.select(tabs[0]);\n            }\n        }\n        this.ionNavWillLoad.emit();\n    }\n    componentWillRender() {\n        const tabBar = this.el.querySelector('ion-tab-bar');\n        if (tabBar) {\n            const tab = this.selectedTab ? this.selectedTab.tab : undefined;\n            tabBar.selectedTab = tab;\n        }\n    }\n    /**\n     * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     *\n     * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n     */\n    async select(tab) {\n        const selectedTab = getTab(this.tabs, tab);\n        if (!this.shouldSwitch(selectedTab)) {\n            return false;\n        }\n        await this.setActive(selectedTab);\n        await this.notifyRouter();\n        this.tabSwitch();\n        return true;\n    }\n    /**\n     * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     *\n     * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n     */\n    async getTab(tab) {\n        return getTab(this.tabs, tab);\n    }\n    /**\n     * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     */\n    getSelected() {\n        return Promise.resolve(this.selectedTab ? this.selectedTab.tab : undefined);\n    }\n    /** @internal */\n    async setRouteId(id) {\n        const selectedTab = getTab(this.tabs, id);\n        if (!this.shouldSwitch(selectedTab)) {\n            return { changed: false, element: this.selectedTab };\n        }\n        await this.setActive(selectedTab);\n        return {\n            changed: true,\n            element: this.selectedTab,\n            markVisible: () => this.tabSwitch(),\n        };\n    }\n    /** @internal */\n    async getRouteId() {\n        var _a;\n        const tabId = (_a = this.selectedTab) === null || _a === void 0 ? void 0 : _a.tab;\n        return tabId !== undefined ? { id: tabId, element: this.selectedTab } : undefined;\n    }\n    setActive(selectedTab) {\n        if (this.transitioning) {\n            return Promise.reject('transitioning already happening');\n        }\n        this.transitioning = true;\n        this.leavingTab = this.selectedTab;\n        this.selectedTab = selectedTab;\n        this.ionTabsWillChange.emit({ tab: selectedTab.tab });\n        selectedTab.active = true;\n        return Promise.resolve();\n    }\n    tabSwitch() {\n        const selectedTab = this.selectedTab;\n        const leavingTab = this.leavingTab;\n        this.leavingTab = undefined;\n        this.transitioning = false;\n        if (!selectedTab) {\n            return;\n        }\n        if (leavingTab !== selectedTab) {\n            if (leavingTab) {\n                leavingTab.active = false;\n            }\n            this.ionTabsDidChange.emit({ tab: selectedTab.tab });\n        }\n    }\n    notifyRouter() {\n        if (this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                return router.navChanged('forward');\n            }\n        }\n        return Promise.resolve(false);\n    }\n    shouldSwitch(selectedTab) {\n        const leavingTab = this.selectedTab;\n        return selectedTab !== undefined && selectedTab !== leavingTab && !this.transitioning;\n    }\n    get tabs() {\n        return Array.from(this.el.querySelectorAll('ion-tab'));\n    }\n    render() {\n        return (h(Host, { key: '239bdb2ff2ec5cfcc74c51812cf6240f48acc617', onIonTabButtonClick: this.onTabClicked }, h(\"slot\", { key: '2b266d0f87c7c82e992fe0d9b5c8f25569b118e3', name: \"top\" }), h(\"div\", { key: '4cbbd010f0b0b1cd82714db7d098d28fab6d5218', class: \"tabs-inner\" }, h(\"slot\", { key: '72b2a02cd29d5b30e47758f3d6daaa1021916256' })), h(\"slot\", { key: '25494076dbbf7606d477b44f1b969719e7b4a805', name: \"bottom\" })));\n    }\n    get el() { return getElement(this); }\n};\nconst getTab = (tabs, tab) => {\n    const tabEl = typeof tab === 'string' ? tabs.find((t) => t.tab === tab) : tab;\n    if (!tabEl) {\n        console.error(`tab with id: \"${tabEl}\" does not exist`);\n    }\n    return tabEl;\n};\nTabs.style = IonTabsStyle0;\n\nexport { Tab as ion_tab, Tabs as ion_tabs };\n"], "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "d", "createEvent", "a", "attachComponent", "tabCss", "IonTabStyle0", "Tab", "constructor", "hostRef", "loaded", "active", "delegate", "undefined", "tab", "component", "componentWillLoad", "_this", "_asyncToGenerator", "setActive", "_this2", "prepareLazyLoaded", "changeActive", "isActive", "el", "e", "console", "error", "Promise", "resolve", "render", "key", "role", "class", "watchers", "style", "tabsCss", "IonTabsStyle0", "Tabs", "ionNavWillLoad", "ionTabsWillChange", "ionTabsDidChange", "transitioning", "onTabClicked", "ev", "href", "detail", "useRouter", "router", "document", "querySelector", "push", "select", "selectedTab", "_this3", "closest", "tabs", "length", "emit", "componentWillRender", "tabBar", "_this4", "getTab", "shouldSwitch", "notify<PERSON><PERSON><PERSON>", "tabSwitch", "_this5", "getSelected", "setRouteId", "id", "_this6", "changed", "element", "markVisible", "getRouteId", "_this7", "_a", "tabId", "reject", "leavingTab", "navChanged", "Array", "from", "querySelectorAll", "onIonTabButtonClick", "name", "tabEl", "find", "t", "ion_tab", "ion_tabs"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}