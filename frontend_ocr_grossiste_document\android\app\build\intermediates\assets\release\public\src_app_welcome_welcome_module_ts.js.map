{"version": 3, "file": "src_app_welcome_welcome_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEV;;;AAE7C,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,sDAAWA;CACvB,CACF;AAMK,MAAOI,wBAAwB;4BAAxBA,wBAAwB;;mBAAxBA,yBAAwB;AAAA;;QAAxBA;AAAwB;;YAHzBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,wBAAwB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFzBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEuB;AAEvB;;AAWvC,MAAOa,iBAAiB;qBAAjBA,iBAAiB;;mBAAjBA,kBAAiB;AAAA;;QAAjBA;AAAiB;;YAP1BH,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,6EAAwB;AAAA;;sHAIfQ,iBAAiB;IAAAC,YAAA,GAFbb,sDAAW;IAAAM,OAAA,GALxBG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,6EAAwB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;ACPtB,MAAOJ,WAAW;EAEtBc,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;EAAkB;EAE7CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAE7B;EAEAC,cAAcA,CAACC,QAAwC;IACrD;IACAC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEF,QAAQ,CAAC;IACzCH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;IAE3C;IACA,IAAI,CAACL,OAAO,CAACQ,YAAY,CAAC,QAAQ,CAAC;EACrC;;eAhBWvB,WAAW;;mBAAXA,YAAW,EAAAwB,+DAAA,CAAAjB,yDAAA;AAAA;;QAAXP,YAAW;EAAA2B,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCRxBT,uDAAA,iBAEa;MAKHA,4DAHV,kBAAc,aACiB,cACd,iBACkC;MACvCA,uDAAA,aAA0D;MAExDA,4DADF,aAA2B,SACrB;MAAAA,oDAAA,kDAAiC;MAAAA,0DAAA,EAAK;MAC1CA,4DAAA,QAAG;MAAAA,oDAAA,6EAAgE;MACrEA,0DADqE,EAAI,EACnE;MACNA,4DAAA,UAAI;MAAAA,oDAAA,mCAA2B;MAM3CA,0DAN2C,EAAK,EAC5B,EACF,EAGV,EACM;MAGJA,4DAFN,kBAAY,cACwB,qBAIc;MAA1CA,wDAAA,mBAAAgB,kDAAA;QAAA,OAASN,GAAA,CAAAf,cAAA,CAAe,eAAe,CAAC;MAAA,EAAC;MACzCK,uDAAA,cAAiG;MACnGA,0DAAA,EAAa;MACbA,4DAAA,qBAGyC;MAAvCA,wDAAA,mBAAAiB,kDAAA;QAAA,OAASP,GAAA,CAAAf,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACtCK,uDAAA,cAA2F;MAGnGA,0DAFM,EAAa,EACT,EACG", "sources": ["./src/app/welcome/welcome-routing.module.ts", "./src/app/welcome/welcome.module.ts", "./src/app/welcome/welcome.page.ts", "./src/app/welcome/welcome.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { WelcomePage } from './welcome.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: WelcomePage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class WelcomePageRoutingModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { WelcomePageRoutingModule } from './welcome-routing.module';\r\n\r\nimport { WelcomePage } from './welcome.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    WelcomePageRoutingModule\r\n  ],\r\n  declarations: [WelcomePage]\r\n})\r\nexport class WelcomePageModule {}\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n})\r\nexport class WelcomePage implements OnInit {\r\n\r\n  constructor(private navCtrl: NavController) {}\r\n\r\n  ngOnInit() {\r\n    console.log(\"Welcome Page\");\r\n\r\n  }\r\n\r\n  selectPlatform(platform: 'winpluspharma' | 'pharmalien') {\r\n    // Store the selected platform in localStorage\r\n    localStorage.setItem('src_app', platform);\r\n    console.log('Platform selected:', platform);\r\n\r\n    // Navigate to login page\r\n    this.navCtrl.navigateRoot('/login');\r\n  }\r\n}\r\n", "<ion-header>\r\n\r\n</ion-header>\r\n\r\n<ion-content >\r\n  <div class=\"welcome-wrapper\">\r\n        <ion-row>\r\n          <ion-col size=\"12\" class=\"slide-content\">\r\n            <img src=\"/assets/icon-welcome.svg\" class=\"slide-image\" />\r\n            <div class=\"content-slide\">\r\n              <h2><PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON><PERSON>, automatise<PERSON> !</h2>\r\n              <p>Vos bons de livraison enregistrés automatiquement en un instant.</p>\r\n            </div>\r\n            <h3>Choisissez votre plateforme</h3>\r\n          </ion-col>\r\n        </ion-row>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n    <ion-footer>\r\n        <div class=\"platform-selection\">\r\n          <ion-button\r\n            fill=\"clear\"\r\n            class=\"platform-button winplus-button\"\r\n            (click)=\"selectPlatform('winpluspharma')\">\r\n            <img src=\"/assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlusPharm\" class=\"platform-logo\" />\r\n          </ion-button>\r\n          <ion-button\r\n            fill=\"clear\"\r\n            class=\"platform-button pharmalien-button\"\r\n            (click)=\"selectPlatform('pharmalien')\">\r\n            <img src=\"/assets/onboarding_images/winpharm.svg\" alt=\"Pharmalien\" class=\"platform-logo\" />\r\n          </ion-button>\r\n        </div>\r\n    </ion-footer>"], "names": ["RouterModule", "WelcomePage", "routes", "path", "component", "WelcomePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "WelcomePageModule", "declarations", "constructor", "navCtrl", "ngOnInit", "console", "log", "selectPlatform", "platform", "localStorage", "setItem", "navigateRoot", "i0", "ɵɵdirectiveInject", "NavController", "selectors", "decls", "vars", "consts", "template", "WelcomePage_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "WelcomePage_Template_ion_button_click_15_listener", "WelcomePage_Template_ion_button_click_17_listener"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}