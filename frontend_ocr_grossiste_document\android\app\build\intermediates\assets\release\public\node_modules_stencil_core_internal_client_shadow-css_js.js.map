{"version": 3, "file": "node_modules_stencil_core_internal_client_shadow-css_js.js", "mappings": ";;;;;;;;;;;;;AAAA;AACA,IAAIA,6BAA6B,GAAIC,IAAI,IAAK;EAC5C,OAAOA,IAAI,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAIC,QAAQ,IAAK;EAC/B,MAAMC,YAAY,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC;EACbF,QAAQ,GAAGA,QAAQ,CAACF,OAAO,CAAC,eAAe,EAAE,CAACK,CAAC,EAAEC,IAAI,KAAK;IACxD,MAAMC,SAAS,GAAI,QAAOH,KAAM,IAAG;IACnCD,YAAY,CAACK,IAAI,CAACF,IAAI,CAAC;IACvBF,KAAK,EAAE;IACP,OAAOG,SAAS;EAClB,CAAC,CAAC;EACF,MAAME,OAAO,GAAGP,QAAQ,CAACF,OAAO,CAAC,2BAA2B,EAAE,CAACK,CAAC,EAAEK,MAAM,EAAEC,GAAG,KAAK;IAChF,MAAMJ,SAAS,GAAI,QAAOH,KAAM,IAAG;IACnCD,YAAY,CAACK,IAAI,CAACG,GAAG,CAAC;IACtBP,KAAK,EAAE;IACP,OAAOM,MAAM,GAAGH,SAAS;EAC3B,CAAC,CAAC;EACF,MAAMK,EAAE,GAAG;IACTH,OAAO;IACPN;EACF,CAAC;EACD,OAAOS,EAAE;AACX,CAAC;AACD,IAAIC,mBAAmB,GAAGA,CAACV,YAAY,EAAEM,OAAO,KAAK;EACnD,OAAOA,OAAO,CAACT,OAAO,CAAC,eAAe,EAAE,CAACK,CAAC,EAAED,KAAK,KAAKD,YAAY,CAAC,CAACC,KAAK,CAAC,CAAC;AAC7E,CAAC;AACD,IAAIU,aAAa,GAAG,gBAAgB;AACpC,IAAIC,gBAAgB,GAAG,mBAAmB;AAC1C,IAAIC,oBAAoB,GAAG,mBAAmB;AAC9C,IAAIC,YAAY,GAAG,iDAAiD;AACpE,IAAIC,eAAe,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGL,aAAa,GAAGG,YAAY,EAAE,KAAK,CAAC;AAC3E,IAAIG,sBAAsB,GAAG,IAAID,MAAM,CAAC,GAAG,GAAGH,oBAAoB,GAAGC,YAAY,EAAE,KAAK,CAAC;AACzF,IAAII,kBAAkB,GAAG,IAAIF,MAAM,CAAC,GAAG,GAAGJ,gBAAgB,GAAGE,YAAY,EAAE,KAAK,CAAC;AACjF,IAAIK,yBAAyB,GAAGR,aAAa,GAAG,gBAAgB;AAChE,IAAIS,2BAA2B,GAAG,sCAAsC;AACxE,IAAIC,qBAAqB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACvD,IAAIC,iBAAiB,GAAG,4BAA4B;AACpD,IAAIC,eAAe,GAAG,mBAAmB;AACzC,IAAIC,oBAAoB,GAAIzB,QAAQ,IAAK,IAAIiB,MAAM,CAAE,oCAAmCjB,QAAS,MAAK,EAAE,KAAK,CAAC;AAC9G,IAAI0B,eAAe,GAAGD,oBAAoB,CAAC,WAAW,CAAC;AACvD,IAAIE,YAAY,GAAGF,oBAAoB,CAAC,OAAO,CAAC;AAChD,IAAIG,mBAAmB,GAAGH,oBAAoB,CAAC,eAAe,CAAC;AAC/D,IAAII,UAAU,GAAG,sBAAsB;AACvC,IAAIC,aAAa,GAAIC,KAAK,IAAK;EAC7B,OAAOA,KAAK,CAACjC,OAAO,CAAC+B,UAAU,EAAE,EAAE,CAAC;AACtC,CAAC;AACD,IAAIG,kBAAkB,GAAG,8CAA8C;AACvE,IAAIC,uBAAuB,GAAIF,KAAK,IAAK;EACvC,OAAOA,KAAK,CAACG,KAAK,CAACF,kBAAkB,CAAC,IAAI,EAAE;AAC9C,CAAC;AACD,IAAIG,OAAO,GAAG,uDAAuD;AACrE,IAAIC,QAAQ,GAAG,SAAS;AACxB,IAAIC,gBAAgB,GAAG,2BAA2B;AAClD,IAAIC,UAAU,GAAG,GAAG;AACpB,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,iBAAiB,GAAG,SAAS;AACjC,IAAIC,YAAY,GAAGA,CAACV,KAAK,EAAEW,YAAY,KAAK;EAC1C,MAAMC,sBAAsB,GAAGC,YAAY,CAACb,KAAK,CAAC;EAClD,IAAIc,cAAc,GAAG,CAAC;EACtB,OAAOF,sBAAsB,CAACG,aAAa,CAAChD,OAAO,CAACqC,OAAO,EAAE,CAAC,GAAGY,CAAC,KAAK;IACrE,MAAM/C,QAAQ,GAAG+C,CAAC,CAAC,CAAC,CAAC;IACrB,IAAIxC,OAAO,GAAG,EAAE;IAChB,IAAIyC,MAAM,GAAGD,CAAC,CAAC,CAAC,CAAC;IACjB,IAAIE,aAAa,GAAG,EAAE;IACtB,IAAID,MAAM,IAAIA,MAAM,CAACE,UAAU,CAAC,GAAG,GAAGV,iBAAiB,CAAC,EAAE;MACxDjC,OAAO,GAAGoC,sBAAsB,CAACQ,MAAM,CAACN,cAAc,EAAE,CAAC;MACzDG,MAAM,GAAGA,MAAM,CAACI,SAAS,CAACZ,iBAAiB,CAACa,MAAM,GAAG,CAAC,CAAC;MACvDJ,aAAa,GAAG,GAAG;IACrB;IACA,MAAMK,OAAO,GAAG;MACdtD,QAAQ;MACRO;IACF,CAAC;IACD,MAAMgD,IAAI,GAAGb,YAAY,CAACY,OAAO,CAAC;IAClC,OAAQ,GAAEP,CAAC,CAAC,CAAC,CAAE,GAAEQ,IAAI,CAACvD,QAAS,GAAE+C,CAAC,CAAC,CAAC,CAAE,GAAEE,aAAc,GAAEM,IAAI,CAAChD,OAAQ,GAAEyC,MAAO,EAAC;EACjF,CAAC,CAAC;AACJ,CAAC;AACD,IAAIJ,YAAY,GAAIb,KAAK,IAAK;EAC5B,MAAMyB,UAAU,GAAGzB,KAAK,CAAC0B,KAAK,CAACrB,QAAQ,CAAC;EACxC,MAAMsB,WAAW,GAAG,EAAE;EACtB,MAAMC,aAAa,GAAG,EAAE;EACxB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGN,UAAU,CAACH,MAAM,EAAES,SAAS,EAAE,EAAE;IAClE,MAAMC,IAAI,GAAGP,UAAU,CAACM,SAAS,CAAC;IAClC,IAAIC,IAAI,KAAKxB,WAAW,EAAE;MACxBqB,YAAY,EAAE;IAChB;IACA,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpBC,iBAAiB,CAACvD,IAAI,CAACyD,IAAI,CAAC;IAC9B,CAAC,MAAM;MACL,IAAIF,iBAAiB,CAACR,MAAM,GAAG,CAAC,EAAE;QAChCM,aAAa,CAACrD,IAAI,CAACuD,iBAAiB,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9CN,WAAW,CAACpD,IAAI,CAACkC,iBAAiB,CAAC;QACnCqB,iBAAiB,GAAG,EAAE;MACxB;MACAH,WAAW,CAACpD,IAAI,CAACyD,IAAI,CAAC;IACxB;IACA,IAAIA,IAAI,KAAKzB,UAAU,EAAE;MACvBsB,YAAY,EAAE;IAChB;EACF;EACA,IAAIC,iBAAiB,CAACR,MAAM,GAAG,CAAC,EAAE;IAChCM,aAAa,CAACrD,IAAI,CAACuD,iBAAiB,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9CN,WAAW,CAACpD,IAAI,CAACkC,iBAAiB,CAAC;EACrC;EACA,MAAMyB,gBAAgB,GAAG;IACvBnB,aAAa,EAAEY,WAAW,CAACM,IAAI,CAAC,EAAE,CAAC;IACnCb,MAAM,EAAEQ;EACV,CAAC;EACD,OAAOM,gBAAgB;AACzB,CAAC;AACD,IAAIC,2BAA2B,GAAIC,OAAO,IAAK;EAC7CA,OAAO,GAAGA,OAAO,CAACrE,OAAO,CAAC8B,mBAAmB,EAAG,KAAId,oBAAqB,EAAC,CAAC,CAAChB,OAAO,CAAC6B,YAAY,EAAG,KAAIf,aAAc,EAAC,CAAC,CAACd,OAAO,CAAC4B,eAAe,EAAG,KAAIb,gBAAiB,EAAC,CAAC;EACzK,OAAOsD,OAAO;AAChB,CAAC;AACD,IAAIC,gBAAgB,GAAGA,CAACD,OAAO,EAAEE,MAAM,EAAEC,YAAY,KAAK;EACxD,OAAOH,OAAO,CAACrE,OAAO,CAACuE,MAAM,EAAE,CAAC,GAAGtB,CAAC,KAAK;IACvC,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,MAAMwB,KAAK,GAAGxB,CAAC,CAAC,CAAC,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC;MAC7B,MAAMe,CAAC,GAAG,EAAE;MACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAAClB,MAAM,EAAEoB,CAAC,EAAE,EAAE;QACrC,MAAMC,CAAC,GAAGH,KAAK,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;QACzB,IAAI,CAACD,CAAC,EAAE;QACRF,CAAC,CAAClE,IAAI,CAACgE,YAAY,CAAClD,yBAAyB,EAAEsD,CAAC,EAAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D;MACA,OAAOyB,CAAC,CAACR,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC,MAAM;MACL,OAAO5C,yBAAyB,GAAG2B,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,IAAI6B,qBAAqB,GAAGA,CAACC,IAAI,EAAEd,IAAI,EAAEf,MAAM,KAAK;EAClD,OAAO6B,IAAI,GAAGd,IAAI,CAACjE,OAAO,CAACc,aAAa,EAAE,EAAE,CAAC,GAAGoC,MAAM;AACxD,CAAC;AACD,IAAI8B,gBAAgB,GAAIX,OAAO,IAAK;EAClC,OAAOC,gBAAgB,CAACD,OAAO,EAAEnD,eAAe,EAAE4D,qBAAqB,CAAC;AAC1E,CAAC;AACD,IAAIG,4BAA4B,GAAGA,CAACF,IAAI,EAAEd,IAAI,EAAEf,MAAM,KAAK;EACzD,IAAIe,IAAI,CAACiB,OAAO,CAACpE,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;IACpC,OAAOgE,qBAAqB,CAACC,IAAI,EAAEd,IAAI,EAAEf,MAAM,CAAC;EAClD,CAAC,MAAM;IACL,OAAO6B,IAAI,GAAGd,IAAI,GAAGf,MAAM,GAAG,IAAI,GAAGe,IAAI,GAAG,GAAG,GAAGc,IAAI,GAAG7B,MAAM;EACjE;AACF,CAAC;AACD,IAAIiC,mBAAmB,GAAGA,CAACd,OAAO,EAAEe,WAAW,KAAK;EAClD,MAAMC,SAAS,GAAG,GAAG,GAAGD,WAAW,GAAG,KAAK;EAC3C,MAAME,SAAS,GAAG,EAAE;EACpBjB,OAAO,GAAGA,OAAO,CAACrE,OAAO,CAACqB,kBAAkB,EAAE,CAAC,GAAG4B,CAAC,KAAK;IACtD,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,MAAMsC,QAAQ,GAAGtC,CAAC,CAAC,CAAC,CAAC,CAAC4B,IAAI,CAAC,CAAC;MAC5B,MAAM3B,MAAM,GAAGD,CAAC,CAAC,CAAC,CAAC;MACnB,MAAMuC,eAAe,GAAGH,SAAS,GAAGE,QAAQ,GAAGrC,MAAM;MACrD,IAAIuC,cAAc,GAAG,EAAE;MACvB,KAAK,IAAId,CAAC,GAAG1B,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE0B,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAClC,MAAMe,IAAI,GAAGzC,CAAC,CAAC,CAAC,CAAC,CAAC0B,CAAC,CAAC;QACpB,IAAIe,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;UAChC;QACF;QACAD,cAAc,GAAGC,IAAI,GAAGD,cAAc;MACxC;MACA,MAAME,WAAW,GAAG,CAACF,cAAc,GAAGD,eAAe,EAAEX,IAAI,CAAC,CAAC;MAC7D,MAAMe,aAAa,GAAI,GAAEH,cAAc,CAACI,OAAO,CAAC,CAAE,GAAEL,eAAe,CAACX,IAAI,CAAC,CAAE,EAAC,CAACA,IAAI,CAAC,CAAC;MACnF,IAAIc,WAAW,KAAKC,aAAa,EAAE;QACjC,MAAME,eAAe,GAAI,GAAEF,aAAc,KAAID,WAAY,EAAC;QAC1DL,SAAS,CAAC9E,IAAI,CAAC;UACbmF,WAAW;UACXG;QACF,CAAC,CAAC;MACJ;MACA,OAAON,eAAe;IACxB,CAAC,MAAM;MACL,OAAOlE,yBAAyB,GAAG2B,CAAC,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,CAAC;EACF,OAAO;IACLqC,SAAS;IACTjB;EACF,CAAC;AACH,CAAC;AACD,IAAI0B,uBAAuB,GAAI1B,OAAO,IAAK;EACzC,OAAOC,gBAAgB,CAACD,OAAO,EAAEjD,sBAAsB,EAAE6D,4BAA4B,CAAC;AACxF,CAAC;AACD,IAAIe,yBAAyB,GAAI3B,OAAO,IAAK;EAC3C,OAAO7C,qBAAqB,CAACyE,MAAM,CAAC,CAACC,MAAM,EAAEC,OAAO,KAAKD,MAAM,CAAClG,OAAO,CAACmG,OAAO,EAAE,GAAG,CAAC,EAAE9B,OAAO,CAAC;AACjG,CAAC;AACD,IAAI+B,gBAAgB,GAAIC,cAAc,IAAK;EACzC,MAAMC,GAAG,GAAG,KAAK;EACjB,MAAMC,GAAG,GAAG,KAAK;EACjBF,cAAc,GAAGA,cAAc,CAACrG,OAAO,CAACsG,GAAG,EAAE,KAAK,CAAC,CAACtG,OAAO,CAACuG,GAAG,EAAE,KAAK,CAAC;EACvE,OAAO,IAAIpF,MAAM,CAAC,IAAI,GAAGkF,cAAc,GAAG,GAAG,GAAG5E,iBAAiB,EAAE,GAAG,CAAC;AACzE,CAAC;AACD,IAAI+E,oBAAoB,GAAGA,CAACtG,QAAQ,EAAEmG,cAAc,KAAK;EACvD,MAAMI,EAAE,GAAGL,gBAAgB,CAACC,cAAc,CAAC;EAC3C,OAAO,CAACI,EAAE,CAACC,IAAI,CAACxG,QAAQ,CAAC;AAC3B,CAAC;AACD,IAAIyG,qBAAqB,GAAGA,CAACzG,QAAQ,EAAE0G,eAAe,KAAK;EACzD,OAAO1G,QAAQ,CAACF,OAAO,CAACuC,gBAAgB,EAAE,CAAClC,CAAC,EAAEwG,MAAM,GAAG,EAAE,EAAEC,WAAW,EAAEC,KAAK,GAAG,EAAE,EAAEC,KAAK,GAAG,EAAE,KAAK;IACjG,OAAOH,MAAM,GAAGD,eAAe,GAAGG,KAAK,GAAGC,KAAK;EACjD,CAAC,CAAC;AACJ,CAAC;AACD,IAAIC,wBAAwB,GAAGA,CAAC/G,QAAQ,EAAEmG,cAAc,EAAEa,YAAY,KAAK;EACzExF,eAAe,CAACyF,SAAS,GAAG,CAAC;EAC7B,IAAIzF,eAAe,CAACgF,IAAI,CAACxG,QAAQ,CAAC,EAAE;IAClC,MAAMK,SAAS,GAAI,IAAG2G,YAAa,EAAC;IACpC,OAAOhH,QAAQ,CAACF,OAAO,CAACuB,2BAA2B,EAAE,CAAClB,CAAC,EAAE+G,SAAS,KAAKT,qBAAqB,CAACS,SAAS,EAAE7G,SAAS,CAAC,CAAC,CAACP,OAAO,CAAC0B,eAAe,EAAEnB,SAAS,GAAG,GAAG,CAAC;EAC/J;EACA,OAAO8F,cAAc,GAAG,GAAG,GAAGnG,QAAQ;AACxC,CAAC;AACD,IAAImH,wBAAwB,GAAGA,CAACnH,QAAQ,EAAEmG,cAAc,EAAEa,YAAY,KAAK;EACzE,MAAMI,IAAI,GAAG,kBAAkB;EAC/BjB,cAAc,GAAGA,cAAc,CAACrG,OAAO,CAACsH,IAAI,EAAE,CAACjH,CAAC,EAAE,GAAGoE,KAAK,KAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM8C,SAAS,GAAG,GAAG,GAAGlB,cAAc;EACtC,MAAMmB,kBAAkB,GAAI5C,CAAC,IAAK;IAChC,IAAI6C,OAAO,GAAG7C,CAAC,CAACC,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC4C,OAAO,EAAE;MACZ,OAAO,EAAE;IACX;IACA,IAAI7C,CAAC,CAACM,OAAO,CAAC5D,yBAAyB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7CmG,OAAO,GAAGR,wBAAwB,CAACrC,CAAC,EAAEyB,cAAc,EAAEa,YAAY,CAAC;IACrE,CAAC,MAAM;MACL,MAAMQ,CAAC,GAAG9C,CAAC,CAAC5E,OAAO,CAAC0B,eAAe,EAAE,EAAE,CAAC;MACxC,IAAIgG,CAAC,CAACnE,MAAM,GAAG,CAAC,EAAE;QAChBkE,OAAO,GAAGd,qBAAqB,CAACe,CAAC,EAAEH,SAAS,CAAC;MAC/C;IACF;IACA,OAAOE,OAAO;EAChB,CAAC;EACD,MAAME,WAAW,GAAG1H,YAAY,CAACC,QAAQ,CAAC;EAC1CA,QAAQ,GAAGyH,WAAW,CAAClH,OAAO;EAC9B,IAAImH,cAAc,GAAG,EAAE;EACvB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,GAAG;EACP,MAAMC,GAAG,GAAG,qBAAqB;EACjC,MAAMC,OAAO,GAAG9H,QAAQ,CAACgF,OAAO,CAAC5D,yBAAyB,CAAC,GAAG,CAAC,CAAC;EAChE,IAAI2G,WAAW,GAAG,CAACD,OAAO;EAC1B,OAAO,CAACF,GAAG,GAAGC,GAAG,CAACG,IAAI,CAAChI,QAAQ,CAAC,MAAM,IAAI,EAAE;IAC1C,MAAMiI,SAAS,GAAGL,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMM,KAAK,GAAGlI,QAAQ,CAACmI,KAAK,CAACR,UAAU,EAAEC,GAAG,CAAC1H,KAAK,CAAC,CAACyE,IAAI,CAAC,CAAC;IAC1DoD,WAAW,GAAGA,WAAW,IAAIG,KAAK,CAAClD,OAAO,CAAC5D,yBAAyB,CAAC,GAAG,CAAC,CAAC;IAC1E,MAAMgH,UAAU,GAAGL,WAAW,GAAGT,kBAAkB,CAACY,KAAK,CAAC,GAAGA,KAAK;IAClER,cAAc,IAAK,GAAEU,UAAW,IAAGH,SAAU,GAAE;IAC/CN,UAAU,GAAGE,GAAG,CAACZ,SAAS;EAC5B;EACA,MAAMlD,IAAI,GAAG/D,QAAQ,CAACoD,SAAS,CAACuE,UAAU,CAAC;EAC3CI,WAAW,GAAGA,WAAW,IAAIhE,IAAI,CAACiB,OAAO,CAAC5D,yBAAyB,CAAC,GAAG,CAAC,CAAC;EACzEsG,cAAc,IAAIK,WAAW,GAAGT,kBAAkB,CAACvD,IAAI,CAAC,GAAGA,IAAI;EAC/D,OAAOpD,mBAAmB,CAAC8G,WAAW,CAACxH,YAAY,EAAEyH,cAAc,CAAC;AACtE,CAAC;AACD,IAAIW,aAAa,GAAGA,CAACrI,QAAQ,EAAEsI,iBAAiB,EAAEtB,YAAY,EAAEuB,YAAY,KAAK;EAC/E,OAAOvI,QAAQ,CAACyD,KAAK,CAAC,GAAG,CAAC,CAAC+E,GAAG,CAAEC,WAAW,IAAK;IAC9C,IAAIF,YAAY,IAAIE,WAAW,CAACzD,OAAO,CAAC,GAAG,GAAGuD,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;MAChE,OAAOE,WAAW,CAAC9D,IAAI,CAAC,CAAC;IAC3B;IACA,IAAI2B,oBAAoB,CAACmC,WAAW,EAAEH,iBAAiB,CAAC,EAAE;MACxD,OAAOnB,wBAAwB,CAACsB,WAAW,EAAEH,iBAAiB,EAAEtB,YAAY,CAAC,CAACrC,IAAI,CAAC,CAAC;IACtF,CAAC,MAAM;MACL,OAAO8D,WAAW,CAAC9D,IAAI,CAAC,CAAC;IAC3B;EACF,CAAC,CAAC,CAACX,IAAI,CAAC,IAAI,CAAC;AACf,CAAC;AACD,IAAI0E,cAAc,GAAGA,CAACvE,OAAO,EAAEmE,iBAAiB,EAAEtB,YAAY,EAAEuB,YAAY,KAAK;EAC/E,OAAO9F,YAAY,CAAC0B,OAAO,EAAGZ,IAAI,IAAK;IACrC,IAAIvD,QAAQ,GAAGuD,IAAI,CAACvD,QAAQ;IAC5B,IAAIO,OAAO,GAAGgD,IAAI,CAAChD,OAAO;IAC1B,IAAIgD,IAAI,CAACvD,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5BA,QAAQ,GAAGqI,aAAa,CAAC9E,IAAI,CAACvD,QAAQ,EAAEsI,iBAAiB,EAAEtB,YAAY,EAAEuB,YAAY,CAAC;IACxF,CAAC,MAAM,IAAIhF,IAAI,CAACvD,QAAQ,CAACkD,UAAU,CAAC,QAAQ,CAAC,IAAIK,IAAI,CAACvD,QAAQ,CAACkD,UAAU,CAAC,WAAW,CAAC,IAAIK,IAAI,CAACvD,QAAQ,CAACkD,UAAU,CAAC,OAAO,CAAC,IAAIK,IAAI,CAACvD,QAAQ,CAACkD,UAAU,CAAC,WAAW,CAAC,EAAE;MACpK3C,OAAO,GAAGmI,cAAc,CAACnF,IAAI,CAAChD,OAAO,EAAE+H,iBAAiB,EAAEtB,YAAY,EAAEuB,YAAY,CAAC;IACvF;IACA,MAAMjF,OAAO,GAAG;MACdtD,QAAQ,EAAEA,QAAQ,CAACF,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC6E,IAAI,CAAC,CAAC;MACjDpE;IACF,CAAC;IACD,OAAO+C,OAAO;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,IAAIqF,YAAY,GAAGA,CAACxE,OAAO,EAAEyE,OAAO,EAAEC,WAAW,EAAE3D,WAAW,KAAK;EACjEf,OAAO,GAAGD,2BAA2B,CAACC,OAAO,CAAC;EAC9CA,OAAO,GAAGW,gBAAgB,CAACX,OAAO,CAAC;EACnCA,OAAO,GAAG0B,uBAAuB,CAAC1B,OAAO,CAAC;EAC1C,MAAM2E,OAAO,GAAG7D,mBAAmB,CAACd,OAAO,EAAEe,WAAW,CAAC;EACzDf,OAAO,GAAG2E,OAAO,CAAC3E,OAAO;EACzBA,OAAO,GAAG2B,yBAAyB,CAAC3B,OAAO,CAAC;EAC5C,IAAIyE,OAAO,EAAE;IACXzE,OAAO,GAAGuE,cAAc,CAACvE,OAAO,EAAEyE,OAAO,EAAEC,WAAW,EAAE3D,WAAW,CAAC;EACtE;EACAf,OAAO,GAAG4E,oBAAoB,CAAC5E,OAAO,EAAE0E,WAAW,CAAC;EACpD1E,OAAO,GAAGA,OAAO,CAACrE,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC;EACzD,OAAO;IACLqE,OAAO,EAAEA,OAAO,CAACQ,IAAI,CAAC,CAAC;IACvB;IACA;IACAqE,gBAAgB,EAAEF,OAAO,CAAC1D,SAAS,CAACoD,GAAG,CAAES,GAAG,KAAM;MAChDxD,WAAW,EAAEsD,oBAAoB,CAACE,GAAG,CAACxD,WAAW,EAAEoD,WAAW,CAAC;MAC/DjD,eAAe,EAAEmD,oBAAoB,CAACE,GAAG,CAACrD,eAAe,EAAEiD,WAAW;IACxE,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAIE,oBAAoB,GAAGA,CAAC5E,OAAO,EAAE0E,WAAW,KAAK;EACnD,OAAO1E,OAAO,CAACrE,OAAO,CAAC,+BAA+B,EAAG,IAAG+I,WAAY,EAAC,CAAC;AAC5E,CAAC;AACD,IAAIK,QAAQ,GAAGA,CAAC/E,OAAO,EAAEyE,OAAO,KAAK;EACnC,MAAMC,WAAW,GAAGD,OAAO,GAAG,IAAI;EAClC,MAAM1D,WAAW,GAAG0D,OAAO,GAAG,IAAI;EAClC,MAAMO,gBAAgB,GAAGlH,uBAAuB,CAACkC,OAAO,CAAC;EACzDA,OAAO,GAAGrC,aAAa,CAACqC,OAAO,CAAC;EAChC,MAAMiF,MAAM,GAAGT,YAAY,CAACxE,OAAO,EAAEyE,OAAO,EAAEC,WAAW,EAAE3D,WAAW,CAAC;EACvEf,OAAO,GAAG,CAACiF,MAAM,CAACjF,OAAO,EAAE,GAAGgF,gBAAgB,CAAC,CAACnF,IAAI,CAAC,IAAI,CAAC;EAC1DoF,MAAM,CAACJ,gBAAgB,CAACK,OAAO,CAAE/D,eAAe,IAAK;IACnD,MAAMgE,KAAK,GAAG,IAAIrI,MAAM,CAACrB,6BAA6B,CAAC0F,eAAe,CAACG,WAAW,CAAC,EAAE,GAAG,CAAC;IACzFtB,OAAO,GAAGA,OAAO,CAACrE,OAAO,CAACwJ,KAAK,EAAEhE,eAAe,CAACM,eAAe,CAAC;EACnE,CAAC,CAAC;EACF,OAAOzB,OAAO;AAChB,CAAC", "sources": ["./node_modules/@stencil/core/internal/client/shadow-css.js"], "sourcesContent": ["// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = (text) => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = (selector) => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = (selector) => new RegExp(`((?<!(^@supports(.*)))|(?<={.*))(${selector}\\\\b)`, \"gim\");\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = (input) => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = (input) => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = (input) => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = (cssText) => {\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i = 0; i < parts.length; i++) {\n        const p = parts[i].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i = m[4] - 1; i >= 0; i--) {\n        const char = m[5][i];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = (cssText) => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = (cssText) => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = (scopeSelector2) => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = (p) => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map((shallowPart) => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector) => {\n  return processRules(cssText, (rule) => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId, hostScopeId, slotScopeId) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId) {\n    cssText = scopeSelectors(cssText, scopeId, hostScopeId, slotScopeId);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map((ref) => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar scopeCss = (cssText, scopeId) => {\n  const hostScopeId = scopeId + \"-h\";\n  const slotScopeId = scopeId + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const scoped = scopeCssText(cssText, scopeId, hostScopeId, slotScopeId);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  scoped.slottedSelectors.forEach((slottedSelector) => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  return cssText;\n};\nexport {\n  scopeCss\n};\n"], "names": ["escapeRegExpSpecialCharacters", "text", "replace", "safeSelector", "selector", "placeholders", "index", "_", "keep", "<PERSON><PERSON><PERSON>", "push", "content", "pseudo", "exp", "ss", "restoreSafeSelector", "_polyfillHost", "_polyfillSlotted", "_polyfillHostContext", "_parenSuffix", "_cssColonHostRe", "RegExp", "_cssColonHostContextRe", "_cssColonSlottedRe", "_polyfillHostNoCombinator", "_polyfillHostNoCombinatorRe", "_shadowDOMSelectorsRe", "_selectorReSuffix", "_polyfillHostRe", "createSupportsRuleRe", "_colonSlottedRe", "_colonHostRe", "_colonHostContextRe", "_commentRe", "stripComments", "input", "_commentWithHashRe", "extractCommentsWithHash", "match", "_ruleRe", "_curlyRe", "_selectorPartsRe", "OPEN_CURLY", "CLOSE_CURLY", "BLOCK_PLACEHOLDER", "processRules", "ruleCallback", "inputWithEscapedBlocks", "escapeBlocks", "nextBlockIndex", "escapedString", "m", "suffix", "contentPrefix", "startsWith", "blocks", "substring", "length", "cssRule", "rule", "inputParts", "split", "resultParts", "escapedBlocks", "bracketCount", "currentBlockParts", "partIndex", "part", "join", "strEscapedBlocks", "insertPolyfillHostInCssText", "cssText", "convertColonRule", "regExp", "partReplacer", "parts", "r", "i", "p", "trim", "colonHostPartReplacer", "host", "convertColonHost", "colonHostContextPartReplacer", "indexOf", "convertColonSlotted", "slotScopeId", "slotClass", "selectors", "compound", "slottedSelector", "prefixSelector", "char", "orgSelector", "addedSelector", "trimEnd", "updatedSelector", "convertColonHostContext", "convertShadowDOMSelectors", "reduce", "result", "pattern", "makeScopeMatcher", "scopeSelector2", "lre", "rre", "selectorNeedsScoping", "re", "test", "injectScopingSelector", "scopingSelector", "before", "_colonGroup", "colon", "after", "applySimpleSelectorScope", "hostSelector", "lastIndex", "selector2", "applyStrictSelectorScope", "isRe", "className", "_scopeSelectorPart", "scopedP", "t", "safeContent", "scopedSelector", "startIndex", "res", "sep", "hasHost", "shouldScope", "exec", "separator", "part2", "slice", "scopedPart", "scopeSelector", "scopeSelectorText", "slotSelector", "map", "shallowPart", "scopeSelectors", "scopeCssText", "scopeId", "hostScopeId", "slotted", "replaceShadowCssHost", "slottedSelectors", "ref", "scopeCss", "commentsWithHash", "scoped", "for<PERSON>ach", "regex"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}