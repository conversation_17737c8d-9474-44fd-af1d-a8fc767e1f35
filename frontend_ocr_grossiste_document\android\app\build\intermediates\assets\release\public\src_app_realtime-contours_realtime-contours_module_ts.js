"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_realtime-contours_realtime-contours_module_ts"],{

/***/ 71990:
/*!***********************************************************************!*\
  !*** ./src/app/realtime-contours/realtime-contours-routing.module.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RealtimeContoursPageRoutingModule: () => (/* binding */ RealtimeContoursPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _realtime_contours_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./realtime-contours.page */ 47944);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _RealtimeContoursPageRoutingModule;




const routes = [{
  path: '',
  component: _realtime_contours_page__WEBPACK_IMPORTED_MODULE_0__.RealtimeContoursPage
}];
class RealtimeContoursPageRoutingModule {}
_RealtimeContoursPageRoutingModule = RealtimeContoursPageRoutingModule;
_RealtimeContoursPageRoutingModule.ɵfac = function RealtimeContoursPageRoutingModule_Factory(t) {
  return new (t || _RealtimeContoursPageRoutingModule)();
};
_RealtimeContoursPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _RealtimeContoursPageRoutingModule
});
_RealtimeContoursPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](RealtimeContoursPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 21343:
/*!***************************************************************!*\
  !*** ./src/app/realtime-contours/realtime-contours.module.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RealtimeContoursPageModule: () => (/* binding */ RealtimeContoursPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _realtime_contours_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./realtime-contours-routing.module */ 71990);
/* harmony import */ var _realtime_contours_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./realtime-contours.page */ 47944);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/shared.module */ 93887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 37580);
var _RealtimeContoursPageModule;





 // Import SharedModule

class RealtimeContoursPageModule {}
_RealtimeContoursPageModule = RealtimeContoursPageModule;
_RealtimeContoursPageModule.ɵfac = function RealtimeContoursPageModule_Factory(t) {
  return new (t || _RealtimeContoursPageModule)();
};
_RealtimeContoursPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
  type: _RealtimeContoursPageModule
});
_RealtimeContoursPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _realtime_contours_routing_module__WEBPACK_IMPORTED_MODULE_0__.RealtimeContoursPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](RealtimeContoursPageModule, {
    declarations: [_realtime_contours_page__WEBPACK_IMPORTED_MODULE_1__.RealtimeContoursPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_5__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonicModule, _realtime_contours_routing_module__WEBPACK_IMPORTED_MODULE_0__.RealtimeContoursPageRoutingModule, _shared_shared_module__WEBPACK_IMPORTED_MODULE_2__.SharedModule]
  });
})();

/***/ }),

/***/ 47944:
/*!*************************************************************!*\
  !*** ./src/app/realtime-contours/realtime-contours.page.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RealtimeContoursPage: () => (/* binding */ RealtimeContoursPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _services_open_cv_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/open-cv.service */ 78208);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 60316);

var _RealtimeContoursPage;




const _c0 = ["video"];
const _c1 = ["canvas"];
function RealtimeContoursPage_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "ion-spinner");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Loading OpenCV...");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function RealtimeContoursPage_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "video", 6, 0)(3, "canvas", 7, 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("width", ctx_r0.width)("height", ctx_r0.height);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("width", ctx_r0.width)("height", ctx_r0.height);
  }
}
class RealtimeContoursPage {
  constructor(platform, openCVService, changeDetector) {
    this.platform = platform;
    this.openCVService = openCVService;
    this.changeDetector = changeDetector;
    this.streaming = false;
    this.width = 640;
    this.height = 480;
    this.isOpenCVReady = false;
  }
  ngOnInit() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        _this.cv = yield _this.openCVService.getOpenCV();
        _this.isOpenCVReady = true;
        _this.changeDetector.detectChanges();
        yield _this.startCamera();
      } catch (error) {
        console.error('Error initializing OpenCV:', error);
      }
    })();
  }
  // Update your template to fix the accessibility warning:
  startCamera() {
    var _this2 = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!_this2.isOpenCVReady) {
        console.warn('OpenCV is not ready yet');
        return;
      }
      try {
        // Get actual device dimensions
        const constraints = {
          video: {
            facingMode: 'environment',
            width: {
              ideal: _this2.width
            },
            height: {
              ideal: _this2.height
            }
          },
          audio: false
        };
        const stream = yield navigator.mediaDevices.getUserMedia(constraints);
        if (_this2.video && _this2.video.nativeElement) {
          const videoElement = _this2.video.nativeElement;
          videoElement.srcObject = stream;
          // Wait for video metadata to load
          videoElement.onloadedmetadata = () => {
            // Update canvas dimensions to match video
            const track = stream.getVideoTracks()[0];
            const settings = track.getSettings();
            _this2.width = settings.width || _this2.width;
            _this2.height = settings.height || _this2.height;
            if (_this2.canvas) {
              _this2.canvas.nativeElement.width = _this2.width;
              _this2.canvas.nativeElement.height = _this2.height;
            }
            videoElement.play();
            _this2.streaming = true;
            _this2.processVideo();
          };
        }
      } catch (err) {
        console.error('Error accessing camera:', err);
      }
    })();
  }
  processVideo() {
    try {
      if (!this.streaming) return;
      const video = this.video.nativeElement;
      const canvas = this.canvas.nativeElement;
      const context = canvas.getContext('2d');
      // Draw video frame to canvas
      context.drawImage(video, 0, 0, this.width, this.height);
      // Get image data from canvas
      let src = this.cv.imread(canvas);
      let dst = src.clone();
      // Blur the image to enhance edge detection
      let blurred = new this.cv.Mat();
      this.cv.medianBlur(src, blurred, 9);
      let squares = [];
      // Find squares in every color plane of the image
      for (let c = 0; c < 3; c++) {
        let gray0 = new this.cv.Mat();
        let gray = new this.cv.Mat();
        // Extract the c-th color plane
        let ch = [c, 0];
        this.cv.extractChannel(blurred, gray0, c);
        // Try several threshold levels
        for (let l = 0; l < 2; l++) {
          if (l === 0) {
            // Use Canny
            this.cv.Canny(gray0, gray, 10, 20, 3);
            // Dilate to remove potential holes between edge segments
            let kernel = this.cv.getStructuringElement(this.cv.MORPH_RECT, new this.cv.Size(3, 3));
            this.cv.dilate(gray, gray, kernel, new this.cv.Point(-1, -1));
            kernel.delete();
          } else {
            // Use simple thresholding
            this.cv.threshold(gray0, gray, (l + 1) * 255 / 2, 255, this.cv.THRESH_BINARY);
          }
          // Find contours
          let contours = new this.cv.MatVector();
          let hierarchy = new this.cv.Mat();
          this.cv.findContours(gray, contours, hierarchy, this.cv.RETR_LIST, this.cv.CHAIN_APPROX_SIMPLE);
          // Test each contour
          for (let i = 0; i < contours.size(); i++) {
            let contour = contours.get(i);
            let approx = new this.cv.Mat();
            let perimeter = this.cv.arcLength(contour, true);
            // Approximate contour with accuracy proportional to the contour perimeter
            this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);
            // Square contours should have 4 vertices after approximation
            // Relatively large area (to filter out noisy contours)
            // And be convex
            let area = Math.abs(this.cv.contourArea(approx));
            if (approx.rows === 4 && area > 1000 && area < this.width * this.height * 0.9 &&
            // Add maximum area constraint
            this.cv.isContourConvex(approx)) {
              // Check if angles are approximately 90 degrees
              let maxCosine = 0;
              let points = [];
              // Get points from approx
              for (let j = 0; j < 4; j++) {
                points.push({
                  x: approx.data32S[j * 2],
                  y: approx.data32S[j * 2 + 1]
                });
              }
              // Check angles
              for (let j = 2; j < 5; j++) {
                let cosine = Math.abs(this.angle(points[j % 4], points[j - 2], points[j - 1]));
                maxCosine = Math.max(maxCosine, cosine);
              }
              // If all angles are approximately 90 degrees (cos < 0.3)
              if (maxCosine < 0.3) {
                // Draw the square
                this.cv.drawContours(dst, new this.cv.MatVector([approx]), -1, new this.cv.Scalar(0, 255, 0, 255), 3);
                // Draw corners
                points.forEach(point => {
                  this.cv.circle(dst, new this.cv.Point(point.x, point.y), 10, new this.cv.Scalar(255, 0, 0, 255), -1);
                });
              }
            }
            approx.delete();
          }
          contours.delete();
          hierarchy.delete();
        }
        gray0.delete();
        gray.delete();
      }
      // Show result
      this.cv.imshow(canvas, dst);
      // Cleanup
      src.delete();
      dst.delete();
      blurred.delete();
      // Continue processing
      requestAnimationFrame(() => this.processVideo());
    } catch (err) {
      console.error('Error in processVideo:', err);
    }
  }
  // Helper function to calculate angle
  angle(pt1, pt2, pt0) {
    const dx1 = pt1.x - pt0.x;
    const dy1 = pt1.y - pt0.y;
    const dx2 = pt2.x - pt0.x;
    const dy2 = pt2.y - pt0.y;
    return (dx1 * dx2 + dy1 * dy2) / Math.sqrt((dx1 * dx1 + dy1 * dy1) * (dx2 * dx2 + dy2 * dy2) + 1e-10);
  }
  // processVideo() {
  //   try {
  //     const video = this.video!.nativeElement;
  //     const canvas = this.canvas!.nativeElement;
  //     const context = canvas.getContext('2d');
  //     if (this.streaming) {
  //       // Draw video frame to canvas
  //       context.drawImage(video, 0, 0, this.width, this.height);
  //       // Get image data from canvas
  //       let imageData = context.getImageData(0, 0, this.width, this.height);
  //       let src = this.cv.matFromImageData(imageData);
  //       let dst = new this.cv.Mat();
  //       let gray = new this.cv.Mat();
  //       let edges = new this.cv.Mat();
  //       // Convert to grayscale
  //       this.cv.cvtColor(src, dst, this.cv.COLOR_RGBA2GRAY);
  //       // Apply Gaussian blur
  //       this.cv.GaussianBlur(dst, dst, new this.cv.Size(5, 5), 0);
  //       // Apply Canny edge detection
  //       this.cv.Canny(dst, dst, 75, 200);
  //       // Find contours
  //       let contours = new this.cv.MatVector();
  //       let hierarchy = new this.cv.Mat();
  //       this.cv.findContours(dst, contours, hierarchy, this.cv.RETR_EXTERNAL, this.cv.CHAIN_APPROX_SIMPLE);
  //       // Convert back to RGB for drawing
  //       this.cv.cvtColor(dst, dst, this.cv.COLOR_GRAY2RGBA);
  //       // Copy original image
  //       src.copyTo(dst);
  //       // Find the largest contour that could be a document
  //       let maxArea = 0;
  //       let maxContourIndex = -1;
  //       let documentContour = null;
  //       for (let i = 0; i < contours.size(); i++) {
  //         const contour = contours.get(i);
  //         const area = this.cv.contourArea(contour);
  //         const perimeter = this.cv.arcLength(contour, true);
  //         let approx = new this.cv.Mat();
  //         this.cv.approxPolyDP(contour, approx, 0.02 * perimeter, true);
  //         if (area > maxArea && approx.rows === 4) {
  //           maxArea = area;
  //           documentContour = approx;
  //           maxContourIndex = i;
  //         }
  //         approx.delete();
  //       }
  //       // Draw the largest contour if found
  //       if (maxContourIndex !== -1) {
  //         this.cv.drawContours(dst, contours, maxContourIndex, new this.cv.Scalar(0, 255, 0, 255), 2);
  //       }
  //       // Show result on canvas
  //       this.cv.imshow(canvas, dst);
  //       // Clean up
  //       src.delete();
  //       dst.delete();
  //       gray.delete();
  //       edges.delete();
  //       contours.delete();
  //       hierarchy.delete()
  //     }
  //     // Process next frame
  //     requestAnimationFrame(() => this.processVideo());
  //   } catch (err) {
  //     console.error('Error processing video:', err);
  //   }
  // }
  isValidDocument(vertices) {
    // Check aspect ratio (approximate A4 ratio 1:√2)
    const width = this.dist(vertices[0], vertices[1]);
    const height = this.dist(vertices[1], vertices[2]);
    const ratio = width > height ? width / height : height / width;
    return ratio > 1.3 && ratio < 1.5;
  }
  // Helper functions
  getCorners(approx) {
    const corners = [];
    for (let i = 0; i < 4; i++) {
      corners.push({
        x: approx.data32S[i * 2],
        y: approx.data32S[i * 2 + 1]
      });
    }
    return corners;
  }
  dist(p1, p2) {
    return Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2);
  }
  ngOnDestroy() {
    var _this$video;
    this.streaming = false;
    if ((_this$video = this.video) !== null && _this$video !== void 0 && _this$video.nativeElement.srcObject) {
      this.video.nativeElement.srcObject.getTracks().forEach(track => track.stop());
    }
    if (this.openCVSubscription) {
      this.openCVSubscription.unsubscribe();
    }
  }
}
_RealtimeContoursPage = RealtimeContoursPage;
_RealtimeContoursPage.ɵfac = function RealtimeContoursPage_Factory(t) {
  return new (t || _RealtimeContoursPage)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.Platform), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_open_cv_service__WEBPACK_IMPORTED_MODULE_1__.OpenCVService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_2__.ChangeDetectorRef));
};
_RealtimeContoursPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
  type: _RealtimeContoursPage,
  selectors: [["app-realtime-contours"]],
  viewQuery: function RealtimeContoursPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵviewQuery"](_c0, 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵviewQuery"](_c1, 5);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵloadQuery"]()) && (ctx.video = _t.first);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵloadQuery"]()) && (ctx.canvas = _t.first);
    }
  },
  decls: 7,
  vars: 2,
  consts: [["video", ""], ["canvas", ""], ["class", "loading-container", 4, "ngIf"], ["class", "camera-container", 4, "ngIf"], [1, "loading-container"], [1, "camera-container"], [2, "display", "none", 3, "width", "height"], [3, "width", "height"]],
  template: function RealtimeContoursPage_Template(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "ion-header")(1, "ion-toolbar")(2, "ion-title");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Document Scanner");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "ion-content");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](5, RealtimeContoursPage_div_5_Template, 4, 0, "div", 2)(6, RealtimeContoursPage_div_6_Template, 5, 4, "div", 3);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.isOpenCVReady);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isOpenCVReady);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonSpinner, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonTitle, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonToolbar],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\nion-content[_ngcontent-%COMP%]::part(scroll) {\n  overflow-y: hidden !important;\n  --overflow: hidden !important;\n}\n\nion-content[_ngcontent-%COMP%] {\n  --offset-top: 0px !important;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  background-color: #e5e7eb;\n}\n\n.camera-button[_ngcontent-%COMP%] {\n  --background: #3b82f6;\n  --background-activated: #2563eb;\n  border-radius: 50%;\n  width: 60px;\n  height: 60px;\n  margin-top: -30px;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #3b82f6;\n}\n\nion-button[_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-button[slot=icon-only][_ngcontent-%COMP%] {\n  --color: #3b82f6;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  background-color: #dddbff;\n  height: 110px;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-direction: row;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\n  --border-width: 0;\n}\n\nion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%], ion-footer[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  flex-direction: row;\n}\n\n  ion-button.menu-button app-custom-icon img {\n  width: 30px !important;\n  height: 30px !important;\n  color: #000;\n}\n\n  .menu-button.active app-custom-icon img {\n  color: #2f4fcd;\n}\n\n  .menu-button-middle {\n  background-color: #2f4fcd;\n  padding: 2px 12px;\n  border-radius: 14px;\n  width: 85px;\n  height: 60px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 15px;\n}\n  .menu-button-middle app-custom-icon img {\n  width: 45px !important;\n  height: 45px !important;\n  color: #fff;\n}\n\n.videoCamera[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  background-color: #000;\n  display: flex !important;\n  justify-content: center !important;\n  align-items: flex-start !important;\n}\n\n.videoCanva[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  height: auto;\n  padding: 0;\n  margin: 0;\n}\n\n.videoCanva[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  padding: 0;\n  margin: 0;\n}\n\n.camera-container[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: #000;\n}\n.camera-container[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n[_nghost-%COMP%] {\n  display: block;\n  width: 100%;\n  height: 100%;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ }),

/***/ 78208:
/*!*********************************************!*\
  !*** ./src/app/services/open-cv.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OpenCVService: () => (/* binding */ OpenCVService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 75797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _OpenCVService;


class OpenCVService {
  constructor() {
    this.openCVLoadedSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(false);
    this.openCVLoaded$ = this.openCVLoadedSubject.asObservable();
    this.initOpenCV();
  }
  initOpenCV() {
    // Check if OpenCV is already loaded
    if (typeof window !== 'undefined' && window.cv) {
      console.log('OpenCV already available');
      this.openCVLoadedSubject.next(true);
      return;
    }
    // If not loaded, create a promise to wait for it
    const loadOpenCV = new Promise(resolve => {
      window.onOpenCVReady = () => {
        console.log('OpenCV is ready');
        resolve();
      };
    });
    // Wait for OpenCV to be ready
    loadOpenCV.then(() => {
      this.openCVLoadedSubject.next(true);
    });
  }
  getOpenCV() {
    return new Promise(resolve => {
      if (this.openCVLoadedSubject.value) {
        resolve(window.cv);
      } else {
        this.openCVLoaded$.subscribe(loaded => {
          if (loaded) {
            resolve(window.cv);
          }
        });
      }
    });
  }
}
_OpenCVService = OpenCVService;
_OpenCVService.ɵfac = function OpenCVService_Factory(t) {
  return new (t || _OpenCVService)();
};
_OpenCVService.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
  token: _OpenCVService,
  factory: _OpenCVService.ɵfac,
  providedIn: 'root'
});

/***/ })

}]);
//# sourceMappingURL=src_app_realtime-contours_realtime-contours_module_ts.js.map