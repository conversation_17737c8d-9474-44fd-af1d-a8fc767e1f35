{"version": 3, "file": "src_app_process-doc_process-doc_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAEH;;;AAEpD,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,6DAAcA;CAC1B,CACF;AAMK,MAAOI,2BAA2B;+BAA3BA,2BAA2B;;mBAA3BA,4BAA2B;AAAA;;QAA3BA;AAA2B;;YAH5BL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,2BAA2B;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAF5BT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAE8B;AACpB,CAAC;AAEJ;;AAa9C,MAAOc,oBAAoB;wBAApBA,oBAAoB;;mBAApBA,qBAAoB;AAAA;;QAApBA;AAAoB;;YAT7BJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,oFAA2B,EAC3BQ,+DAAY;AAAA;;sHAKHC,oBAAoB;IAAAC,YAAA,GAFhBd,6DAAc;IAAAM,OAAA,GAP3BG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,oFAA2B,EAC3BQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVO;AAEwB;AACY;AAIA;;;;;;;;;;;;;;;;;;ICOrDO,4DAAA,4BAGG;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EACpB;;;;IAFCA,wDAAA,UAAAK,SAAA,CAAAC,KAAA,CAAsB;IACrBN,uDAAA,EAAkB;IAAlBA,+DAAA,CAAAK,SAAA,CAAAI,KAAA,CAAkB;;;;;IA+BfT,uDAAA,cAAgD;;;;;IAChDA,4DAAA,cAA8D;IAC5DA,uDAAA,kBAA2B;IAC7BA,0DAAA,EAAM;;;;;;IAZVA,4DAHJ,uBAAgF,cAC7C,cAEgB;IAAvBA,wDAAA,mBAAAY,6DAAA;MAAA,MAAAC,IAAA,GAAAb,2DAAA,CAAAe,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAjB,2DAAA;MAAA,OAAAA,yDAAA,CAASiB,MAAA,CAAAG,SAAA,CAAAP,IAAA,CAAY;IAAA,EAAC;IAC5Cb,uDAAA,mBAA+C;IACjDA,0DAAA,EAAM;IACNA,4DAAA,cAA6B;IAC3BA,uDAAA,cAAgG;IAEhGA,4DAAA,cAEC;IAECA,wDADA,IAAAsB,6CAAA,kBAA0C,IAAAC,6CAAA,kBACoB;IAG9DvB,4DAAA,cAKE;IADAA,wDAAA,kBAAAwB,4DAAA;MAAAxB,2DAAA,CAAAe,GAAA;MAAA,MAAAE,MAAA,GAAAjB,2DAAA;MAAA,OAAAA,yDAAA,CAAQiB,MAAA,CAAAQ,mBAAA,EAAqB;IAAA,EAAC;IAKxCzB,0DATQ,EAKE,EACE,EACF,EACF,EACO;;;;;IAlBiBA,uDAAA,GAA0B;IAA1BA,wDAAA,QAAA0B,OAAA,CAAAC,aAAA,EAAA3B,2DAAA,CAA0B;IAGlDA,uDAAA,EAA0H;IAA1HA,wDAAA,YAAAA,6DAAA,IAAA8B,GAAA,EAAAb,MAAA,CAAAc,UAAA,EAAAd,MAAA,CAAAe,aAAA,EAAAf,MAAA,CAAAc,UAAA,EAA0H;IAElG/B,uDAAA,EAAgB;IAAhBA,wDAAA,SAAAiB,MAAA,CAAAc,UAAA,CAAgB;IAClC/B,uDAAA,EAA0B;IAA1BA,wDAAA,UAAAiB,MAAA,CAAAgB,mBAAA,CAA0B;IAI9BjC,uDAAA,EAA2B;IAE3BA,wDAFA,QAAA0B,OAAA,CAAAQ,cAAA,EAAAlC,2DAAA,CAA2B,YAAAiB,MAAA,CAAAgB,mBAAA,CAEI;;;ADxCvC,MAAOpD,cAAc;EAyBzBsD,YACUC,KAAqB,EACrBC,QAAkB,EAClBC,eAAgC,EAChCC,EAAqB,EACrBC,UAAsB;IAJtB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IAxBpB,KAAAT,UAAU,GAAY,KAAK;IAC3B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAS,gBAAgB,GAAW,EAAE;IAE7B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,aAAa,GAAW,EAAE;IAE1B,KAAAC,mBAAmB,GAAY,KAAK,CAAC,CAAC;IACtC,KAAAX,mBAAmB,GAAY,KAAK;IACpC,KAAAY,SAAS,GAAU,EAAE,CAAC,CAAC;IAEvB,KAAAC,QAAQ,GAAqB,EAAE;IAE/B,KAAAC,OAAO,GAAGnD,qDAAM,CAACC,yDAAa,CAAC;IAC/B,KAAAmD,aAAa,GAAGpD,qDAAM,CAACE,mEAAa,CAAC;IAErC,KAAAmD,WAAW,GACTlD,qEAAW,CAACmD,QAAQ,KAAK,KAAK,GAAG,cAAc,GAAG,cAAc;EAQ/D;EAEGC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MAAA,IAAAC,qBAAA;MACZ,MAAMF,KAAI,CAACG,gBAAgB,EAAE;MAE7B;MACAH,KAAI,CAACP,SAAS,CAACW,OAAO,CAAEC,QAAQ,IAAI;QAClC,IAAIA,QAAQ,CAACnD,KAAK,KAAK,QAAQ,EAAE;UAC/BmD,QAAQ,CAACnD,KAAK,GAAG,OAAO;;MAE5B,CAAC,CAAC;MACF,MAAMoD,MAAM,GAAGN,KAAI,CAACf,QAAQ,CAACsB,QAAQ,EAAS;MAC9CP,KAAI,CAACQ,IAAI,GAAGF,MAAM;MAClBG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEV,KAAI,CAACQ,IAAI,CAAC;MAExCG,UAAU,eAAAV,6OAAA,CAAC,aAAW;QACpB,MAAMD,KAAI,CAACY,eAAe,EAAE;MAC9B,CAAC,GAAE,GAAG,CAAC;MAEPZ,KAAI,CAACN,QAAQ,GAAGM,KAAI,CAACJ,aAAa,CAACiB,OAAO,EAAE;MAC5Cb,KAAI,CAACX,gBAAgB,IAAAa,qBAAA,GAAGY,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,cAAAb,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAEtEF,KAAI,CAACb,EAAE,CAAC6B,aAAa,EAAE,CAAC,CAAC;MAEzB;MACA,IAAIhB,KAAI,CAACJ,aAAa,CAACqB,UAAU,EAAE;QACjC,IAAIjB,KAAI,CAACQ,IAAI,CAACU,aAAa,IAAIlB,KAAI,CAACQ,IAAI,CAACU,aAAa,IAAI,SAAS,EAAE;UACnElB,KAAI,CAACX,gBAAgB,GAAGW,KAAI,CAACQ,IAAI,CAACU,aAAa,CAACC,WAAW,EAAE;UAC7DL,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAEpB,KAAI,CAACX,gBAAgB,CAAC;SAChE,MAAM,IACLW,KAAI,CAACX,gBAAgB,IAAI,EAAE,IAC3B,CAACW,KAAI,CAACX,gBAAgB,IACtBW,KAAI,CAACX,gBAAgB,IAAI,SAAS,EAClC;UACA;UACA,MAAMgC,KAAK,SAASrB,KAAI,CAACd,eAAe,CAACoC,MAAM,CAAC;YAC9CC,MAAM,EAAE,aAAa;YACrBC,OAAO,EAAE,yEAAyE;YAClFC,OAAO,EAAE,CACP;cACEC,IAAI,EAAE,KAAK;cACXC,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBAAA,IAAAC,sBAAA;gBACZ9B,KAAI,CAACX,gBAAgB,IAAAyC,sBAAA,GACnBhB,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,cAAAe,sBAAA,cAAAA,sBAAA,GAAI,EAAE;gBAChDrB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC/B;aACD,EACD;cACEgB,IAAI,EAAE,KAAK;cACXE,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBAAA,IAAAE,iBAAA,EAAAC,kBAAA;gBACZ,MAAMC,aAAa,IAAAF,iBAAA,GACjB/B,KAAI,CAACkC,WAAW,cAAAH,iBAAA,gBAAAA,iBAAA,GAAhBA,iBAAA,CAAkBI,aAAa,CAACC,MAAM,cAAAL,iBAAA,uBAAtCA,iBAAA,CAAwCM,WAAW;gBACrD,CAAAL,kBAAA,GAAAhC,KAAI,CAACkC,WAAW,cAAAF,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkBG,aAAa,CAACC,MAAM,cAAAJ,kBAAA,eAAtCA,kBAAA,CAAwCM,WAAW,CACjDL,aAAa,CACd;gBACDjC,KAAI,CAACJ,aAAa,CAAC2C,UAAU,CAACN,aAAa,CAAC;gBAC5CjC,KAAI,CAACN,QAAQ,GAAGM,KAAI,CAACJ,aAAa,CAACiB,OAAO,EAAE;gBAC5Cb,KAAI,CAACL,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;cACvC;aACD;WAEJ,CAAC;UAEF;UACAxC,KAAI,CAACR,mBAAmB,GAAG,IAAI;UAE/B;UACAsB,YAAY,CAACM,OAAO,CAClB,qBAAqB,EACrBpB,KAAI,CAACR,mBAAmB,CAACiD,QAAQ,EAAE,CACpC;UAED,MAAMpB,KAAK,CAACqB,OAAO,EAAE;SACtB,MAAM;UACL1C,KAAI,CAACX,gBAAgB,GAAG,EAAE;;OAE7B,MAAM;QACL;QACA,IACEW,KAAI,CAACQ,IAAI,CAACU,aAAa,IACvBlB,KAAI,CAACQ,IAAI,CAACU,aAAa,IAAI,SAAS,IACpClB,KAAI,CAACQ,IAAI,CAACU,aAAa,CAACC,WAAW,EAAE,IACnCnB,KAAI,CAACX,gBAAgB,CAAC8B,WAAW,EAAE,IACrCnB,KAAI,CAACN,QAAQ,CAACiD,MAAM,GAAG,CAAC,EACxB;UACA;UACA,MAAMtB,KAAK,SAASrB,KAAI,CAACd,eAAe,CAACoC,MAAM,CAAC;YAC9CC,MAAM,EAAE,aAAa;YACrBC,OAAO,EAAE,EAAE;YACXC,OAAO,EAAE,CACP;cACEC,IAAI,EAAE,SAAS;cACfC,IAAI,EAAE,QAAQ;cACdC,QAAQ,EAAE,4BAA4B;cACtCC,OAAO,EAAEA,CAAA,KAAK;gBAAA,IAAAe,sBAAA;gBACZnC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;gBAC7B;gBACAV,KAAI,CAACX,gBAAgB,IAAAuD,sBAAA,GACnB9B,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,cAAA6B,sBAAA,cAAAA,sBAAA,GAAI,EAAE;cAClD;aACD,EACD;cACElB,IAAI,EAAE,sBAAsB;cAC5BE,QAAQ,EAAE,6BAA6B;cACvCC,OAAO,EAAEA,CAAA,KAAK;gBACZpB,OAAO,CAACC,GAAG,CACT,4BAA4B,EAC5BV,KAAI,CAACQ,IAAI,CAACU,aAAa,CACxB;gBACDT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEV,KAAI,CAACX,gBAAgB,CAAC;gBAC/DW,KAAI,CAACX,gBAAgB,GAAGW,KAAI,CAACQ,IAAI,CAACU,aAAa,CAACC,WAAW,EAAE;gBAC7DL,YAAY,CAACM,OAAO,CAClB,kBAAkB,EAClBpB,KAAI,CAACQ,IAAI,CAACU,aAAa,CAACC,WAAW,EAAE,CACtC;gBACDV,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEV,KAAI,CAACQ,IAAI,CAAC;cACvC;aACD;WAEJ,CAAC;UACF,MAAMa,KAAK,CAACqB,OAAO,EAAE;UAErB;UACA,MAAMG,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;UAC7D,IAAIF,YAAY,EAAE;YAChBA,YAAY,CAACG,SAAS,GAAG,6BACvBhD,KAAI,CAACX,gBACP,iDAAiDW,KAAI,CAACQ,IAAI,CAACU,aAAa,CAACC,WAAW,EAAE,kEAAkE;;;;MAK9JR,UAAU,CAAC,MAAK;QACdX,KAAI,CAACiD,WAAW,EAAE;QAElB,IAAInC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI,EAAE;UAAA,IAAAmC,sBAAA;UACpDzC,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpBI,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,CACzC;UACDf,KAAI,CAACX,gBAAgB,IAAA6D,sBAAA,GAAGpC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,cAAAmC,sBAAA,cAAAA,sBAAA,GAAI,EAAE;;MAE1E,CAAC,EAAE,IAAI,CAAC;IAAC;EACX;EAEMtC,eAAeA,CAAA;IAAA,IAAAuC,MAAA;IAAA,OAAAlD,6OAAA;MACnB;MACAkD,MAAI,CAAC/D,UAAU,CAACwB,eAAe,EAAE,CAACwC,SAAS,CACxC3D,SAAS,IAAI;QACZ0D,MAAI,CAAC1D,SAAS,GAAGA,SAAS,CAAC4D,GAAG,CAAEhD,QAAa,IAAI;UAC/C,IAAIA,QAAQ,CAACnD,KAAK,KAAK,QAAQ,EAAE;YAC/B,OAAO;cAAEA,KAAK,EAAE,OAAO;cAAEG,KAAK,EAAE;YAAO,CAAE;;UAG3C;UACA,IAAIgD,QAAQ,CAACnD,KAAK,CAACoG,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChC,OAAO;cACL,GAAGjD,QAAQ;cACXnD,KAAK,EAAEmD,QAAQ,CAACnD,KAAK;cACrBG,KAAK,EAAEgD,QAAQ,CAAChD,KAAK,CAACkG,OAAO,CAAC,IAAI,EAAE,GAAG;aACxC;;UAGH,OAAOlD,QAAQ;QACjB,CAAC,CAAC;QAEF;QACA8C,MAAI,CAAC1D,SAAS,GAAG0D,MAAI,CAAC1D,SAAS,CAAC+D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACvG,KAAK,KAAK,OAAO,GAAG,CAAC,GAAGwG,CAAC,CAACxG,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC;MAC1G,CAAC,EACAyG,KAAK,IAAI;QACRlD,OAAO,CAACkD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;IAAC;EACN;EAIQxD,gBAAgBA,CAAA;IAAA,IAAAyD,MAAA;IAAA,OAAA3D,6OAAA;MACpB,IACE2D,MAAI,CAAChE,aAAa,CAACiB,OAAO,EAAE,IAAI,IAAI,IACpC+C,MAAI,CAAChE,aAAa,CAACiB,OAAO,EAAE,IAAIgD,SAAS,IACzCD,MAAI,CAAChE,aAAa,CAACiB,OAAO,EAAE,CAAC8B,MAAM,IAAI,CAAC,EACxC;QACAiB,MAAI,CAACjE,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;;IACtC;EACH;EAEAS,WAAWA,CAAA;IACT,IAAI,CAACtE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnBgC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC/B,aAAa,GAAG,IAAI;MAC3B,CAAC,EAAE,IAAI,CAAC;MACR,IAAI,CAACA,aAAa,GAAG,KAAK;;EAE9B;EAEMkF,cAAcA,CAACC,OAAe;IAAA,OAAA9D,6OAAA;MAClC,IAAI;QACF,MAAM+D,QAAQ,SAASC,KAAK,CAACF,OAAO,CAAC;QACrC,OAAOC,QAAQ,CAACE,EAAE,CAAC,CAAC;OACrB,CAAC,OAAOP,KAAK,EAAE;QACd,OAAO,KAAK,CAAC,CAAC;;IACf;EACH;EAEAQ,aAAaA,CAAA;IACX1D,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAI,CAAC0D,WAAW,EAAE;EACpB;EACAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,aAAa,EAAE;MACtD,MAAMkC,mBAAmB,GAAG,IAAI,CAACnC,WAAW,CAACC,aAAa,CAACC,MAAM;;EAErE;EAEMpE,SAASA,CAACJ,KAAa;IAAA,IAAA0G,MAAA;IAAA,OAAArE,6OAAA;MAC3B,MAAMoB,KAAK,SAASiD,MAAI,CAACpF,eAAe,CAACoC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE,kDAAkD;QAC3DC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZpB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEgB,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAA0C,kBAAA;YACZD,MAAI,CAAC1E,aAAa,CAAC2C,UAAU,CAAC3E,KAAK,CAAC;YACpC0G,MAAI,CAAC5E,QAAQ,GAAG4E,MAAI,CAAC1E,aAAa,CAACiB,OAAO,EAAE;YAC5C,CAAA0D,kBAAA,GAAAD,MAAI,CAACpC,WAAW,cAAAqC,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkBpC,aAAa,CAACC,MAAM,cAAAmC,kBAAA,eAAtCA,kBAAA,CAAwCjC,WAAW,CAAC1E,KAAK,CAAC;YAC1D,IAAI0G,MAAI,CAAC1E,aAAa,CAACiB,OAAO,EAAE,CAAC8B,MAAM,IAAI,CAAC,EAAE;cAC5C7B,YAAY,CAAC0D,UAAU,CAAC,kBAAkB,CAAC;cAC3CF,MAAI,CAAC3E,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;;UAEzC;SACD;OAEJ,CAAC;MAEF,MAAMnB,KAAK,CAACqB,OAAO,EAAE;IAAC;EACxB;EAEM+B,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzE,6OAAA;MAChB,MAAMoB,KAAK,SAASqD,MAAI,CAACxF,eAAe,CAACoC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE,yDAAyD;QAClEC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZpB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEgB,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAA8C,kBAAA;YACZD,MAAI,CAAC9E,aAAa,CAACgF,aAAa,EAAE;YAClCF,MAAI,CAAChF,QAAQ,GAAG,EAAE;YAClB,CAAAiF,kBAAA,GAAAD,MAAI,CAACxC,WAAW,cAAAyC,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkBxC,aAAa,CAACC,MAAM,cAAAuC,kBAAA,eAAtCA,kBAAA,CAAwCE,eAAe,EAAE;YACzD/D,YAAY,CAAC0D,UAAU,CAAC,kBAAkB,CAAC;YAC3C;YACAE,MAAI,CAAC/E,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;UACvC;SACD;OAEJ,CAAC;MAEF,MAAMnB,KAAK,CAACqB,OAAO,EAAE;IAAC;EACxB;EAEMoC,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9E,6OAAA;MACf,MAAMoB,KAAK,SAAS0D,MAAI,CAAC7F,eAAe,CAACoC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE,sDAAsD;QAC/DC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZpB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEgB,IAAI,EAAE,KAAK;UACXE,QAAQ,EAAE,qBAAqB;UAC/BC,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAAmD,kBAAA,EAAAC,mBAAA;YACZ,MAAMhD,aAAa,IAAA+C,kBAAA,GACjBD,MAAI,CAAC7C,WAAW,cAAA8C,kBAAA,gBAAAA,kBAAA,GAAhBA,kBAAA,CAAkB7C,aAAa,CAACC,MAAM,cAAA4C,kBAAA,uBAAtCA,kBAAA,CAAwC3C,WAAW;YACrD;YACA,CAAA4C,mBAAA,GAAAF,MAAI,CAAC7C,WAAW,cAAA+C,mBAAA,gBAAAA,mBAAA,GAAhBA,mBAAA,CAAkB9C,aAAa,CAACC,MAAM,cAAA6C,mBAAA,eAAtCA,mBAAA,CAAwC3C,WAAW,CAACL,aAAa,CAAC;YAClE;YACA8C,MAAI,CAACnF,aAAa,CAAC2C,UAAU,CAACN,aAAa,CAAC;YAC5C8C,MAAI,CAACrF,QAAQ,GAAGqF,MAAI,CAACnF,aAAa,CAACiB,OAAO,EAAE;YAC5C;YACAkE,MAAI,CAACpF,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;UACvC;SACD;OAEJ,CAAC;MAEF,MAAMnB,KAAK,CAACqB,OAAO,EAAE;IAAC;EACxB;EAEMwC,gBAAgBA,CAACC,MAAW;IAAA,IAAAC,MAAA;IAAA,OAAAnF,6OAAA;MAChCQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyE,MAAM,CAACE,MAAM,CAACnI,KAAK,CAAC;MACtD,IAAIiI,MAAM,CAACE,MAAM,CAACnI,KAAK,IAAI4D,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnE;QACA,MAAMM,KAAK,SAAS+D,MAAI,CAAClG,eAAe,CAACoC,MAAM,CAAC;UAC9CC,MAAM,EAAE,aAAa;UACrBC,OAAO,EAAE,qHAAqH;UAC9HC,OAAO,EAAE,CACP;YACEC,IAAI,EAAE,SAAS;YACfC,IAAI,EAAE,QAAQ;YACdC,QAAQ,EAAE,4BAA4B;YACtCC,OAAO,EAAEA,CAAA,KAAK;cAAA,IAAAyD,sBAAA;cACZ7E,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC7B;cACA0E,MAAI,CAAC/F,gBAAgB,IAAAiG,sBAAA,GACnBxE,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,cAAAuE,sBAAA,cAAAA,sBAAA,GAAI,EAAE;YAClD;WACD,EACD;YACE5D,IAAI,EAAE,gBAAgB;YACtBE,QAAQ,EAAE,6BAA6B;YACvCC,OAAO,EAAEA,CAAA,KAAK;cACZuD,MAAI,CAAC/F,gBAAgB,GAAG8F,MAAM,CAACE,MAAM,CAACnI,KAAK;cAC3CkI,MAAI,CAAC1F,QAAQ,CAACU,OAAO,CAAEmF,GAAG,IAAI;gBAC5BA,GAAG,CAACrE,aAAa,GAAGiE,MAAM,CAACE,MAAM,CAACnI,KAAK;cACzC,CAAC,CAAC;cACFuD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE0E,MAAI,CAAC1F,QAAQ,CAAC;cACzCoB,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAE+D,MAAM,CAACE,MAAM,CAACnI,KAAK,IAAI,OAAO,GAAG,QAAQ,GAAGiI,MAAM,CAACE,MAAM,CAACnI,KAAK,CAAC;cACzGuD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyE,MAAM,CAACE,MAAM,CAACnI,KAAK,CAAC;YACxD;WACD;SAEJ,CAAC;QAEF,MAAMmE,KAAK,CAACqB,OAAO,EAAE;;IACtB;EACH;EAEM8C,OAAOA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAxF,6OAAA;MACX;MACA,IAAIwF,MAAI,CAACpG,gBAAgB,IAAI,EAAE,EAAE;QAC/BoG,MAAI,CAAC9F,OAAO,CAAC6C,YAAY,CAAC,WAAW,CAAC;OACvC,MAAM;QACL,MAAMnB,KAAK,SAASoE,MAAI,CAACvG,eAAe,CAACoC,MAAM,CAAC;UAC9CC,MAAM,EAAE,aAAa;UACrBC,OAAO,EAAE,sCAAsC;UAC/CC,OAAO,EAAE,CAAC,IAAI;SACf,CAAC;QACF,MAAMJ,KAAK,CAACqB,OAAO,EAAE;;IACtB;EACH;EAEAgD,aAAaA,CAACC,IAAU;IACtB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAI;QAAA,IAAAC,aAAA;QACxB,MAAMC,GAAG,GAAG,IAAIC,KAAK,EAAE;QACvBD,GAAG,CAACE,GAAG,IAAAH,aAAA,GAAGD,KAAK,CAACK,MAAM,cAAAJ,aAAA,uBAAZA,aAAA,CAAcK,MAAgB;QACxCJ,GAAG,CAACH,MAAM,GAAG,MAAK;UAChB,MAAMQ,MAAM,GAAG3D,QAAQ,CAAC4D,aAAa,CAAC,QAAQ,CAAC;UAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;UACnC,MAAMC,SAAS,GAAG,IAAI;UACtB,MAAMC,UAAU,GAAG,IAAI;UACvB,IAAIC,KAAK,GAAGX,GAAG,CAACW,KAAK;UACrB,IAAIC,MAAM,GAAGZ,GAAG,CAACY,MAAM;UAEvB,IAAID,KAAK,GAAGC,MAAM,IAAID,KAAK,GAAGF,SAAS,EAAE;YACvCG,MAAM,GAAIA,MAAM,GAAGH,SAAS,GAAIE,KAAK;YACrCA,KAAK,GAAGF,SAAS;WAClB,MAAM,IAAIG,MAAM,GAAGD,KAAK,IAAIC,MAAM,GAAGF,UAAU,EAAE;YAChDC,KAAK,GAAIA,KAAK,GAAGD,UAAU,GAAIE,MAAM;YACrCA,MAAM,GAAGF,UAAU;;UAErBL,MAAM,CAACM,KAAK,GAAGA,KAAK;UACpBN,MAAM,CAACO,MAAM,GAAGA,MAAM;UACtBL,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEM,SAAS,CAACb,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEW,KAAK,EAAEC,MAAM,CAAC;UACxCnB,OAAO,CAACY,MAAM,CAACS,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;MACH,CAAC;MACDnB,MAAM,CAACoB,OAAO,GAAGrB,MAAM;MACvBC,MAAM,CAACqB,aAAa,CAACzB,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA0B,YAAYA,CAACf,GAAW;IACtB,OAAO,IAAIV,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMM,GAAG,GAAG,IAAIC,KAAK,EAAE;MACvBD,GAAG,CAACE,GAAG,GAAGA,GAAG;MACbF,GAAG,CAACH,MAAM,GAAG,MAAMJ,OAAO,EAAE;MAC5BO,GAAG,CAACe,OAAO,GAAIG,GAAG,IAAKxB,MAAM,CAACwB,GAAG,CAAC;IACpC,CAAC,CAAC;EACJ;EAEMC,iBAAiBA,CAACC,IAAoB;IAAA,IAAAC,MAAA;IAAA,OAAAxH,6OAAA;MAC1C,IAAI;QACF,IAAIuH,IAAI,CAAC1I,cAAc,EAAE;UACvB,MAAM2I,MAAI,CAACJ,YAAY,CAACG,IAAI,CAAC1I,cAAc,CAAC;UAC5C2I,MAAI,CAAC9I,UAAU,GAAG,IAAI;SACvB,MAAM;UACL8B,OAAO,CAACkD,KAAK,CAAC,mCAAmC,CAAC;;OAErD,CAAC,OAAOA,KAAK,EAAE;QACdlD,OAAO,CAACkD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;;IAC9D;EACH;EAEAtF,mBAAmBA,CAAA;IACjB,IAAI,CAACQ,mBAAmB,GAAG,IAAI;EACjC;EAEA6I,UAAUA,CAACC,GAAW,EAAEC,QAAgB;IACtC9G,YAAY,CAACM,OAAO,CAACuG,GAAG,EAAEC,QAAQ,CAAC;EACrC;EAEAC,cAAcA,CAACF,GAAW;IACxB,OAAO7G,YAAY,CAACC,OAAO,CAAC4G,GAAG,CAAC;EAClC;EAGAG,OAAOA,CAAA;IACL,IAAI,CAACnI,OAAO,CAAC6C,YAAY,CAAC,UAAU,CAAC;EACvC;;kBArdW/G,cAAc;;mBAAdA,eAAc,EAAAmB,+DAAA,CAAAZ,2DAAA,GAAAY,+DAAA,CAAAqL,qDAAA,GAAArL,+DAAA,CAAAuL,2DAAA,GAAAvL,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAA0L,6DAAA;AAAA;;QAAd7M,eAAc;EAAA+M,SAAA;EAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAhC,GAAA;IAAA,IAAAgC,EAAA;;;;;;;;;;;;;;MCpBvB/L,4DAFJ,iBAAY,kBACG,gBACA;MAAAA,oDAAA,uBAAgB;MAAAA,0DAAA,EAAY;MAErCA,4DADF,qBAAwB,oBACU;MAApBA,wDAAA,mBAAAgM,oDAAA;QAAAhM,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAS+J,GAAA,CAAAmB,OAAA,EAAS;MAAA,EAAC;MAC7BlL,uDAAA,yBAAkD;MAI1DA,0DAHM,EAAa,EACD,EACF,EACH;MAKTA,4DAHJ,qBAAyC,aAEF,oBAOlC;MALCA,8DAAA,2BAAAmM,4DAAA5D,MAAA;QAAAvI,2DAAA,CAAAiM,GAAA;QAAAjM,gEAAA,CAAA+J,GAAA,CAAAtH,gBAAA,EAAA8F,MAAA,MAAAwB,GAAA,CAAAtH,gBAAA,GAAA8F,MAAA;QAAA,OAAAvI,yDAAA,CAAAuI,MAAA;MAAA,EAA8B;MAI9BvI,wDAAA,uBAAAqM,wDAAA9D,MAAA;QAAAvI,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAa+J,GAAA,CAAAzB,gBAAA,CAAAC,MAAA,CAAwB;MAAA,EAAC;MAEtCvI,wDAAA,KAAAsM,4CAAA,+BAGG;MAGPtM,0DADE,EAAa,EACT;MAENA,4DAAA,8BAaC;MADCA,wDAAA,+BAAAuM,uEAAA;QAAAvM,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAqB+J,GAAA,CAAAxC,aAAA,EAAe;MAAA,EAAC;MAErCvH,wDAAA,KAAAwM,uCAAA,4BAAgF;MA2B9ExM,4DADF,wBAAsF,eACvD;MAC3BA,uDAAA,2BAA+E;MAE7EA,4DADF,eAAqB,UACf;MAAAA,oDAAA,8BAAsB;MAAAA,0DAAA,EAAK;MAC/BA,4DAAA,SAAG;MAAAA,oDAAA,yDAAuC;MAKpDA,0DALoD,EAAI,EAC1C,EACF,EACO,EACE,EACP;MAKRA,4DAHN,kBAAY,mBACG,mBACE,sBAC2D;MAAzBA,wDAAA,mBAAAyM,qDAAA;QAAAzM,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAS+J,GAAA,CAAAlC,YAAA,EAAc;MAAA,EAAC;MACnE7H,uDAAA,2BAAiD;MACnDA,0DAAA,EAAa;MACbA,4DAAA,sBAA0D;MAApBA,wDAAA,mBAAA0M,qDAAA;QAAA1M,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAS+J,GAAA,CAAAnB,OAAA,EAAS;MAAA,EAAC;MACvD5I,uDAAA,2BAAkD;MAClDA,4DAAA,YAAM;MAAAA,oDAAA,eAAO;MACfA,0DADe,EAAO,EACT;MACbA,4DAAA,sBAA4E;MAAxBA,wDAAA,mBAAA2M,qDAAA;QAAA3M,2DAAA,CAAAiM,GAAA;QAAA,OAAAjM,yDAAA,CAAS+J,GAAA,CAAA7B,WAAA,EAAa;MAAA,EAAC;MACzElI,uDAAA,2BAAoD;MAI5DA,0DAHM,EAAa,EACD,EACF,EACH;;;MAjFPA,uDAAA,GAA8B;MAA9BA,8DAAA,YAAA+J,GAAA,CAAAtH,gBAAA,CAA8B;MAOTzC,uDAAA,EAAY;MAAZA,wDAAA,YAAA+J,GAAA,CAAAlH,SAAA,CAAY;MAqBJ7C,uDAAA,GAAa;MAAbA,wDAAA,YAAA+J,GAAA,CAAAjH,QAAA,CAAa;MA0Bc9C,uDAAA,EAA2B;MAA3BA,wDAAA,eAAAA,6DAAA,IAAA8M,GAAA,EAA2B", "sources": ["./src/app/process-doc/process-doc-routing.module.ts", "./src/app/process-doc/process-doc.module.ts", "./src/app/process-doc/process-doc.page.ts", "./src/app/process-doc/process-doc.page.html"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { ProcessDocPage } from './process-doc.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ProcessDocPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ProcessDocPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { ProcessDocPageRoutingModule } from './process-doc-routing.module';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\nimport { ProcessDocPage } from './process-doc.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    ProcessDocPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [ProcessDocPage]\r\n})\r\nexport class ProcessDocPageModule {}\r\n", "import { Location } from '@angular/common';\r\nimport {\r\n  Component,\r\n  ElementRef,\r\n  OnInit,\r\n  ViewChild,\r\n  inject,\r\n} from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { NavController } from '@ionic/angular';\r\nimport { SignalService } from '../services/signal.service';\r\nimport { ProcessDocData } from 'src/models/ProcessDocData';\r\nimport { AlertController } from '@ionic/angular';\r\nimport { ChangeDetectorRef } from '@angular/core';\r\nimport { environment } from 'src/environments/environment';\r\nimport { ApiService } from '../services/api.service';\r\n\r\n@Component({\r\n  selector: 'app-process-doc',\r\n  templateUrl: './process-doc.page.html',\r\n  styleUrls: ['./process-doc.page.scss'],\r\n})\r\nexport class ProcessDocPage implements OnInit {\r\n  @ViewChild('swiper_docs', { static: true }) swiper_docs:\r\n    | ElementRef\r\n    | undefined;\r\n\r\n  data: any;\r\n  isScanning: boolean = false;\r\n  hideAnimation: boolean = false;\r\n  selectedSupplier: string = '';\r\n\r\n  croppedImage: string = '';\r\n  filteredImage: string = '';\r\n\r\n  forceSupplierGlobal: boolean = false; // force the supplier to be global for apply the Advanced OCR 'Mindee'\r\n  filteredImageLoaded: boolean = false;\r\n  suppliers: any[] = []; // Initialize suppliers as an empty array\r\n\r\n  listDocs: ProcessDocData[] = [];\r\n\r\n  navCtrl = inject(NavController);\r\n  signalService = inject(SignalService);\r\n\r\n  swiperClass: string =\r\n    environment.platform === 'web' ? 'swiper-small' : 'swiper-large';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private alertController: AlertController,\r\n    private cd: ChangeDetectorRef,\r\n    private apiService: ApiService\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    await this.redirectToScanBL();\r\n    \r\n    // if the value is equal 'GLOBAL' then change it to 'AUTRE'\r\n    this.suppliers.forEach((supplier) => {\r\n      if (supplier.value === 'GLOBAL') {\r\n        supplier.value = 'AUTRE';\r\n      }\r\n    });\r\n    const params = this.location.getState() as any;\r\n    this.data = params;\r\n    console.log('Received data:', this.data);\r\n\r\n    setTimeout(async () => {\r\n      await this.getAllSuppliers();\r\n    }, 200);\r\n\r\n    this.listDocs = this.signalService.getData();\r\n    this.selectedSupplier = localStorage.getItem('selectedSupplier') ?? '';\r\n\r\n    this.cd.detectChanges(); // Add this line\r\n\r\n    // Check if it's the first entry\r\n    if (this.signalService.firstEntry) {\r\n      if (this.data.supplier_name && this.data.supplier_name != 'Unknown') {\r\n        this.selectedSupplier = this.data.supplier_name.toUpperCase();\r\n        localStorage.setItem('selectedSupplier', this.selectedSupplier);\r\n      } else if (\r\n        this.selectedSupplier == '' ||\r\n        !this.selectedSupplier ||\r\n        this.selectedSupplier == 'UNKNOWN'\r\n      ) {\r\n        // conseil the user to take a other photo if the supplier is unknown\r\n        const alert = await this.alertController.create({\r\n          header: 'Attention !',\r\n          message: `Le fournisseur n'a pas été reconnu !. Veuillez prendre une autre photo.`,\r\n          buttons: [\r\n            {\r\n              text: 'Non',\r\n              role: 'cancel',\r\n              cssClass: 'custom-alert-button cancel',\r\n              handler: () => {\r\n                this.selectedSupplier =\r\n                  localStorage.getItem('selectedSupplier') ?? '';\r\n                console.log('Confirm Cancel');\r\n              },\r\n            },\r\n            {\r\n              text: 'Oui',\r\n              cssClass: 'custom-alert-button danger',\r\n              handler: () => {\r\n                const index_currect =\r\n                  this.swiper_docs?.nativeElement.swiper?.activeIndex;\r\n                this.swiper_docs?.nativeElement.swiper?.removeSlide(\r\n                  index_currect\r\n                );\r\n                this.signalService.removeData(index_currect);\r\n                this.listDocs = this.signalService.getData();\r\n                this.navCtrl.navigateRoot('/scan-bl');\r\n              },\r\n            },\r\n          ],\r\n        });\r\n\r\n        // force the supplier to be global for apply the Advanced OCR 'Mindee'\r\n        this.forceSupplierGlobal = true;\r\n\r\n        // set it in the local storage\r\n        localStorage.setItem(\r\n          'forceSupplierGlobal',\r\n          this.forceSupplierGlobal.toString()\r\n        );\r\n\r\n        await alert.present();\r\n      } else {\r\n        this.selectedSupplier = '';\r\n      }\r\n    } else {\r\n      // Check if the supplier came from the backend not the same that the user selected\r\n      if (\r\n        this.data.supplier_name &&\r\n        this.data.supplier_name != 'Unknown' &&\r\n        this.data.supplier_name.toUpperCase() !=\r\n          this.selectedSupplier.toUpperCase() &&\r\n        this.listDocs.length > 0\r\n      ) {\r\n        // alert prompt for attention the user that if change supplier value for this document then all documents will be updated\r\n        const alert = await this.alertController.create({\r\n          header: 'Attention !',\r\n          message: '',\r\n          buttons: [\r\n            {\r\n              text: 'Annuler',\r\n              role: 'cancel',\r\n              cssClass: 'custom-alert-button cancel',\r\n              handler: () => {\r\n                console.log('Confirm Cancel');\r\n                // set the selected supplier to the previous value\r\n                this.selectedSupplier =\r\n                  localStorage.getItem('selectedSupplier') ?? '';\r\n              },\r\n            },\r\n            {\r\n              text: 'Oui, Mettre à jour !',\r\n              cssClass: 'custom-alert-button warning',\r\n              handler: () => {\r\n                console.log(\r\n                  'Selected Supplier backend:',\r\n                  this.data.supplier_name\r\n                );\r\n                console.log('Selected Supplier select:', this.selectedSupplier);\r\n                this.selectedSupplier = this.data.supplier_name.toUpperCase();\r\n                localStorage.setItem(\r\n                  'selectedSupplier',\r\n                  this.data.supplier_name.toUpperCase()\r\n                );\r\n                console.log('Slide data:', this.data);\r\n              },\r\n            },\r\n          ],\r\n        });\r\n        await alert.present();\r\n\r\n        // Use a query selector to set the innerHTML of the message\r\n        const alertMessage = document.querySelector('.alert-message');\r\n        if (alertMessage) {\r\n          alertMessage.innerHTML = `Le nom du fournisseur <b>'${\r\n            this.selectedSupplier\r\n          }'</b> ne correspond pas au modèle attendu <b>'${this.data.supplier_name.toUpperCase()}'</b>. Voulez-vous mettre à jour le fournisseur de ce document ?`;\r\n        }\r\n      }\r\n    }\r\n\r\n    setTimeout(() => {\r\n      this.applyFilter();\r\n\r\n      if (localStorage.getItem('selectedSupplier') != null) {\r\n        console.log(\r\n          'Selected Supplier:',\r\n          localStorage.getItem('selectedSupplier')\r\n        );\r\n        this.selectedSupplier = localStorage.getItem('selectedSupplier') ?? '';\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  async getAllSuppliers() {\r\n    // Fetch suppliers from the API\r\n    this.apiService.getAllSuppliers().subscribe(\r\n      (suppliers) => {\r\n        this.suppliers = suppliers.map((supplier: any) => {\r\n          if (supplier.value === 'GLOBAL') {\r\n            return { value: 'AUTRE', label: 'AUTRE' };\r\n          }\r\n\r\n          // Replace underscores with spaces in supplier.value\r\n          if (supplier.value.includes('_')) {\r\n            return {\r\n              ...supplier,\r\n              value: supplier.value,\r\n              label: supplier.label.replace(/_/g, ' '),\r\n            };\r\n          }\r\n\r\n          return supplier;\r\n        });\r\n\r\n        // Move \"AUTRE\" to the end\r\n        this.suppliers = this.suppliers.sort((a, b) => (a.value === 'AUTRE' ? 1 : b.value === 'AUTRE' ? -1 : 0));\r\n      },\r\n      (error) => {\r\n        console.error('Error fetching suppliers:', error);\r\n      }\r\n    );\r\n}\r\n\r\n\r\n\r\n  async redirectToScanBL() {\r\n    if (\r\n      this.signalService.getData() == null ||\r\n      this.signalService.getData() == undefined ||\r\n      this.signalService.getData().length == 0\r\n    ) {\r\n      this.navCtrl.navigateRoot('/scan-bl');\r\n    }\r\n  }\r\n\r\n  applyFilter() {\r\n    this.isScanning = !this.isScanning;\r\n    if (this.isScanning) {\r\n      setTimeout(() => {\r\n        this.hideAnimation = true;\r\n      }, 2000);\r\n      this.hideAnimation = false;\r\n    }\r\n  }\r\n\r\n  async isValidBlobUrl(blobUrl: string): Promise<boolean> {\r\n    try {\r\n      const response = await fetch(blobUrl);\r\n      return response.ok; // Returns true if the response is ok (status is in the range 200-299)\r\n    } catch (error) {\r\n      return false; // Returns false if there is an error (e.g., network issue, invalid URL)\r\n    }\r\n  }\r\n\r\n  onSlideChange() {\r\n    console.log('changedslidechange');\r\n    this.initSwipers();\r\n  }\r\n  initSwipers() {\r\n    if (this.swiper_docs && this.swiper_docs.nativeElement) {\r\n      const swiper_docsInstance = this.swiper_docs.nativeElement.swiper;\r\n    }\r\n  }\r\n\r\n  async removeDoc(index: number) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.signalService.removeData(index);\r\n            this.listDocs = this.signalService.getData();\r\n            this.swiper_docs?.nativeElement.swiper?.removeSlide(index);\r\n            if (this.signalService.getData().length == 0) {\r\n              localStorage.removeItem('selectedSupplier');\r\n              this.navCtrl.navigateRoot('/scan-bl');\r\n            }\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async removeAllDoc() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.signalService.removeAllData();\r\n            this.listDocs = [];\r\n            this.swiper_docs?.nativeElement.swiper?.removeAllSlides();\r\n            localStorage.removeItem('selectedSupplier');\r\n            // redirect to scan-bl\r\n            this.navCtrl.navigateRoot('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async reTakePhoto() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir prends une nouvelle photo ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui',\r\n          cssClass: 'custom-alert-button',\r\n          handler: () => {\r\n            const index_currect =\r\n              this.swiper_docs?.nativeElement.swiper?.activeIndex;\r\n            // remove currect slide\r\n            this.swiper_docs?.nativeElement.swiper?.removeSlide(index_currect);\r\n            // remove data\r\n            this.signalService.removeData(index_currect);\r\n            this.listDocs = this.signalService.getData();\r\n            // redirect to scan-bl\r\n            this.navCtrl.navigateRoot('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async onSupplierChange($event: any) {\r\n    console.log('Selected Supplier:', $event.detail.value);\r\n    if ($event.detail.value != localStorage.getItem('selectedSupplier')) {\r\n      // alert prompt for attention the user that if change supplier value for this document then all documents will be updated\r\n      const alert = await this.alertController.create({\r\n        header: 'Attention !',\r\n        message: `Êtes-vous sûr de vouloir changer le fournisseur ? Tous les documents seront mis à jour avec le nouveau fournisseur.`,\r\n        buttons: [\r\n          {\r\n            text: 'Annuler',\r\n            role: 'cancel',\r\n            cssClass: 'custom-alert-button cancel',\r\n            handler: () => {\r\n              console.log('Confirm Cancel');\r\n              // set the selected supplier to the previous value\r\n              this.selectedSupplier =\r\n                localStorage.getItem('selectedSupplier') ?? '';\r\n            },\r\n          },\r\n          {\r\n            text: 'Oui, Changer !',\r\n            cssClass: 'custom-alert-button warning',\r\n            handler: () => {\r\n              this.selectedSupplier = $event.detail.value;\r\n              this.listDocs.forEach((doc) => {\r\n                doc.supplier_name = $event.detail.value;\r\n              });\r\n              console.log('Slide data:', this.listDocs);\r\n              localStorage.setItem('selectedSupplier', $event.detail.value == 'AUTRE' ? 'GLOBAL' : $event.detail.value);\r\n              console.log('Selected Supplier:', $event.detail.value);\r\n            },\r\n          },\r\n        ],\r\n      });\r\n\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  async valider() {\r\n    // check if selectedSupplier not empty then redirect to  [routerLink]=\"['/doc-list']\" else show alert message\r\n    if (this.selectedSupplier != '') {\r\n      this.navCtrl.navigateRoot('/doc-list');\r\n    } else {\r\n      const alert = await this.alertController.create({\r\n        header: 'Attention !',\r\n        message: `Veuillez sélectionner un fournisseur`,\r\n        buttons: ['OK'],\r\n      });\r\n      await alert.present();\r\n    }\r\n  }\r\n\r\n  compressImage(file: File): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.onload = (event) => {\r\n        const img = new Image();\r\n        img.src = event.target?.result as string;\r\n        img.onload = () => {\r\n          const canvas = document.createElement('canvas');\r\n          const ctx = canvas.getContext('2d');\r\n          const MAX_WIDTH = 1024;\r\n          const MAX_HEIGHT = 1024;\r\n          let width = img.width;\r\n          let height = img.height;\r\n\r\n          if (width > height && width > MAX_WIDTH) {\r\n            height = (height * MAX_WIDTH) / width;\r\n            width = MAX_WIDTH;\r\n          } else if (height > width && height > MAX_HEIGHT) {\r\n            width = (width * MAX_HEIGHT) / height;\r\n            height = MAX_HEIGHT;\r\n          }\r\n          canvas.width = width;\r\n          canvas.height = height;\r\n          ctx?.drawImage(img, 0, 0, width, height);\r\n          resolve(canvas.toDataURL('image/jpeg', 0.8));\r\n        };\r\n      };\r\n      reader.onerror = reject;\r\n      reader.readAsDataURL(file);\r\n    });\r\n  }\r\n\r\n  preloadImage(src: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const img = new Image();\r\n      img.src = src;\r\n      img.onload = () => resolve();\r\n      img.onerror = (err) => reject(err);\r\n    });\r\n  }\r\n\r\n  async showFilteredImage(item: ProcessDocData) {\r\n    try {\r\n      if (item.filtered_image) {\r\n        await this.preloadImage(item.filtered_image);\r\n        this.isScanning = true;\r\n      } else {\r\n        console.error(\"L'image filtrée n'est pas définie\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Impossible de charger l'image filtrée\", error);\r\n    }\r\n  }\r\n\r\n  onFilteredImageLoad() {\r\n    this.filteredImageLoaded = true;\r\n  }\r\n\r\n  cacheImage(key: string, imageUrl: string) {\r\n    localStorage.setItem(key, imageUrl);\r\n  }\r\n\r\n  getCachedImage(key: string): string | null {\r\n    return localStorage.getItem(key);\r\n  }\r\n\r\n  \r\n  scan_bl() {\r\n    this.navCtrl.navigateRoot('/scan-bl');  \r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title>Scanner votre BL</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"scan_bl()\">\r\n        <app-custom-icon name=\"re-scan\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"process-doc-content\">\r\n  \r\n  <div class=\"select-supplier-wrapper\">\r\n    <ion-select\r\n      [(ngModel)]=\"selectedSupplier\"\r\n      label=\"Sélection du fournisseur\"\r\n      label-placement=\"floating\"\r\n      fill=\"outline\"\r\n      (ionChange)=\"onSupplierChange($event)\"\r\n    >\r\n      <ion-select-option\r\n        *ngFor=\"let option of suppliers\"\r\n        [value]=\"option.value\"\r\n        >{{ option.label }}</ion-select-option\r\n      >\r\n    </ion-select>\r\n  </div>\r\n\r\n  <swiper-container\r\n    effect=\"coverflow\"\r\n    grab-cursor=\"true\"\r\n    centered-slides=\"false\"\r\n    slides-per-view=\"auto\"\r\n    coverflow-effect-rotate=\"50\"\r\n    coverflow-effect-stretch=\"0\"\r\n    coverflow-effect-depth=\"100\"\r\n    coverflow-effect-modifier=\"1\"\r\n    coverflow-effect-slide-shadows=\"true\"\r\n    class=\"swiper top-swiper\"\r\n    #swiper_docs\r\n    (swiperslidechange)=\"onSlideChange()\"\r\n  >\r\n    <swiper-slide *ngFor=\"let item of listDocs; let i = index\" class=\"swiper-slide\">\r\n      <div class=\"process-doc-wrapper\">\r\n\r\n        <div class=\"remove-doc\" (click)=\"removeDoc(i)\">\r\n          <ion-icon name=\"close-circle-sharp\"></ion-icon>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <img id=\"unfilteredImage\" [src]=\"item.cropped_image\" alt=\"Unfiltered Document\" loading=\"lazy\" />\r\n\r\n          <div\r\n            [ngClass]=\"{'filter-animation': true, 'd-block-img': isScanning, 'hide-animation': hideAnimation, 'scanning': isScanning}\"\r\n          >\r\n            <div class=\"scan-line\" *ngIf=\"isScanning\"></div>\r\n            <div *ngIf=\"!filteredImageLoaded\" class=\"loading-placeholder\">\r\n              <ion-spinner></ion-spinner>\r\n            </div>\r\n            <img\r\n              [src]=\"item.filtered_image\"\r\n              alt=\"Filtered Document\"\r\n              [hidden]=\"!filteredImageLoaded\"\r\n              (load)=\"onFilteredImageLoad()\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </swiper-slide>\r\n    <swiper-slide class=\"swiper-slide last-swiper-slide-scan\" [routerLink]=\"['/scan-bl']\">\r\n      <div class=\"scan-bl-wrapper\">\r\n        <app-custom-icon name=\"file-import\" class=\"file-import-icon\"></app-custom-icon>\r\n        <div class=\"content\">\r\n          <h2>Ajouter une autre page</h2>\r\n          <p>La page doit être un même Bon Livraison</p>\r\n        </div>\r\n      </div>\r\n    </swiper-slide>\r\n  </swiper-container>\r\n</ion-content>\r\n\r\n<ion-footer>\r\n  <ion-toolbar>\r\n    <ion-buttons>\r\n      <ion-button class=\"menu-button\" size=\"small\" (click)=\"removeAllDoc()\">\r\n        <app-custom-icon name=\"delete\"></app-custom-icon>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button-middle\"(click)=\"valider()\">\r\n        <app-custom-icon name=\"extract\"></app-custom-icon>\r\n        <span>VALIDER</span>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button active\" size=\"small\" (click)=\"reTakePhoto()\">\r\n        <app-custom-icon name=\"re-scan-2\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-footer>\r\n"], "names": ["RouterModule", "ProcessDocPage", "routes", "path", "component", "ProcessDocPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "ProcessDocPageModule", "declarations", "inject", "NavController", "SignalService", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r2", "value", "ɵɵadvance", "ɵɵtextInterpolate", "label", "ɵɵelement", "ɵɵlistener", "ProcessDocPage_swiper_slide_13_Template_div_click_2_listener", "i_r4", "ɵɵrestoreView", "_r3", "index", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "removeDoc", "ɵɵtemplate", "ProcessDocPage_swiper_slide_13_div_7_Template", "ProcessDocPage_swiper_slide_13_div_8_Template", "ProcessDocPage_swiper_slide_13_Template_img_load_9_listener", "onFilteredImageLoad", "item_r6", "cropped_image", "ɵɵsanitizeUrl", "ɵɵpureFunction3", "_c2", "isScanning", "hideAnimation", "filteredImageLoaded", "filtered_image", "constructor", "route", "location", "alertController", "cd", "apiService", "selectedSupplier", "croppedImage", "filteredImage", "forceSupplierGlobal", "suppliers", "listDocs", "navCtrl", "signalService", "swiperClass", "platform", "ngOnInit", "_this", "_asyncToGenerator", "_localStorage$getItem", "redirectToScanBL", "for<PERSON>ach", "supplier", "params", "getState", "data", "console", "log", "setTimeout", "getAllSuppliers", "getData", "localStorage", "getItem", "detectChanges", "firstEntry", "supplier_name", "toUpperCase", "setItem", "alert", "create", "header", "message", "buttons", "text", "role", "cssClass", "handler", "_localStorage$getItem2", "_this$swiper_docs", "_this$swiper_docs2", "index_currect", "swiper_docs", "nativeElement", "swiper", "activeIndex", "removeSlide", "removeData", "navigateRoot", "toString", "present", "length", "_localStorage$getItem3", "alertMessage", "document", "querySelector", "innerHTML", "applyFilter", "_localStorage$getItem4", "_this2", "subscribe", "map", "includes", "replace", "sort", "a", "b", "error", "_this3", "undefined", "isValidBlobUrl", "blobUrl", "response", "fetch", "ok", "onSlideChange", "initSwipers", "swiper_docsInstance", "_this4", "_this4$swiper_docs", "removeItem", "removeAllDoc", "_this5", "_this5$swiper_docs", "removeAllData", "removeAllSlides", "reTakePhoto", "_this6", "_this6$swiper_docs", "_this6$swiper_docs2", "onSupplierChange", "$event", "_this7", "detail", "_localStorage$getItem5", "doc", "valider", "_this8", "compressImage", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "event", "_event$target", "img", "Image", "src", "target", "result", "canvas", "createElement", "ctx", "getContext", "MAX_WIDTH", "MAX_HEIGHT", "width", "height", "drawImage", "toDataURL", "onerror", "readAsDataURL", "preloadImage", "err", "showFilteredImage", "item", "_this9", "cacheImage", "key", "imageUrl", "getCachedImage", "scan_bl", "ɵɵdirectiveInject", "ActivatedRoute", "i2", "Location", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChangeDetectorRef", "i4", "ApiService", "selectors", "viewQuery", "ProcessDocPage_Query", "rf", "ProcessDocPage_Template_ion_button_click_5_listener", "_r1", "ɵɵtwoWayListener", "ProcessDocPage_Template_ion_select_ngModelChange_9_listener", "ɵɵtwoWayBindingSet", "ProcessDocPage_Template_ion_select_ionChange_9_listener", "ProcessDocPage_ion_select_option_10_Template", "ProcessDocPage_Template_swiper_container_swiperslidechange_11_listener", "ProcessDocPage_swiper_slide_13_Template", "ProcessDocPage_Template_ion_button_click_25_listener", "ProcessDocPage_Template_ion_button_click_27_listener", "ProcessDocPage_Template_ion_button_click_31_listener", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c1"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}