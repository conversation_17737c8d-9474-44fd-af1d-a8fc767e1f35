{"version": 3, "file": "common.js", "mappings": ";;;;;;;;;;;;;;;AAA6D;;AAQvD,MAAOE,aAAa;EAH1BC,YAAA;IAIU,KAAAC,IAAI,GAAGJ,qDAAM,CAAmB,EAAE,CAAC;IACnC,KAAAK,SAAS,GAAGL,qDAAM,CAAmB,EAAE,CAAC;IACxC,KAAAM,eAAe,GAAGN,qDAAM,CAAuB,EAAE,CAAC;IAE1D;IACiB,KAAAO,OAAO,GAAGN,uDAAQ,CAAC,MAAM,IAAI,CAACG,IAAI,EAAE,CAACI,MAAM,KAAK,CAAC,CAAC;;EAEnE;EAEAC,OAAOA,CAACC,OAAuB;IAC7BA,OAAO,CAACC,KAAK,GAAG,QAAQ,IAAI,CAACC,UAAU,EAAE,EAAE;IAE3C,IAAI,CAACR,IAAI,CAACS,MAAM,CAAET,IAAI,IAAI;MACxB,MAAMU,KAAK,GAAGV,IAAI,CAACI,MAAM;MACzBE,OAAO,CAACK,UAAU,GAAGD,KAAK,GAAG,CAAC;MAC9B,MAAME,mBAAmB,GAAG,IAAI,CAACC,cAAc,EAAE;MACjDb,IAAI,CAACc,IAAI,CAAC;QAAE,GAAGR,OAAO;QAAES,IAAI,EAAEH;MAAmB,CAAE,CAAC;MAEpD;MACA,IAAIZ,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;QACrBY,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;;MAGhD,OAAOjB,IAAI;IACb,CAAC,CAAC;EACJ;EAEAkB,OAAOA,CAAA;IACL,MAAMC,WAAW,GAAG,IAAI,CAACnB,IAAI,EAAE;IAC/B;IACA,IAAImB,WAAW,CAACf,MAAM,KAAK,CAAC,EAAE;MAC5BY,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;;IAEhD,OAAOE,WAAW;EACpB;EAGAC,eAAeA,CAAA;IACb,IAAI,CAACpB,IAAI,CAACS,MAAM,CAAET,IAAI,IAAI;MACxB;MACA,MAAMqB,cAAc,GAAGrB,IAAI,CAACI,MAAM;MAElC;MACAJ,IAAI,CAACsB,GAAG,EAAE;MAEV;MACA,IAAID,cAAc,KAAK,CAAC,EAAE;QACxBL,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;;MAEhD,OAAOjB,IAAI;IACb,CAAC,CAAC;EACJ;EAGAuB,UAAUA,CAACb,KAAa;IACtB,IAAI,CAACV,IAAI,CAACS,MAAM,CAAET,IAAI,IAAI;MACxB;MACA,MAAMqB,cAAc,GAAGrB,IAAI,CAACI,MAAM;MAElC;MACAJ,IAAI,CAACwB,MAAM,CAACd,KAAK,EAAE,CAAC,CAAC;MAErB;MACA,IAAIW,cAAc,KAAK,CAAC,EAAE;QACxBL,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;;MAGhD;MACAjB,IAAI,CAACyB,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAMD,IAAI,CAACf,UAAU,GAAGgB,CAAC,GAAG,CAAE,CAAC;MAEpD,OAAO3B,IAAI;IACb,CAAC,CAAC;EACJ;EAEA4B,gBAAgBA,CAAA;IACd,IAAI,CAAC5B,IAAI,CAACS,MAAM,CAAET,IAAI,IAAI;MACxB,IAAIA,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;QACrBY,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;QAC9C,OAAOjB,IAAI;;MAEb,OAAOA,IAAI;IACb,CAAC,CAAC;EACJ;EAEA6B,aAAaA,CAAA;IACX,IAAI,CAAC7B,IAAI,CAACS,MAAM,CAAET,IAAI,IAAI;MACxBA,IAAI,GAAG,EAAE;MACT,OAAOA,IAAI;IACb,CAAC,CAAC;IACFgB,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;EAChD;EAEAa,gBAAgBA,CAAA;IACd;IACA,MAAMC,WAAW,GAAG,IAAIC,IAAI,EAAE;IAE9B;IACA,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEzG,MAAMC,aAAa,GAAG,GAAGF,UAAU,CAACF,WAAW,CAACK,MAAM,EAAE,CAAC,IAAIF,YAAY,CAACH,WAAW,CAACM,QAAQ,EAAE,CAAC,EAAE;IACnG,OAAOF,aAAa;EACtB;EAEAtB,cAAcA,CAAA;IACZ;IACA,MAAMyB,WAAW,GAAG,IAAIN,IAAI,EAAE;IAC9B;IACA,MAAMD,WAAW,GAAGO,WAAW;IAE/B;IACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,WAAW,CAACI,OAAO,EAAE,GAAGX,WAAW,CAACW,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEtG,IAAIP,aAAa;IACjB,IAAII,UAAU,KAAK,CAAC,EAAE;MACpB;MACAJ,aAAa,GAAG,aAAa;KAC9B,MAAM,IAAII,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAJ,aAAa,GAAG,MAAM;KACvB,MAAM;MACL;MACA;MACAA,aAAa,GAAG,GAAGJ,WAAW,CAACY,OAAO,EAAE,IAAIZ,WAAW,CAACM,QAAQ,EAAE,GAAG,CAAC,EAAE;;IAG1E,OAAOF,aAAa;EACtB;EAEA3B,UAAUA,CAAA;IACR,MAAMO,IAAI,GAAG,IAAIiB,IAAI,EAAE;IACvB,MAAMY,GAAG,GAAG,IAAI,CAACC,SAAS,CAAC9B,IAAI,CAAC4B,OAAO,EAAE,CAAC;IAC1C,MAAMG,KAAK,GAAG,IAAI,CAACD,SAAS,CAAC9B,IAAI,CAACsB,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACnD,MAAMU,IAAI,GAAGhC,IAAI,CAACiC,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACJ,SAAS,CAAC9B,IAAI,CAACmC,QAAQ,EAAE,CAAC;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACN,SAAS,CAAC9B,IAAI,CAACqC,UAAU,EAAE,CAAC;IACjD,MAAMC,OAAO,GAAG,IAAI,CAACR,SAAS,CAAC9B,IAAI,CAACuC,UAAU,EAAE,CAAC;IACjD,OAAO,GAAGV,GAAG,IAAIE,KAAK,IAAIC,IAAI,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;EACjE;EAEAR,SAASA,CAACU,GAAW;IACnB,OAAOA,GAAG,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACxC;EAEA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAC1D,IAAI,EAAE,CAACI,MAAM,KAAK,CAAC;EACjC;EAEQuD,mBAAmBA,CAACC,GAAW;IACrC,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB,MAAMC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IAC5B,OAAOD,KAAK,CAACA,KAAK,CAACzD,MAAM,GAAG,CAAC,CAAC,CAAC0D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;EAEAC,gBAAgBA,CAACC,OAAe;IAC9B,MAAM1B,WAAW,GAAG,IAAIN,IAAI,EAAE;IAC9B,MAAMjB,IAAI,GAAG,IAAIiB,IAAI,CAACgC,OAAO,CAAC;IAC9B,MAAMzB,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,WAAW,CAACI,OAAO,EAAE,GAAG3B,IAAI,CAAC2B,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/F,IAAIH,UAAU,KAAK,CAAC,EAAE;MACpB,OAAO,aAAa;KACrB,MAAM,IAAIA,UAAU,KAAK,CAAC,EAAE;MAC3B,OAAO,MAAM;KACd,MAAM;MACL,OAAO,GAAGxB,IAAI,CAAC4B,OAAO,EAAE,IAAI5B,IAAI,CAACsB,QAAQ,EAAE,GAAG,CAAC,EAAE;;EAErD;EAEA;EAEA4B,YAAYA,CAAC3D,OAAkB;IAC7B,IAAI,CAACL,SAAS,CAACQ,MAAM,CAAER,SAAS,IAAI;MAClC,MAAMS,KAAK,GAAGT,SAAS,CAACG,MAAM;MAC9BE,OAAO,CAACK,UAAU,GAAGD,KAAK,GAAG,CAAC;MAC9B,OAAOT,SAAS;IAClB,CAAC,CAAC;EACJ;EAEAiE,YAAYA,CAAA;IACV,OAAO,IAAI,CAACjE,SAAS,EAAE;EACzB;EAEA;EAEAkE,mBAAmBA,CAACnE,IAAS;IAC3B,IAAI,CAACE,eAAe,CAACkE,GAAG,CAACpE,IAAI,CAAC;EAChC;EAEAqE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACnE,eAAe,EAAE;EAC/B;;iBA/LWJ,aAAa;;mBAAbA,cAAa;AAAA;;SAAbA,cAAa;EAAAwE,OAAA,EAAbxE,cAAa,CAAAyE,IAAA;EAAAC,UAAA,EAFZ;AAAM;;;;;;;;;;;;;;;;;ACNpB;AACA;AACA;AACqD;AACkE;AACnE;AAEpD,MAAMU,yBAAyB,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;EAChD,IAAIC,oBAAoB;EACxB,IAAIC,oBAAoB;EACxB,MAAMC,qBAAqB,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,gBAAgB,KAAK;IACtD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC;IACJ;IACA,MAAMC,MAAM,GAAGD,QAAQ,CAACE,gBAAgB,CAACL,CAAC,EAAEC,CAAC,CAAC;IAC9C,IAAI,CAACG,MAAM,IAAI,CAACR,QAAQ,CAACQ,MAAM,CAAC,IAAIA,MAAM,CAACE,QAAQ,EAAE;MACjDC,iBAAiB,CAAC,CAAC;MACnB;IACJ;IACA,IAAIH,MAAM,KAAKP,oBAAoB,EAAE;MACjCU,iBAAiB,CAAC,CAAC;MACnBC,eAAe,CAACJ,MAAM,EAAEF,gBAAgB,CAAC;IAC7C;EACJ,CAAC;EACD,MAAMM,eAAe,GAAGA,CAACC,MAAM,EAAEP,gBAAgB,KAAK;IAClDL,oBAAoB,GAAGY,MAAM;IAC7B,IAAI,CAACX,oBAAoB,EAAE;MACvBA,oBAAoB,GAAGD,oBAAoB;IAC/C;IACA,MAAMa,cAAc,GAAGb,oBAAoB;IAC3CX,qDAAS,CAAC,MAAMwB,cAAc,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9DV,gBAAgB,CAAC,CAAC;EACtB,CAAC;EACD,MAAMK,iBAAiB,GAAGA,CAACM,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI,CAAChB,oBAAoB,EAAE;MACvB;IACJ;IACA,MAAMa,cAAc,GAAGb,oBAAoB;IAC3CX,qDAAS,CAAC,MAAMwB,cAAc,CAACC,SAAS,CAACG,MAAM,CAAC,eAAe,CAAC,CAAC;IACjE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAID,aAAa,IAAIf,oBAAoB,KAAKD,oBAAoB,EAAE;MAChEA,oBAAoB,CAACkB,KAAK,CAAC,CAAC;IAChC;IACAlB,oBAAoB,GAAGmB,SAAS;EACpC,CAAC;EACD,OAAOvB,iEAAa,CAAC;IACjBE,EAAE;IACFsB,WAAW,EAAE,kBAAkB;IAC/BC,SAAS,EAAE,CAAC;IACZC,OAAO,EAAGC,EAAE,IAAKrB,qBAAqB,CAACqB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAEhC,kDAAoB,CAAC;IACtFiC,MAAM,EAAGH,EAAE,IAAKrB,qBAAqB,CAACqB,EAAE,CAACC,QAAQ,EAAED,EAAE,CAACE,QAAQ,EAAE9B,kDAAsB,CAAC;IACvFgC,KAAK,EAAEA,CAAA,KAAM;MACTjB,iBAAiB,CAAC,IAAI,CAAC;MACvBnB,sDAAkB,CAAC,CAAC;MACpBU,oBAAoB,GAAGkB,SAAS;IACpC;EACJ,CAAC,CAAC;AACN,CAAC;;;;;;;;;;;;;;;;AChED;AACA;AACA;AAC+C;AAE/C,MAAMW,YAAY,GAAGA,CAAA,KAAM;EACvB,IAAID,iDAAG,KAAKV,SAAS,EAAE;IACnB,OAAOU,iDAAG,CAACE,SAAS;EACxB;EACA,OAAOZ,SAAS;AACpB,CAAC;;;;;;;;;;;;;;;;ACVD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,cAAc,GAAGA,CAACC,YAAY,EAAEC,YAAY,EAAEC,WAAW,KAAK;EAChE,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IACnC,OAAOA,WAAW,CAACF,YAAY,EAAEC,YAAY,CAAC;EAClD,CAAC,MACI,IAAI,OAAOC,WAAW,KAAK,QAAQ,EAAE;IACtC,OAAOF,YAAY,CAACE,WAAW,CAAC,KAAKD,YAAY,CAACC,WAAW,CAAC;EAClE,CAAC,MACI;IACD,OAAOC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,CAACI,QAAQ,CAACL,YAAY,CAAC,GAAGA,YAAY,KAAKC,YAAY;EAC5G;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,gBAAgB,GAAGA,CAACN,YAAY,EAAEC,YAAY,EAAEC,WAAW,KAAK;EAClE,IAAIF,YAAY,KAAKf,SAAS,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIkB,KAAK,CAACC,OAAO,CAACJ,YAAY,CAAC,EAAE;IAC7B,OAAOA,YAAY,CAACO,IAAI,CAAEC,GAAG,IAAKT,cAAc,CAACS,GAAG,EAAEP,YAAY,EAAEC,WAAW,CAAC,CAAC;EACrF,CAAC,MACI;IACD,OAAOH,cAAc,CAACC,YAAY,EAAEC,YAAY,EAAEC,WAAW,CAAC;EAClE;AACJ,CAAC;;;;;;;;;;;;;;;ACtCD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,KAAK,GAAIC,MAAM,IAAK;EACtB,IAAIA,MAAM,EAAE;IACR,IAAIA,MAAM,CAACC,GAAG,KAAK,EAAE,EAAE;MACnB,OAAOD,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;IAC7C;EACJ;EACA,OAAO,CAACxC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACuC,GAAG,CAACC,WAAW,CAAC,CAAC,MAAM,KAAK;AACrG,CAAC;;;;;;;;;;;;;;;ACfD;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,aAAa,GAAG,eAAe;AACrC,MAAMC,UAAU,GAAG,CACf,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,GAAG,EACH,OAAO,EACP,OAAO,EACP,WAAW,EACX,YAAY,EACZ,SAAS,EACT,MAAM,EACN,KAAK,CACR;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,YAAY,GAAG,IAAI;EACvB,MAAMC,GAAG,GAAGH,MAAM,GAAGA,MAAM,CAACI,UAAU,GAAGjD,QAAQ;EACjD,MAAMkD,IAAI,GAAGL,MAAM,GAAGA,MAAM,GAAG7C,QAAQ,CAACmD,IAAI;EAC5C,MAAMC,QAAQ,GAAIC,QAAQ,IAAK;IAC3BP,YAAY,CAAChH,OAAO,CAAE0D,EAAE,IAAKA,EAAE,CAACgB,SAAS,CAACG,MAAM,CAAC8B,WAAW,CAAC,CAAC;IAC9DY,QAAQ,CAACvH,OAAO,CAAE0D,EAAE,IAAKA,EAAE,CAACgB,SAAS,CAACC,GAAG,CAACgC,WAAW,CAAC,CAAC;IACvDK,YAAY,GAAGO,QAAQ;EAC3B,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtBP,YAAY,GAAG,KAAK;IACpBK,QAAQ,CAAC,EAAE,CAAC;EAChB,CAAC;EACD,MAAMG,SAAS,GAAItC,EAAE,IAAK;IACtB8B,YAAY,GAAGJ,UAAU,CAACV,QAAQ,CAAChB,EAAE,CAACuC,GAAG,CAAC;IAC1C,IAAI,CAACT,YAAY,EAAE;MACfK,QAAQ,CAAC,EAAE,CAAC;IAChB;EACJ,CAAC;EACD,MAAMK,SAAS,GAAIxC,EAAE,IAAK;IACtB,IAAI8B,YAAY,IAAI9B,EAAE,CAACyC,YAAY,KAAK7C,SAAS,EAAE;MAC/C,MAAM8C,OAAO,GAAG1C,EAAE,CAACyC,YAAY,CAAC,CAAC,CAACE,MAAM,CAAEpE,EAAE,IAAK;QAC7C;QACA,IAAIA,EAAE,CAACgB,SAAS,EAAE;UACd,OAAOhB,EAAE,CAACgB,SAAS,CAACqD,QAAQ,CAACnB,aAAa,CAAC;QAC/C;QACA,OAAO,KAAK;MAChB,CAAC,CAAC;MACFU,QAAQ,CAACO,OAAO,CAAC;IACrB;EACJ,CAAC;EACD,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAId,GAAG,CAACe,aAAa,KAAKb,IAAI,EAAE;MAC5BE,QAAQ,CAAC,EAAE,CAAC;IAChB;EACJ,CAAC;EACDJ,GAAG,CAACgB,gBAAgB,CAAC,SAAS,EAAET,SAAS,CAAC;EAC1CP,GAAG,CAACgB,gBAAgB,CAAC,SAAS,EAAEP,SAAS,CAAC;EAC1CT,GAAG,CAACgB,gBAAgB,CAAC,UAAU,EAAEF,UAAU,CAAC;EAC5Cd,GAAG,CAACgB,gBAAgB,CAAC,YAAY,EAAEV,WAAW,EAAE;IAAEW,OAAO,EAAE;EAAK,CAAC,CAAC;EAClEjB,GAAG,CAACgB,gBAAgB,CAAC,WAAW,EAAEV,WAAW,CAAC;EAC9C,MAAMY,OAAO,GAAGA,CAAA,KAAM;IAClBlB,GAAG,CAACmB,mBAAmB,CAAC,SAAS,EAAEZ,SAAS,CAAC;IAC7CP,GAAG,CAACmB,mBAAmB,CAAC,SAAS,EAAEV,SAAS,CAAC;IAC7CT,GAAG,CAACmB,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAAC;IAC/Cd,GAAG,CAACmB,mBAAmB,CAAC,YAAY,EAAEb,WAAW,CAAC;IAClDN,GAAG,CAACmB,mBAAmB,CAAC,WAAW,EAAEb,WAAW,CAAC;EACrD,CAAC;EACD,OAAO;IACHY,OAAO;IACPd;EACJ,CAAC;AACL,CAAC;;;;;;;;;;;;;;;;;;;;;ACxED;AACA;AACA;AAC4D;AAE5D,IAAIgB,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;AACJ;AACA;AACA;AACA;EACIA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzB;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvC;AACJ;AACA;AACA;AACA;EACIA,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACvC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,YAAY,GAAG;EACjBC,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAGhD,yDAAY,CAAC,CAAC;IAChC,IAAIgD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,SAAS,CAAC,EAAE;MAC9F;MACA,OAAOD,SAAS,CAACE,OAAO,CAACC,OAAO;IACpC;IACA,OAAO9D,SAAS;EACpB,CAAC;EACD+D,SAASA,CAAA,EAAG;IACR,MAAMC,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT,OAAO,KAAK;IAChB;IACA,MAAML,SAAS,GAAGhD,yDAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE;MAC3F;MACA,OAAO,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAKnE,SAAS;IAC9E;IACA,OAAO,IAAI;EACf,CAAC;EACDoE,MAAMA,CAACC,OAAO,EAAE;IACZ,MAAML,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACI,MAAM,CAAC;MAAEE,KAAK,EAAED,OAAO,CAACC;IAAM,CAAC,CAAC;EAC3C,CAAC;EACDC,YAAYA,CAACF,OAAO,EAAE;IAClB,MAAML,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACO,YAAY,CAAC;MAAEC,IAAI,EAAEH,OAAO,CAACG;IAAK,CAAC,CAAC;EAC/C,CAAC;EACDC,SAASA,CAAA,EAAG;IACR,IAAI,CAACL,MAAM,CAAC;MAAEE,KAAK,EAAEf,WAAW,CAACmB;IAAM,CAAC,CAAC;EAC7C,CAAC;EACDC,cAAcA,CAAA,EAAG;IACb,MAAMX,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACW,cAAc,CAAC,CAAC;EAC3B,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACf,MAAMZ,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACY,gBAAgB,CAAC,CAAC;EAC7B,CAAC;EACDC,YAAYA,CAAA,EAAG;IACX,MAAMb,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACM,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACa,YAAY,CAAC,CAAC;EACzB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,OAAOrB,YAAY,CAACM,SAAS,CAAC,CAAC;AACnC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;EAC1BD,eAAe,CAAC,CAAC,IAAIrB,YAAY,CAACgB,SAAS,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMnG,oBAAoB,GAAGA,CAAA,KAAM;EAC/BwG,eAAe,CAAC,CAAC,IAAIrB,YAAY,CAACkB,cAAc,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA,MAAMnG,sBAAsB,GAAGA,CAAA,KAAM;EACjCsG,eAAe,CAAC,CAAC,IAAIrB,YAAY,CAACmB,gBAAgB,CAAC,CAAC;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMxG,kBAAkB,GAAGA,CAAA,KAAM;EAC7B0G,eAAe,CAAC,CAAC,IAAIrB,YAAY,CAACoB,YAAY,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,YAAY,GAAIX,OAAO,IAAK;EAC9BS,eAAe,CAAC,CAAC,IAAIrB,YAAY,CAACW,MAAM,CAACC,OAAO,CAAC;AACrD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3JD;AACA;AACA;AAC8D;AACO;AAErE,MAAMgB,oBAAoB,GAAG,aAAa;AAC1C,MAAMC,4BAA4B,GAAG,aAAa;AAClD,MAAMC,0BAA0B,GAAG,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAI,GAAEF,4BAA6B,KAAIC,0BAA2B,EAAC;AAC7F,MAAME,YAAY,GAAI9G,EAAE,IAAKA,EAAE,CAAC+G,OAAO,KAAKL,oBAAoB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,gBAAgB;EAAA,IAAAC,IAAA,GAAAC,6OAAA,CAAG,WAAOlH,EAAE,EAAK;IACnC,IAAI8G,YAAY,CAAC9G,EAAE,CAAC,EAAE;MAClB,MAAM,IAAImH,OAAO,CAAEC,OAAO,IAAKZ,uDAAgB,CAACxG,EAAE,EAAEoH,OAAO,CAAC,CAAC;MAC7D,OAAOpH,EAAE,CAACgH,gBAAgB,CAAC,CAAC;IAChC;IACA,OAAOhH,EAAE;EACb,CAAC;EAAA,gBANKgH,gBAAgBA,CAAAK,EAAA;IAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;EAAA;AAAA,GAMrB;AACD;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIxH,EAAE,IAAK;EAC3B;AACJ;AACA;AACA;AACA;EACI,MAAMyH,iBAAiB,GAAGzH,EAAE,CAAC0H,aAAa,CAACd,0BAA0B,CAAC;EACtE,IAAIa,iBAAiB,EAAE;IACnB,OAAOA,iBAAiB;EAC5B;EACA,OAAOzH,EAAE,CAAC0H,aAAa,CAACb,oBAAoB,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMc,qBAAqB,GAAI3H,EAAE,IAAK;EAClC,OAAOA,EAAE,CAAC4H,OAAO,CAACf,oBAAoB,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMgB,WAAW,GAAGA,CAAC7H,EAAE,EAAE8H,UAAU,KAAK;EACpC,IAAIhB,YAAY,CAAC9G,EAAE,CAAC,EAAE;IAClB,MAAM+H,OAAO,GAAG/H,EAAE;IAClB,OAAO+H,OAAO,CAACF,WAAW,CAACC,UAAU,CAAC;EAC1C;EACA,OAAOX,OAAO,CAACC,OAAO,CAACpH,EAAE,CAACgI,QAAQ,CAAC;IAC/BC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAEL,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG;EAC1C,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAGA,CAACpI,EAAE,EAAEK,CAAC,EAAEC,CAAC,EAAEwH,UAAU,KAAK;EAC5C,IAAIhB,YAAY,CAAC9G,EAAE,CAAC,EAAE;IAClB,MAAM+H,OAAO,GAAG/H,EAAE;IAClB,OAAO+H,OAAO,CAACK,aAAa,CAAC/H,CAAC,EAAEC,CAAC,EAAEwH,UAAU,CAAC;EAClD;EACA,OAAOX,OAAO,CAACC,OAAO,CAACpH,EAAE,CAACqI,QAAQ,CAAC;IAC/BJ,GAAG,EAAE3H,CAAC;IACN4H,IAAI,EAAE7H,CAAC;IACP8H,QAAQ,EAAEL,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG;EAC1C,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,GAAItI,EAAE,IAAK;EACpC,OAAOyG,qDAAyB,CAACzG,EAAE,EAAE2G,4BAA4B,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM4B,qBAAqB,GAAIC,SAAS,IAAK;EACzC,IAAI1B,YAAY,CAAC0B,SAAS,CAAC,EAAE;IACzB,MAAMC,UAAU,GAAGD,SAAS;IAC5B,MAAME,cAAc,GAAGD,UAAU,CAACE,OAAO;IACzCF,UAAU,CAACE,OAAO,GAAG,KAAK;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAOD,cAAc;EACzB,CAAC,MACI;IACDF,SAAS,CAAC7C,KAAK,CAACiD,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC;IACjD,OAAO,IAAI;EACf;AACJ,CAAC;AACD,MAAMC,mBAAmB,GAAGA,CAACL,SAAS,EAAEE,cAAc,KAAK;EACvD,IAAI5B,YAAY,CAAC0B,SAAS,CAAC,EAAE;IACzBA,SAAS,CAACG,OAAO,GAAGD,cAAc;EACtC,CAAC,MACI;IACDF,SAAS,CAAC7C,KAAK,CAACmD,cAAc,CAAC,UAAU,CAAC;EAC9C;AACJ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7HD;AACA;AACA;AACA;AACA,MAAMK,cAAc,GAAG,yPAAyP;AAChR,MAAMC,SAAS,GAAG,yPAAyP;AAC3Q,MAAMC,cAAc,GAAG,qJAAqJ;AAC5K,MAAMC,cAAc,GAAG,qJAAqJ;AAC5K,MAAMC,YAAY,GAAG,sJAAsJ;AAC3K,MAAMC,gBAAgB,GAAG,+OAA+O;AACxQ,MAAMC,WAAW,GAAG,6OAA6O;AACjQ,MAAMC,WAAW,GAAG,6OAA6O;AACjQ,MAAMC,aAAa,GAAG,qQAAqQ;AAC3R,MAAMC,cAAc,GAAG,6OAA6O;AACpQ,MAAMC,qBAAqB,GAAG,6OAA6O;AAC3Q,MAAMC,KAAK,GAAG,oPAAoP;AAClQ,MAAMC,WAAW,GAAG,4YAA4Y;AACha,MAAMC,UAAU,GAAG,0QAA0Q;AAC7R,MAAMC,cAAc,GAAG,+OAA+O;AACtQ,MAAMC,kBAAkB,GAAG,mNAAmN;AAC9O,MAAMC,GAAG,GAAG,6hBAA6hB;AACziB,MAAMC,MAAM,GAAG,49BAA49B;AAC3+B,MAAMC,WAAW,GAAG,wPAAwP;AAC5Q,MAAMC,SAAS,GAAG,8LAA8L;AAChN,MAAMC,aAAa,GAAG,oOAAoO;AAC1P,MAAMC,mBAAmB,GAAG,yPAAyP;AACrR,MAAMC,eAAe,GAAG,8OAA8O;AACtQ,MAAMC,aAAa,GAAG,oYAAoY;AAC1Z,MAAMC,WAAW,GAAG,mXAAmX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5BvY;AACA;AACA;AACuD;AACtB;AACJ;AAE7B,MAAMc,iBAAiB,GAAG,oBAAoB;AAC9C,MAAMC,kBAAkB,GAAG,oBAAoB;AAC/C,MAAMC,kBAAkB,GAAG,GAAG;AAC9B;AACA,IAAIC,sBAAsB,GAAG,CAAC,CAAC;AAC/B,IAAIC,qBAAqB,GAAG,CAAC,CAAC;AAC9B,IAAIC,YAAY,GAAG,KAAK;AACxB;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAC9BH,sBAAsB,GAAG,CAAC,CAAC;EAC3BC,qBAAqB,GAAG,CAAC,CAAC;EAC1BC,YAAY,GAAG,KAAK;AACxB,CAAC;AACD,MAAME,mBAAmB,GAAIjK,GAAG,IAAK;EACjC,MAAMkK,YAAY,GAAGT,oDAAQ,CAACzG,SAAS,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIkH,YAAY,EAAE;IACdC,oBAAoB,CAACnK,GAAG,CAAC;EAC7B,CAAC,MACI;IACD,IAAI,CAACA,GAAG,CAACoK,cAAc,EAAE;MACrB;IACJ;IACAN,qBAAqB,GAAGO,kBAAkB,CAACrK,GAAG,CAACoK,cAAc,CAAC;IAC9DpK,GAAG,CAACoK,cAAc,CAACE,QAAQ,GAAG,MAAM;MAChCC,oBAAoB,CAACvK,GAAG,CAAC;MACzB,IAAIwK,eAAe,CAAC,CAAC,IAAIC,iBAAiB,CAACzK,GAAG,CAAC,EAAE;QAC7C0K,eAAe,CAAC1K,GAAG,CAAC;MACxB,CAAC,MACI,IAAI2K,gBAAgB,CAAC3K,GAAG,CAAC,EAAE;QAC5B4K,gBAAgB,CAAC5K,GAAG,CAAC;MACzB;IACJ,CAAC;EACL;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMmK,oBAAoB,GAAInK,GAAG,IAAK;EAClCA,GAAG,CAACyC,gBAAgB,CAAC,iBAAiB,EAAG/C,EAAE,IAAKgL,eAAe,CAAC1K,GAAG,EAAEN,EAAE,CAAC,CAAC;EACzEM,GAAG,CAACyC,gBAAgB,CAAC,iBAAiB,EAAE,MAAMmI,gBAAgB,CAAC5K,GAAG,CAAC,CAAC;AACxE,CAAC;AACD,MAAM0K,eAAe,GAAGA,CAAC1K,GAAG,EAAEN,EAAE,KAAK;EACjCmL,qBAAqB,CAAC7K,GAAG,EAAEN,EAAE,CAAC;EAC9BqK,YAAY,GAAG,IAAI;AACvB,CAAC;AACD,MAAMa,gBAAgB,GAAI5K,GAAG,IAAK;EAC9B8K,sBAAsB,CAAC9K,GAAG,CAAC;EAC3B+J,YAAY,GAAG,KAAK;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAMO,sBAAsB,GAAG,CAAClB,sBAAsB,CAACmB,MAAM,GAAGlB,qBAAqB,CAACkB,MAAM,IAAIlB,qBAAqB,CAACmB,KAAK;EAC3H,OAAQ,CAAClB,YAAY,IACjBF,sBAAsB,CAACqB,KAAK,KAAKpB,qBAAqB,CAACoB,KAAK,IAC5DH,sBAAsB,GAAGnB,kBAAkB;AACnD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMa,iBAAiB,GAAIzK,GAAG,IAAK;EAC/B,OAAO+J,YAAY,IAAI,CAACY,gBAAgB,CAAC3K,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2K,gBAAgB,GAAI3K,GAAG,IAAK;EAC9B,OAAO+J,YAAY,IAAID,qBAAqB,CAACkB,MAAM,KAAKhL,GAAG,CAACmL,WAAW;AAC3E,CAAC;AACD;AACA;AACA;AACA,MAAMN,qBAAqB,GAAGA,CAAC7K,GAAG,EAAEoL,QAAQ,KAAK;EAC7C,MAAMC,cAAc,GAAGD,QAAQ,GAAGA,QAAQ,CAACC,cAAc,GAAGrL,GAAG,CAACmL,WAAW,GAAGrB,qBAAqB,CAACkB,MAAM;EAC1G,MAAMtL,EAAE,GAAG,IAAI4L,WAAW,CAAC5B,iBAAiB,EAAE;IAC1C6B,MAAM,EAAE;MAAEF;IAAe;EAC7B,CAAC,CAAC;EACFrL,GAAG,CAACwL,aAAa,CAAC9L,EAAE,CAAC;AACzB,CAAC;AACD;AACA;AACA;AACA,MAAMoL,sBAAsB,GAAI9K,GAAG,IAAK;EACpC,MAAMN,EAAE,GAAG,IAAI4L,WAAW,CAAC3B,kBAAkB,CAAC;EAC9C3J,GAAG,CAACwL,aAAa,CAAC9L,EAAE,CAAC;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6K,oBAAoB,GAAIvK,GAAG,IAAK;EAClC6J,sBAAsB,GAAG4B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,qBAAqB,CAAC;EACjEA,qBAAqB,GAAGO,kBAAkB,CAACrK,GAAG,CAACoK,cAAc,CAAC;AAClE,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAID,cAAc,IAAK;EAC3C,OAAO;IACHc,KAAK,EAAE5P,IAAI,CAACqQ,KAAK,CAACvB,cAAc,CAACc,KAAK,CAAC;IACvCF,MAAM,EAAE1P,IAAI,CAACqQ,KAAK,CAACvB,cAAc,CAACY,MAAM,CAAC;IACzCY,SAAS,EAAExB,cAAc,CAACwB,SAAS;IACnCC,UAAU,EAAEzB,cAAc,CAACyB,UAAU;IACrCC,OAAO,EAAE1B,cAAc,CAAC0B,OAAO;IAC/BC,QAAQ,EAAE3B,cAAc,CAAC2B,QAAQ;IACjCd,KAAK,EAAEb,cAAc,CAACa;EAC1B,CAAC;AACL,CAAC;;;;;;;;;;;;;;;;;AC/ID;AACA;AACA;AAC4D;AAE5D,IAAIe,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa;AAChD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AAEzC,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;EAC/B;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjC;AACJ;AACA;AACA;AACA;AACA;EACIA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnC;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;AACnC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAMxC,QAAQ,GAAG;EACbzG,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAGhD,yDAAY,CAAC,CAAC;IAChC,IAAIgD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,UAAU,CAAC,EAAE;MAC/F,OAAOD,SAAS,CAACE,OAAO,CAACsG,QAAQ;IACrC;IACA,OAAOnK,SAAS;EACpB,CAAC;EACD4M,aAAaA,CAAA,EAAG;IACZ,MAAM5I,MAAM,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;IAC/B,IAAI,EAAEM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4I,aAAa,CAAC,EAAE;MACzE,OAAO9G,OAAO,CAACC,OAAO,CAAC/F,SAAS,CAAC;IACrC;IACA,OAAOgE,MAAM,CAAC4I,aAAa,CAAC,CAAC,CAACC,KAAK,CAAEtD,CAAC,IAAK;MACvC,IAAIA,CAAC,CAACuD,IAAI,KAAKJ,aAAa,CAACK,aAAa,EAAE;QACxC;QACA;QACA,OAAO/M,SAAS;MACpB;MACA,MAAMuJ,CAAC;IACX,CAAC,CAAC;EACN;AACJ,CAAC;;;;;;;;;;;;;;;;;;;AC5ED;AACA;AACA;AACyD;AACmB;;AAE5E;AACA;AACA;AACA;AACA;AACA,MAAM0D,kBAAkB,GAAIC,UAAU,IAAK;EACvC;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIF,iDAAG,KAAKhN,SAAS,IAAIkN,UAAU,KAAKP,oDAAc,CAACQ,IAAI,IAAID,UAAU,KAAKlN,SAAS,EAAE;IACrF,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMoN,MAAM,GAAGJ,iDAAG,CAAC3G,aAAa,CAAC,SAAS,CAAC;EAC3C,OAAO+G,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGJ,iDAAG,CAAC1K,IAAI;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM+K,wBAAwB,GAAIH,UAAU,IAAK;EAC7C,MAAMI,gBAAgB,GAAGL,kBAAkB,CAACC,UAAU,CAAC;EACvD,OAAOI,gBAAgB,KAAK,IAAI,GAAG,CAAC,GAAGA,gBAAgB,CAACC,YAAY;AACxE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB;EAAA,IAAA5H,IAAA,GAAAC,6OAAA,CAAG,WAAO4H,sBAAsB,EAAK;IAC/D,IAAIC,uBAAuB;IAC3B,IAAIC,uBAAuB;IAC3B,IAAIC,eAAe;IACnB;AACJ;AACA;AACA;IACI,IAAIC,4BAA4B;IAChC,MAAMC,IAAI;MAAA,IAAAC,KAAA,GAAAlI,6OAAA,CAAG,aAAY;QACrB,MAAMmI,aAAa,SAAS7D,oDAAQ,CAACyC,aAAa,CAAC,CAAC;QACpD,MAAMM,UAAU,GAAGc,aAAa,KAAKhO,SAAS,GAAGA,SAAS,GAAGgO,aAAa,CAACC,IAAI;QAC/EP,uBAAuB,GAAGA,CAAA,KAAM;UAC5B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;UACY,IAAIG,4BAA4B,KAAK7N,SAAS,EAAE;YAC5C6N,4BAA4B,GAAGR,wBAAwB,CAACH,UAAU,CAAC;UACvE;UACAU,eAAe,GAAG,IAAI;UACtBM,kBAAkB,CAACN,eAAe,EAAEV,UAAU,CAAC;QACnD,CAAC;QACDS,uBAAuB,GAAGA,CAAA,KAAM;UAC5BC,eAAe,GAAG,KAAK;UACvBM,kBAAkB,CAACN,eAAe,EAAEV,UAAU,CAAC;QACnD,CAAC;QACDxM,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAACyC,gBAAgB,CAAC,kBAAkB,EAAEuK,uBAAuB,CAAC;QAC3GhN,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAACyC,gBAAgB,CAAC,kBAAkB,EAAEwK,uBAAuB,CAAC;MAC/G,CAAC;MAAA,gBAxBKG,IAAIA,CAAA;QAAA,OAAAC,KAAA,CAAA9H,KAAA,OAAAC,SAAA;MAAA;IAAA,GAwBT;IACD,MAAMgI,kBAAkB,GAAGA,CAACC,KAAK,EAAEjB,UAAU,KAAK;MAC9C,IAAIO,sBAAsB,EAAE;QACxBA,sBAAsB,CAACU,KAAK,EAAEC,2BAA2B,CAAClB,UAAU,CAAC,CAAC;MAC1E;IACJ,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,MAAMkB,2BAA2B,GAAIlB,UAAU,IAAK;MAChD;MACA;AACR;AACA;AACA;AACA;AACA;MACQW,4BAA4B,KAAK,CAAC;MAC9B;AACZ;AACA;AACA;MACYA,4BAA4B,KAAKR,wBAAwB,CAACH,UAAU,CAAC,EAAE;QACvE;MACJ;MACA;AACR;AACA;AACA;AACA;MACQ,MAAMI,gBAAgB,GAAGL,kBAAkB,CAACC,UAAU,CAAC;MACvD,IAAII,gBAAgB,KAAK,IAAI,EAAE;QAC3B;MACJ;MACA;AACR;AACA;AACA;MACQ,OAAO,IAAIxH,OAAO,CAAEC,OAAO,IAAK;QAC5B,MAAMsI,QAAQ,GAAGA,CAAA,KAAM;UACnB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACgB,IAAIf,gBAAgB,CAACC,YAAY,KAAKM,4BAA4B,EAAE;YAChE;AACpB;AACA;AACA;YACoBS,EAAE,CAACC,UAAU,CAAC,CAAC;YACfxI,OAAO,CAAC,CAAC;UACb;QACJ,CAAC;QACD;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,MAAMuI,EAAE,GAAG,IAAIE,cAAc,CAACH,QAAQ,CAAC;QACvCC,EAAE,CAACG,OAAO,CAACnB,gBAAgB,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACD,MAAMjK,OAAO,GAAGA,CAAA,KAAM;MAClB3C,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAAC4C,mBAAmB,CAAC,kBAAkB,EAAEoK,uBAAuB,CAAC;MAC9GhN,iDAAG,KAAK,IAAI,IAAIA,iDAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iDAAG,CAAC4C,mBAAmB,CAAC,kBAAkB,EAAEqK,uBAAuB,CAAC;MAC9GD,uBAAuB,GAAGC,uBAAuB,GAAG3N,SAAS;IACjE,CAAC;IACD,MAAM0O,iBAAiB,GAAGA,CAAA,KAAMd,eAAe;IAC/C,MAAME,IAAI,CAAC,CAAC;IACZ,OAAO;MAAEA,IAAI;MAAEzK,OAAO;MAAEqL;IAAkB,CAAC;EAC/C,CAAC;EAAA,gBApHKlB,wBAAwBA,CAAAxH,EAAA;IAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoH7B;;;;;;;;;;;;;;;;;AClKD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyI,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,IAAIC,WAAW;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,IAAI;IAAA,IAAAjJ,IAAA,GAAAC,6OAAA,CAAG,aAAY;MACrB,MAAM8B,CAAC,GAAGiH,WAAW;MACrB,IAAI7I,OAAO;MACX6I,WAAW,GAAG,IAAI9I,OAAO,CAAE8B,CAAC,IAAM7B,OAAO,GAAG6B,CAAE,CAAC;MAC/C,IAAID,CAAC,KAAK3H,SAAS,EAAE;QACjB,MAAM2H,CAAC;MACX;MACA,OAAO5B,OAAO;IAClB,CAAC;IAAA,gBARK8I,IAAIA,CAAA;MAAA,OAAAjJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA,GAQT;EACD,OAAO;IACH2I;EACJ,CAAC;AACL,CAAC;;;;;;;;;;;;;;;ACnCD;AACA;AACA;AACA,MAAMC,QAAQ,GAAG;EACbC,OAAO,EAAE;IACLC,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMC,cAAc,GAAI,GAAGJ,GAAG,GAAG9U,KAAK,GAAIiV,KAAK,GAAGH,GAAI,IAAG;MACzD,MAAMK,KAAK,GAAI,CAAC,GAAGrT,IAAI,CAACsT,EAAE,GAAGpV,KAAK,GAAIiV,KAAK;MAC3C,OAAO;QACHvH,CAAC,EAAE,CAAC;QACJtD,KAAK,EAAE;UACHsC,GAAG,EAAG,GAAE,EAAE,GAAG5K,IAAI,CAACuT,GAAG,CAACF,KAAK,CAAE,GAAE;UAC/BxI,IAAI,EAAG,GAAE,EAAE,GAAG7K,IAAI,CAACwT,GAAG,CAACH,KAAK,CAAE,GAAE;UAChC,iBAAiB,EAAED;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDH,OAAO,EAAE;IACLD,GAAG,EAAE,IAAI;IACTC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMM,IAAI,GAAGvV,KAAK,GAAGiV,KAAK;MAC1B,MAAMC,cAAc,GAAI,GAAEJ,GAAG,GAAGS,IAAI,GAAGT,GAAI,IAAG;MAC9C,MAAMK,KAAK,GAAG,CAAC,GAAGrT,IAAI,CAACsT,EAAE,GAAGG,IAAI;MAChC,OAAO;QACH7H,CAAC,EAAE,CAAC;QACJtD,KAAK,EAAE;UACHsC,GAAG,EAAG,GAAE,EAAE,GAAG5K,IAAI,CAACuT,GAAG,CAACF,KAAK,CAAE,GAAE;UAC/BxI,IAAI,EAAG,GAAE,EAAE,GAAG7K,IAAI,CAACwT,GAAG,CAACH,KAAK,CAAE,GAAE;UAChC,iBAAiB,EAAED;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDM,QAAQ,EAAE;IACNV,GAAG,EAAE,IAAI;IACTW,WAAW,EAAE,IAAI;IACjBV,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAAA,KAAM;MACN,OAAO;QACHtH,CAAC,EAAE,EAAE;QACLgI,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,gBAAgB;QAC3B1L,KAAK,EAAE,CAAC;MACZ,CAAC;IACL;EACJ,CAAC;EACD2L,QAAQ,EAAE;IACNjB,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAAA,KAAM;MACN,OAAO;QACHtH,CAAC,EAAE,EAAE;QACLtD,KAAK,EAAE,CAAC;MACZ,CAAC;IACL;EACJ,CAAC;EACD4L,IAAI,EAAE;IACFlB,GAAG,EAAE,GAAG;IACRC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAEA,CAACiB,CAAC,EAAEjW,KAAK,KAAK;MACd,MAAMkV,cAAc,GAAG,EAAE,GAAG,GAAGlV,KAAK,CAAC,GAAG,IAAI;MAC5C,OAAO;QACH0N,CAAC,EAAE,CAAC;QACJtD,KAAK,EAAE;UACHuC,IAAI,EAAG,GAAE,EAAE,GAAG,EAAE,GAAG3M,KAAM,GAAE;UAC3B,iBAAiB,EAAEkV;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACDgB,KAAK,EAAE;IACHpB,GAAG,EAAE,IAAI;IACToB,KAAK,EAAE,CAAC;IACRlB,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMa,SAAS,GAAI,UAAU,GAAG,GAAGb,KAAK,GAAIjV,KAAK,IAAIA,KAAK,GAAGiV,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,MAAK;MAC1F,MAAMC,cAAc,GAAI,GAAGJ,GAAG,GAAG9U,KAAK,GAAIiV,KAAK,GAAGH,GAAI,IAAG;MACzD,OAAO;QACHqB,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNhM,KAAK,EAAE;UACH0L,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEZ;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,aAAa,EAAE;IACXJ,GAAG,EAAE,IAAI;IACToB,KAAK,EAAE,CAAC;IACRlB,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMa,SAAS,GAAI,UAAU,GAAG,GAAGb,KAAK,GAAIjV,KAAK,IAAIA,KAAK,GAAGiV,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,MAAK;MAC1F,MAAMC,cAAc,GAAI,GAAGJ,GAAG,GAAG9U,KAAK,GAAIiV,KAAK,GAAGH,GAAI,IAAG;MACzD,OAAO;QACHqB,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNhM,KAAK,EAAE;UACH0L,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEZ;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,aAAa,EAAE;IACXJ,GAAG,EAAE,IAAI;IACToB,KAAK,EAAE,EAAE;IACTlB,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMa,SAAS,GAAI,UAAS,EAAE,GAAG9V,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,MAAK;MACvE,MAAMkV,cAAc,GAAI,GAAGJ,GAAG,GAAG9U,KAAK,GAAIiV,KAAK,GAAGH,GAAI,IAAG;MACzD,OAAO;QACHqB,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNhM,KAAK,EAAE;UACH0L,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEZ;QACvB;MACJ,CAAC;IACL;EACJ,CAAC;EACD,mBAAmB,EAAE;IACjBJ,GAAG,EAAE,IAAI;IACToB,KAAK,EAAE,EAAE;IACTlB,EAAE,EAAEA,CAACF,GAAG,EAAE9U,KAAK,EAAEiV,KAAK,KAAK;MACvB,MAAMa,SAAS,GAAI,UAAS,EAAE,GAAG9V,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,MAAK;MACvE,MAAMkV,cAAc,GAAI,GAAGJ,GAAG,GAAG9U,KAAK,GAAIiV,KAAK,GAAGH,GAAI,IAAG;MACzD,OAAO;QACHqB,EAAE,EAAE,EAAE;QACNC,EAAE,EAAE,EAAE;QACNhM,KAAK,EAAE;UACH0L,SAAS,EAAEA,SAAS;UACpB,iBAAiB,EAAEZ;QACvB;MACJ,CAAC;IACL;EACJ;AACJ,CAAC;AACD,MAAMmB,QAAQ,GAAGzB,QAAQ;;;;;;;;;;;;;;;;;;;AC9IzB;AACA;AACA;AACmD;AACJ;AACK;AACV;AAE1C,MAAM4B,sBAAsB,GAAGA,CAAC/R,EAAE,EAAEgS,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,KAAK;EACjG,MAAMpQ,GAAG,GAAG/B,EAAE,CAACoS,aAAa,CAACC,WAAW;EACxC,IAAIC,GAAG,GAAGzP,mDAAK,CAAC7C,EAAE,CAAC;EACnB;AACJ;AACA;AACA;AACA;EACI,MAAMuS,QAAQ,GAAIjF,MAAM,IAAK;IACzB,MAAM/L,SAAS,GAAG,EAAE;IACpB,MAAM;MAAEiR;IAAO,CAAC,GAAGlF,MAAM;IACzB,IAAIgF,GAAG,EAAE;MACL,OAAOE,MAAM,IAAIzQ,GAAG,CAAC0Q,UAAU,GAAGlR,SAAS;IAC/C;IACA,OAAOiR,MAAM,IAAIjR,SAAS;EAC9B,CAAC;EACD,MAAMmR,SAAS,GAAIpF,MAAM,IAAK;IAC1B,OAAOgF,GAAG,GAAG,CAAChF,MAAM,CAACqF,MAAM,GAAGrF,MAAM,CAACqF,MAAM;EAC/C,CAAC;EACD,MAAMC,YAAY,GAAItF,MAAM,IAAK;IAC7B,OAAOgF,GAAG,GAAG,CAAChF,MAAM,CAACuF,SAAS,GAAGvF,MAAM,CAACuF,SAAS;EACrD,CAAC;EACD,MAAMC,QAAQ,GAAIxF,MAAM,IAAK;IACzB;AACR;AACA;AACA;AACA;IACQgF,GAAG,GAAGzP,mDAAK,CAAC7C,EAAE,CAAC;IACf,OAAOuS,QAAQ,CAACjF,MAAM,CAAC,IAAI0E,eAAe,CAAC,CAAC;EAChD,CAAC;EACD,MAAMpQ,MAAM,GAAI0L,MAAM,IAAK;IACvB;IACA,MAAMyF,KAAK,GAAGL,SAAS,CAACpF,MAAM,CAAC;IAC/B,MAAM0F,SAAS,GAAGD,KAAK,GAAGhR,GAAG,CAAC0Q,UAAU;IACxCP,aAAa,CAACc,SAAS,CAAC;EAC5B,CAAC;EACD,MAAMnR,KAAK,GAAIyL,MAAM,IAAK;IACtB;IACA,MAAMyF,KAAK,GAAGL,SAAS,CAACpF,MAAM,CAAC;IAC/B,MAAML,KAAK,GAAGlL,GAAG,CAAC0Q,UAAU;IAC5B,MAAMO,SAAS,GAAGD,KAAK,GAAG9F,KAAK;IAC/B,MAAMgG,QAAQ,GAAGL,YAAY,CAACtF,MAAM,CAAC;IACrC,MAAM4F,CAAC,GAAGjG,KAAK,GAAG,GAAG;IACrB,MAAMkG,cAAc,GAAGF,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAIF,KAAK,GAAGG,CAAC,CAAC;IACrE,MAAME,OAAO,GAAGD,cAAc,GAAG,CAAC,GAAGH,SAAS,GAAGA,SAAS;IAC1D,MAAMK,eAAe,GAAGD,OAAO,GAAGnG,KAAK;IACvC,IAAIqG,OAAO,GAAG,CAAC;IACf,IAAID,eAAe,GAAG,CAAC,EAAE;MACrB,MAAMhD,GAAG,GAAGgD,eAAe,GAAGhW,IAAI,CAACkW,GAAG,CAACN,QAAQ,CAAC;MAChDK,OAAO,GAAGjW,IAAI,CAACmW,GAAG,CAACnD,GAAG,EAAE,GAAG,CAAC;IAChC;IACA8B,YAAY,CAACgB,cAAc,EAAEH,SAAS,IAAI,CAAC,GAAG,IAAI,GAAGlB,uDAAK,CAAC,CAAC,EAAEkB,SAAS,EAAE,MAAM,CAAC,EAAEM,OAAO,CAAC;EAC9F,CAAC;EACD,OAAOxT,iEAAa,CAAC;IACjBE,EAAE;IACFsB,WAAW,EAAE,cAAc;IAC3B;AACR;AACA;AACA;IACQmS,eAAe,EAAE,GAAG;IACpBlS,SAAS,EAAE,EAAE;IACbuR,QAAQ;IACRtR,OAAO,EAAEyQ,cAAc;IACvBrQ,MAAM;IACNC;EACJ,CAAC,CAAC;AACN,CAAC;;;;;;;;;;;;;;;AC5ED;AACA;AACA;AACA,MAAM6R,eAAe,GAAGA,CAACC,WAAW,EAAE5M,OAAO,EAAE6M,QAAQ,KAAK;EACxD,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;IACzC;EACJ;EACA,MAAMC,QAAQ,GAAG,IAAID,gBAAgB,CAAEE,YAAY,IAAK;IACpDH,QAAQ,CAACI,iBAAiB,CAACD,YAAY,EAAEhN,OAAO,CAAC,CAAC;EACtD,CAAC,CAAC;EACF+M,QAAQ,CAAChE,OAAO,CAAC6D,WAAW,EAAE;IAC1BM,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACnB,CAAC;AACD,MAAME,iBAAiB,GAAGA,CAACD,YAAY,EAAEhN,OAAO,KAAK;EACjD,IAAIoN,SAAS;EACbJ,YAAY,CAACzX,OAAO,CAAE8X,GAAG,IAAK;IAC1B;IACA,KAAK,IAAI5X,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4X,GAAG,CAACC,UAAU,CAACpZ,MAAM,EAAEuB,CAAC,EAAE,EAAE;MAC5C2X,SAAS,GAAGG,iBAAiB,CAACF,GAAG,CAACC,UAAU,CAAC7X,CAAC,CAAC,EAAEuK,OAAO,CAAC,IAAIoN,SAAS;IAC1E;EACJ,CAAC,CAAC;EACF,OAAOA,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,GAAGA,CAACC,IAAI,EAAExN,OAAO,KAAK;EACzC;AACJ;AACA;AACA;EACI,IAAIwN,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;IACrB,OAAOnT,SAAS;EACpB;EACA;EACA,MAAMrB,EAAE,GAAGuU,IAAI;EACf,MAAM7O,OAAO,GAAG1F,EAAE,CAAC+G,OAAO,KAAKA,OAAO,CAAC0N,WAAW,CAAC,CAAC,GAAG,CAACzU,EAAE,CAAC,GAAGuC,KAAK,CAACmS,IAAI,CAAC1U,EAAE,CAAC2U,gBAAgB,CAAC5N,OAAO,CAAC,CAAC;EACtG,OAAOrB,OAAO,CAACkP,IAAI,CAAE1J,CAAC,IAAKA,CAAC,CAAC2J,KAAK,KAAK7U,EAAE,CAAC6U,KAAK,CAAC;AACpD,CAAC", "sources": ["./src/app/services/signal.service.ts", "./node_modules/@ionic/core/dist/esm/button-active-f7898f4b.js", "./node_modules/@ionic/core/dist/esm/capacitor-59395cbd.js", "./node_modules/@ionic/core/dist/esm/compare-with-utils-a96ff2ea.js", "./node_modules/@ionic/core/dist/esm/dir-babeabeb.js", "./node_modules/@ionic/core/dist/esm/focus-visible-dd40d69f.js", "./node_modules/@ionic/core/dist/esm/haptic-ac164e4c.js", "./node_modules/@ionic/core/dist/esm/index-5cc724f3.js", "./node_modules/@ionic/core/dist/esm/index-e2cf2ceb.js", "./node_modules/@ionic/core/dist/esm/keyboard-52278bd7.js", "./node_modules/@ionic/core/dist/esm/keyboard-73175e24.js", "./node_modules/@ionic/core/dist/esm/keyboard-controller-ec5c2bfa.js", "./node_modules/@ionic/core/dist/esm/lock-controller-316928be.js", "./node_modules/@ionic/core/dist/esm/spinner-configs-964f7cf3.js", "./node_modules/@ionic/core/dist/esm/swipe-back-e5394307.js", "./node_modules/@ionic/core/dist/esm/watch-options-c2911ace.js"], "sourcesContent": ["import { Injectable, signal, computed } from '@angular/core';\r\nimport { ImageData } from 'src/models/ImageData';\r\nimport { ProcessDocData } from 'src/models/ProcessDocData';\r\nimport { TransformedDocData } from 'src/models/TransformedDocData';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SignalService {\r\n  private data = signal<ProcessDocData[]>([]);\r\n  private imageData = signal<ProcessDocData[]>([]);\r\n  private transformedData = signal<TransformedDocData[]>([]);\r\n\r\n  // Create a computed signal for the empty state\r\n  private readonly isEmpty = computed(() => this.data().length === 0);\r\n\r\n  // Signal of ProcessDocData ------------------------------------------------------------\r\n  \r\n  setData(newData: ProcessDocData) {\r\n    newData.title = `Scan ${this.formatDate()}`;\r\n\r\n    this.data.update((data) => {\r\n      const index = data.length;\r\n      newData.page_index = index + 1;\r\n      const formattedDate_ToDay = this.getDateAsToday();\r\n      data.push({ ...newData, date: formattedDate_ToDay });\r\n      \r\n      // Handle forceSupplierGlobal here\r\n      if (data.length === 0) {\r\n        localStorage.removeItem('forceSupplierGlobal');\r\n      }\r\n      \r\n      return data;\r\n    });\r\n  }\r\n\r\n  getData(): ProcessDocData[] {\r\n    const currentData = this.data();\r\n    // Handle forceSupplierGlobal automatically when data is empty\r\n    if (currentData.length === 0) {\r\n      localStorage.removeItem('forceSupplierGlobal');\r\n    }\r\n    return currentData;\r\n  }\r\n\r\n\r\n  removeLastIndex() {\r\n    this.data.update((data) => {\r\n      // 1. Store the original length for comparison\r\n      const originalLength = data.length;\r\n  \r\n      // 2. Remove the item\r\n      data.pop();\r\n  \r\n      // 3. Check if the array is now empty after removal\r\n      if (originalLength === 1) {\r\n        localStorage.removeItem('forceSupplierGlobal');\r\n      }\r\n      return data;\r\n    });\r\n  }\r\n\r\n  \r\n  removeData(index: number) {\r\n    this.data.update((data) => {\r\n      // 1. Store the original length for comparison\r\n      const originalLength = data.length;\r\n  \r\n      // 2. Remove the item\r\n      data.splice(index, 1);\r\n  \r\n      // 3. Check if the array is now empty after removal\r\n      if (originalLength === 1) {\r\n        localStorage.removeItem('forceSupplierGlobal');\r\n      }\r\n  \r\n      // 4. Reassign page_index for remaining items\r\n      data.forEach((item, i) => (item.page_index = i + 1));\r\n  \r\n      return data;\r\n    });\r\n  }\r\n\r\n  checkDataisEmpty() {\r\n    this.data.update((data) => {\r\n      if (data.length === 0) {\r\n        localStorage.removeItem('forceSupplierGlobal');\r\n        return data;\r\n      }\r\n      return data;\r\n    });\r\n  }\r\n\r\n  removeAllData() {\r\n    this.data.update((data) => {\r\n      data = [];\r\n      return data;\r\n    });\r\n    localStorage.removeItem('forceSupplierGlobal');\r\n  }\r\n\r\n  getDateAsDDD_MMM() {\r\n    // Get the date of the newData or use the current date if not provided\r\n    const newDataDate = new Date();\r\n\r\n    // Format the date as \"ddd/MMM\"\r\n    const daysOfWeek = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];\r\n    const monthsOfYear = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n\r\n    const formattedDate = `${daysOfWeek[newDataDate.getDay()]}/${monthsOfYear[newDataDate.getMonth()]}`;\r\n    return formattedDate\r\n  }\r\n\r\n  getDateAsToday() {\r\n    // Get the current date\r\n    const currentDate = new Date();\r\n    // Get the date of the newData or use the current date if not provided\r\n    const newDataDate = currentDate;\r\n\r\n    // Calculate the difference in days between the current date and newData's date\r\n    const diffInDays = Math.floor((currentDate.getTime() - newDataDate.getTime()) / (1000 * 60 * 60 * 24));\r\n\r\n    let formattedDate;\r\n    if (diffInDays === 0) {\r\n      // If the difference is 0, it means it's today\r\n      formattedDate = \"Aujourd'hui\";\r\n    } else if (diffInDays === 1) {\r\n      // If the difference is 1, it means it's yesterday\r\n      formattedDate = \"Hier\";\r\n    } else {\r\n      // For other dates, you can use your original date formatting logic\r\n      // Format data dd/mm\r\n      formattedDate = `${newDataDate.getDate()}/${newDataDate.getMonth() + 1}`;\r\n    }\r\n\r\n    return formattedDate;\r\n  }\r\n\r\n  formatDate(): string {\r\n    const date = new Date();\r\n    const day = this.padNumber(date.getDate());\r\n    const month = this.padNumber(date.getMonth() + 1); // Months are zero-based\r\n    const year = date.getFullYear();\r\n    const hours = this.padNumber(date.getHours());\r\n    const minutes = this.padNumber(date.getMinutes());\r\n    const seconds = this.padNumber(date.getSeconds());\r\n    return `${day}:${month}:${year} ${hours}:${minutes}:${seconds}`;\r\n  }\r\n\r\n  padNumber(num: number): string {\r\n    return num.toString().padStart(2, '0');\r\n  }\r\n\r\n  get firstEntry() {\r\n    return this.data().length === 1;\r\n  }\r\n\r\n  private extractTitleFromUrl(url: string): string {\r\n    if (!url) return '';\r\n    const parts = url.split('/');\r\n    return parts[parts.length - 1].split('.')[0]; // Extracts the file name without extension\r\n  }\r\n\r\n  getFormattedDate(dateStr: string): string {\r\n    const currentDate = new Date();\r\n    const date = new Date(dateStr);\r\n    const diffInDays = Math.floor((currentDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\r\n\r\n    if (diffInDays === 0) {\r\n      return \"Aujourd'hui\";\r\n    } else if (diffInDays === 1) {\r\n      return 'Hier';\r\n    } else {\r\n      return `${date.getDate()}/${date.getMonth() + 1}`;\r\n    }\r\n  }\r\n\r\n  // Signal of ImageData ------------------------------------------------------------\r\n  \r\n  setImageData(newData: ImageData){\r\n    this.imageData.update((imageData) => {\r\n      const index = imageData.length;\r\n      newData.page_index = index + 1;\r\n      return imageData;\r\n    });\r\n  }\r\n\r\n  getImageData() {\r\n    return this.imageData();\r\n  }\r\n\r\n  // Signal of TransformedDocData ------------------------------------------------------------\r\n\r\n  transformAndSetData(data: any) {\r\n    this.transformedData.set(data)\r\n  }\r\n\r\n  getTransformedData() {\r\n    return this.transformedData();\r\n  }\r\n}\r\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask } from './index-c71c5417.js';\nimport { h as hapticSelectionEnd, a as hapticSelectionStart, b as hapticSelectionChanged } from './haptic-ac164e4c.js';\nimport { createGesture } from './index-39782642.js';\n\nconst createButtonActiveGesture = (el, isButton) => {\n    let currentTouchedButton;\n    let initialTouchedButton;\n    const activateButtonAtPoint = (x, y, hapticFeedbackFn) => {\n        if (typeof document === 'undefined') {\n            return;\n        }\n        const target = document.elementFromPoint(x, y);\n        if (!target || !isButton(target) || target.disabled) {\n            clearActiveButton();\n            return;\n        }\n        if (target !== currentTouchedButton) {\n            clearActiveButton();\n            setActiveButton(target, hapticFeedbackFn);\n        }\n    };\n    const setActiveButton = (button, hapticFeedbackFn) => {\n        currentTouchedButton = button;\n        if (!initialTouchedButton) {\n            initialTouchedButton = currentTouchedButton;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.add('ion-activated'));\n        hapticFeedbackFn();\n    };\n    const clearActiveButton = (dispatchClick = false) => {\n        if (!currentTouchedButton) {\n            return;\n        }\n        const buttonToModify = currentTouchedButton;\n        writeTask(() => buttonToModify.classList.remove('ion-activated'));\n        /**\n         * Clicking on one button, but releasing on another button\n         * does not dispatch a click event in browsers, so we\n         * need to do it manually here. Some browsers will\n         * dispatch a click if clicking on one button, dragging over\n         * another button, and releasing on the original button. In that\n         * case, we need to make sure we do not cause a double click there.\n         */\n        if (dispatchClick && initialTouchedButton !== currentTouchedButton) {\n            currentTouchedButton.click();\n        }\n        currentTouchedButton = undefined;\n    };\n    return createGesture({\n        el,\n        gestureName: 'buttonActiveDrag',\n        threshold: 0,\n        onStart: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionStart),\n        onMove: (ev) => activateButtonAtPoint(ev.currentX, ev.currentY, hapticSelectionChanged),\n        onEnd: () => {\n            clearActiveButton(true);\n            hapticSelectionEnd();\n            initialTouchedButton = undefined;\n        },\n    });\n};\n\nexport { createButtonActiveGesture as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\n\nconst getCapacitor = () => {\n    if (win !== undefined) {\n        return win.Capacitor;\n    }\n    return undefined;\n};\n\nexport { getCapacitor as g };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Uses the compareWith param to compare two values to determine if they are equal.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst compareOptions = (currentValue, compareValue, compareWith) => {\n    if (typeof compareWith === 'function') {\n        return compareWith(currentValue, compareValue);\n    }\n    else if (typeof compareWith === 'string') {\n        return currentValue[compareWith] === compareValue[compareWith];\n    }\n    else {\n        return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;\n    }\n};\n/**\n * Compares a value against the current value(s) to determine if it is selected.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst isOptionSelected = (currentValue, compareValue, compareWith) => {\n    if (currentValue === undefined) {\n        return false;\n    }\n    if (Array.isArray(currentValue)) {\n        return currentValue.some((val) => compareOptions(val, compareValue, compareWith));\n    }\n    else {\n        return compareOptions(currentValue, compareValue, compareWith);\n    }\n};\n\nexport { compareOptions as c, isOptionSelected as i };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = (hostEl) => {\n    if (hostEl) {\n        if (hostEl.dir !== '') {\n            return hostEl.dir.toLowerCase() === 'rtl';\n        }\n    }\n    return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\n\nexport { isRTL as i };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = [\n    'Tab',\n    'ArrowDown',\n    'Space',\n    'Escape',\n    ' ',\n    'Shift',\n    'Enter',\n    'ArrowLeft',\n    'ArrowRight',\n    'ArrowUp',\n    'Home',\n    'End',\n];\nconst startFocusVisible = (rootEl) => {\n    let currentFocus = [];\n    let keyboardMode = true;\n    const ref = rootEl ? rootEl.shadowRoot : document;\n    const root = rootEl ? rootEl : document.body;\n    const setFocus = (elements) => {\n        currentFocus.forEach((el) => el.classList.remove(ION_FOCUSED));\n        elements.forEach((el) => el.classList.add(ION_FOCUSED));\n        currentFocus = elements;\n    };\n    const pointerDown = () => {\n        keyboardMode = false;\n        setFocus([]);\n    };\n    const onKeydown = (ev) => {\n        keyboardMode = FOCUS_KEYS.includes(ev.key);\n        if (!keyboardMode) {\n            setFocus([]);\n        }\n    };\n    const onFocusin = (ev) => {\n        if (keyboardMode && ev.composedPath !== undefined) {\n            const toFocus = ev.composedPath().filter((el) => {\n                // TODO(FW-2832): type\n                if (el.classList) {\n                    return el.classList.contains(ION_FOCUSABLE);\n                }\n                return false;\n            });\n            setFocus(toFocus);\n        }\n    };\n    const onFocusout = () => {\n        if (ref.activeElement === root) {\n            setFocus([]);\n        }\n    };\n    ref.addEventListener('keydown', onKeydown);\n    ref.addEventListener('focusin', onFocusin);\n    ref.addEventListener('focusout', onFocusout);\n    ref.addEventListener('touchstart', pointerDown, { passive: true });\n    ref.addEventListener('mousedown', pointerDown);\n    const destroy = () => {\n        ref.removeEventListener('keydown', onKeydown);\n        ref.removeEventListener('focusin', onFocusin);\n        ref.removeEventListener('focusout', onFocusout);\n        ref.removeEventListener('touchstart', pointerDown);\n        ref.removeEventListener('mousedown', pointerDown);\n    };\n    return {\n        destroy,\n        setFocus,\n    };\n};\n\nexport { startFocusVisible };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n            // Capacitor\n            return capacitor.Plugins.Haptics;\n        }\n        return undefined;\n    },\n    available() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return false;\n        }\n        const capacitor = getCapacitor();\n        /**\n         * Developers can manually import the\n         * Haptics plugin in their app which will cause\n         * getEngine to return the Haptics engine. However,\n         * the Haptics engine will throw an error if\n         * used in a web browser that does not support\n         * the Vibrate API. This check avoids that error\n         * if the browser does not support the Vibrate API.\n         */\n        if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n            return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n        }\n        return true;\n    },\n    impact(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.impact({ style: options.style });\n    },\n    notification(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.notification({ type: options.type });\n    },\n    selection() {\n        this.impact({ style: ImpactStyle.Light });\n    },\n    selectionStart() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionStart();\n    },\n    selectionChanged() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionChanged();\n    },\n    selectionEnd() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionEnd();\n    },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n    return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n    hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n    hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n    hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n    hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n    hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionStart as a, hapticSelectionChanged as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers-da915de8.js';\nimport { b as printRequiredElementError } from './index-9b0d46f4.js';\n\nconst ION_CONTENT_TAG_NAME = 'ION-CONTENT';\nconst ION_CONTENT_ELEMENT_SELECTOR = 'ion-content';\nconst ION_CONTENT_CLASS_SELECTOR = '.ion-content-scroll-host';\n/**\n * Selector used for implementations reliant on `<ion-content>` for scroll event changes.\n *\n * Developers should use the `.ion-content-scroll-host` selector to target the element emitting\n * scroll events. With virtual scroll implementations this will be the host element for\n * the scroll viewport.\n */\nconst ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;\nconst isIonContent = (el) => el.tagName === ION_CONTENT_TAG_NAME;\n/**\n * Waits for the element host fully initialize before\n * returning the inner scroll element.\n *\n * For `ion-content` the scroll target will be the result\n * of the `getScrollElement` function.\n *\n * For custom implementations it will be the element host\n * or a selector within the host, if supplied through `scrollTarget`.\n */\nconst getScrollElement = async (el) => {\n    if (isIonContent(el)) {\n        await new Promise((resolve) => componentOnReady(el, resolve));\n        return el.getScrollElement();\n    }\n    return el;\n};\n/**\n * Queries the element matching the selector for IonContent.\n * See ION_CONTENT_SELECTOR for the selector used.\n */\nconst findIonContent = (el) => {\n    /**\n     * First we try to query the custom scroll host selector in cases where\n     * the implementation is using an outer `ion-content` with an inner custom\n     * scroll container.\n     */\n    const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);\n    if (customContentHost) {\n        return customContentHost;\n    }\n    return el.querySelector(ION_CONTENT_SELECTOR);\n};\n/**\n * Queries the closest element matching the selector for IonContent.\n */\nconst findClosestIonContent = (el) => {\n    return el.closest(ION_CONTENT_SELECTOR);\n};\n/**\n * Scrolls to the top of the element. If an `ion-content` is found, it will scroll\n * using the public API `scrollToTop` with a duration.\n */\nconst scrollToTop = (el, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollToTop(durationMs);\n    }\n    return Promise.resolve(el.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: durationMs > 0 ? 'smooth' : 'auto',\n    }));\n};\n/**\n * Scrolls by a specified X/Y distance in the component. If an `ion-content` is found, it will scroll\n * using the public API `scrollByPoint` with a duration.\n */\nconst scrollByPoint = (el, x, y, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollByPoint(x, y, durationMs);\n    }\n    return Promise.resolve(el.scrollBy({\n        top: y,\n        left: x,\n        behavior: durationMs > 0 ? 'smooth' : 'auto',\n    }));\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within either the `ion-content` selector or the `.ion-content-scroll-host` class.\n */\nconst printIonContentErrorMsg = (el) => {\n    return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);\n};\n/**\n * Several components in Ionic need to prevent scrolling\n * during a gesture (card modal, range, item sliding, etc).\n * Use this utility to account for ion-content and custom content hosts.\n */\nconst disableContentScrollY = (contentEl) => {\n    if (isIonContent(contentEl)) {\n        const ionContent = contentEl;\n        const initialScrollY = ionContent.scrollY;\n        ionContent.scrollY = false;\n        /**\n         * This should be passed into resetContentScrollY\n         * so that we can revert ion-content's scrollY to the\n         * correct state. For example, if scrollY = false\n         * initially, we do not want to enable scrolling\n         * when we call resetContentScrollY.\n         */\n        return initialScrollY;\n    }\n    else {\n        contentEl.style.setProperty('overflow', 'hidden');\n        return true;\n    }\n};\nconst resetContentScrollY = (contentEl, initialScrollY) => {\n    if (isIonContent(contentEl)) {\n        contentEl.scrollY = initialScrollY;\n    }\n    else {\n        contentEl.style.removeProperty('overflow');\n    }\n};\n\nexport { ION_CONTENT_CLASS_SELECTOR as I, findIonContent as a, ION_CONTENT_ELEMENT_SELECTOR as b, scrollByPoint as c, disableContentScrollY as d, findClosestIonContent as f, getScrollElement as g, isIonContent as i, printIonContentErrorMsg as p, resetContentScrollY as r, scrollToTop as s };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/* Ionicons v7.2.2, E<PERSON> Modules */\nconst arrowBackSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-miterlimit='10' stroke-width='48' d='M244 400L100 256l144-144M120 256h292' class='ionicon-fill-none'/></svg>\";\nconst arrowDown = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 268l144 144 144-144M256 392V100' class='ionicon-fill-none'/></svg>\";\nconst caretBackSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M368 64L144 256l224 192V64z'/></svg>\";\nconst caretDownSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 144l192 224 192-224H64z'/></svg>\";\nconst caretUpSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 368L256 144 64 368h384z'/></svg>\";\nconst checkmarkOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M416 128L192 384l-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst chevronBack = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>\";\nconst chevronDown = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>\";\nconst chevronExpand = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M136 208l120-104 120 104M136 304l120 104 120-104' stroke-width='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none'/></svg>\";\nconst chevronForward = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>\";\nconst chevronForwardOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>\";\nconst close = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z'/></svg>\";\nconst closeCircle = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>\";\nconst closeSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>\";\nconst ellipseOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='192' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst ellipsisHorizontal = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='48'/><circle cx='416' cy='256' r='48'/><circle cx='96' cy='256' r='48'/></svg>\";\nconst eye = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='256' r='64'/><path d='M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z'/></svg>\";\nconst eyeOff = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 448a15.92 15.92 0 01-11.31-4.69l-352-352a16 16 0 0122.62-22.62l352 352A16 16 0 01432 448zM248 315.85l-51.79-51.79a2 2 0 00-3.39 1.69 64.11 64.11 0 0053.49 53.49 2 2 0 001.69-3.39zM264 196.15L315.87 248a2 2 0 003.4-1.69 64.13 64.13 0 00-53.55-53.55 2 2 0 00-1.72 3.39z'/><path d='M491 273.36a32.2 32.2 0 00-.1-34.76c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.68 96a226.54 226.54 0 00-71.82 11.79 4 4 0 00-1.56 6.63l47.24 47.24a4 4 0 003.82 1.05 96 96 0 01116 116 4 4 0 001.05 3.81l67.95 68a4 4 0 005.4.24 343.81 343.81 0 0067.24-77.4zM256 352a96 96 0 01-93.3-118.63 4 4 0 00-1.05-3.81l-66.84-66.87a4 4 0 00-5.41-.23c-24.39 20.81-47 46.13-67.67 75.72a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.39 76.14 98.28 100.65C162.06 402 207.92 416 255.68 416a238.22 238.22 0 0072.64-11.55 4 4 0 001.61-6.64l-47.47-47.46a4 4 0 00-3.81-1.05A96 96 0 01256 352z'/></svg>\";\nconst menuOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst menuSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M64 384h384v-42.67H64zm0-106.67h384v-42.66H64zM64 128v42.67h384V128z'/></svg>\";\nconst removeOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M400 256H112' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst reorderThreeOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M96 256h320M96 176h320M96 336h320' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst reorderTwoSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='44' d='M118 304h276M118 208h276' class='ionicon-fill-none'/></svg>\";\nconst searchOutline = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>\";\nconst searchSharp = \"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 428L339.92 303.9a160.48 160.48 0 0030.72-94.58C370.64 120.37 298.27 48 209.32 48S48 120.37 48 209.32s72.37 161.32 161.32 161.32a160.48 160.48 0 0094.58-30.72L428 464zM209.32 319.69a110.38 110.38 0 11110.37-110.37 110.5 110.5 0 01-110.37 110.37z'/></svg>\";\n\nexport { arrowBackSharp as a, closeCircle as b, chevronBack as c, closeSharp as d, searchSharp as e, checkmarkOutline as f, ellipseOutline as g, caretBackSharp as h, arrowDown as i, reorderThreeOutline as j, reorderTwoSharp as k, chevronDown as l, chevronForwardOutline as m, ellipsisHorizontal as n, chevronForward as o, caretUpSharp as p, caretDownSharp as q, removeOutline as r, searchOutline as s, close as t, menuOutline as u, menuSharp as v, chevronExpand as w, eye as x, eyeOff as y };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { K as Keyboard } from './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\n\nconst KEYBOARD_DID_OPEN = 'ionKeyboardDidShow';\nconst KEYBOARD_DID_CLOSE = 'ionKeyboardDidHide';\nconst KEYBOARD_THRESHOLD = 150;\n// TODO(FW-2832): types\nlet previousVisualViewport = {};\nlet currentVisualViewport = {};\nlet keyboardOpen = false;\n/**\n * This is only used for tests\n */\nconst resetKeyboardAssist = () => {\n    previousVisualViewport = {};\n    currentVisualViewport = {};\n    keyboardOpen = false;\n};\nconst startKeyboardAssist = (win) => {\n    const nativeEngine = Keyboard.getEngine();\n    /**\n     * If the native keyboard plugin is available\n     * then we are running in a native environment. As a result\n     * we should only listen on the native events instead of\n     * using the Visual Viewport as the Ionic webview manipulates\n     * how it resizes such that the Visual Viewport API is not\n     * reliable here.\n     */\n    if (nativeEngine) {\n        startNativeListeners(win);\n    }\n    else {\n        if (!win.visualViewport) {\n            return;\n        }\n        currentVisualViewport = copyVisualViewport(win.visualViewport);\n        win.visualViewport.onresize = () => {\n            trackViewportChanges(win);\n            if (keyboardDidOpen() || keyboardDidResize(win)) {\n                setKeyboardOpen(win);\n            }\n            else if (keyboardDidClose(win)) {\n                setKeyboardClose(win);\n            }\n        };\n    }\n};\n/**\n * Listen for events fired by native keyboard plugin\n * in Capacitor/Cordova so devs only need to listen\n * in one place.\n */\nconst startNativeListeners = (win) => {\n    win.addEventListener('keyboardDidShow', (ev) => setKeyboardOpen(win, ev));\n    win.addEventListener('keyboardDidHide', () => setKeyboardClose(win));\n};\nconst setKeyboardOpen = (win, ev) => {\n    fireKeyboardOpenEvent(win, ev);\n    keyboardOpen = true;\n};\nconst setKeyboardClose = (win) => {\n    fireKeyboardCloseEvent(win);\n    keyboardOpen = false;\n};\n/**\n * Returns `true` if the `keyboardOpen` flag is not\n * set, the previous visual viewport width equal the current\n * visual viewport width, and if the scaled difference\n * of the previous visual viewport height minus the current\n * visual viewport height is greater than KEYBOARD_THRESHOLD\n *\n * We need to be able to accommodate users who have zooming\n * enabled in their browser (or have zoomed in manually) which\n * is why we take into account the current visual viewport's\n * scale value.\n */\nconst keyboardDidOpen = () => {\n    const scaledHeightDifference = (previousVisualViewport.height - currentVisualViewport.height) * currentVisualViewport.scale;\n    return (!keyboardOpen &&\n        previousVisualViewport.width === currentVisualViewport.width &&\n        scaledHeightDifference > KEYBOARD_THRESHOLD);\n};\n/**\n * Returns `true` if the keyboard is open,\n * but the keyboard did not close\n */\nconst keyboardDidResize = (win) => {\n    return keyboardOpen && !keyboardDidClose(win);\n};\n/**\n * Determine if the keyboard was closed\n * Returns `true` if the `keyboardOpen` flag is set and\n * the current visual viewport height equals the\n * layout viewport height.\n */\nconst keyboardDidClose = (win) => {\n    return keyboardOpen && currentVisualViewport.height === win.innerHeight;\n};\n/**\n * Dispatch a keyboard open event\n */\nconst fireKeyboardOpenEvent = (win, nativeEv) => {\n    const keyboardHeight = nativeEv ? nativeEv.keyboardHeight : win.innerHeight - currentVisualViewport.height;\n    const ev = new CustomEvent(KEYBOARD_DID_OPEN, {\n        detail: { keyboardHeight },\n    });\n    win.dispatchEvent(ev);\n};\n/**\n * Dispatch a keyboard close event\n */\nconst fireKeyboardCloseEvent = (win) => {\n    const ev = new CustomEvent(KEYBOARD_DID_CLOSE);\n    win.dispatchEvent(ev);\n};\n/**\n * Given a window object, create a copy of\n * the current visual and layout viewport states\n * while also preserving the previous visual and\n * layout viewport states\n */\nconst trackViewportChanges = (win) => {\n    previousVisualViewport = Object.assign({}, currentVisualViewport);\n    currentVisualViewport = copyVisualViewport(win.visualViewport);\n};\n/**\n * Creates a deep copy of the visual viewport\n * at a given state\n */\nconst copyVisualViewport = (visualViewport) => {\n    return {\n        width: Math.round(visualViewport.width),\n        height: Math.round(visualViewport.height),\n        offsetTop: visualViewport.offsetTop,\n        offsetLeft: visualViewport.offsetLeft,\n        pageTop: visualViewport.pageTop,\n        pageLeft: visualViewport.pageLeft,\n        scale: visualViewport.scale,\n    };\n};\n\nexport { KEYBOARD_DID_CLOSE, KEYBOARD_DID_OPEN, copyVisualViewport, keyboardDidClose, keyboardDidOpen, keyboardDidResize, resetKeyboardAssist, setKeyboardClose, setKeyboardOpen, startKeyboardAssist, trackViewportChanges };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\n\nvar ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\n\nvar KeyboardResize;\n(function (KeyboardResize) {\n    /**\n     * Only the `body` HTML element will be resized.\n     * Relative units are not affected, because the viewport does not change.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Body\"] = \"body\";\n    /**\n     * Only the `ion-app` HTML element will be resized.\n     * Use it only for Ionic Framework apps.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Ionic\"] = \"ionic\";\n    /**\n     * The whole native Web View will be resized when the keyboard shows/hides.\n     * This affects the `vh` relative unit.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Native\"] = \"native\";\n    /**\n     * Neither the app nor the Web View are resized.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n            return capacitor.Plugins.Keyboard;\n        }\n        return undefined;\n    },\n    getResizeMode() {\n        const engine = this.getEngine();\n        if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n            return Promise.resolve(undefined);\n        }\n        return engine.getResizeMode().catch((e) => {\n            if (e.code === ExceptionCode.Unimplemented) {\n                // If the native implementation is not available\n                // we treat it the same as if the plugin is not available.\n                return undefined;\n            }\n            throw e;\n        });\n    },\n};\n\nexport { Keyboard as K, KeyboardResize as a };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win, d as doc } from './index-a5d50daf.js';\nimport { K as Keyboard, a as KeyboardResize } from './keyboard-73175e24.js';\n\n/**\n * The element that resizes when the keyboard opens\n * is going to depend on the resize mode\n * which is why we check that here.\n */\nconst getResizeContainer = (resizeMode) => {\n    /**\n     * If doc is undefined then we are\n     * in an SSR environment, so the keyboard\n     * adjustment does not apply.\n     * If the webview does not resize then there\n     * is no container to resize.\n     */\n    if (doc === undefined || resizeMode === KeyboardResize.None || resizeMode === undefined) {\n        return null;\n    }\n    /**\n     * The three remaining resize modes: Native, Ionic, and Body\n     * all cause `ion-app` to resize, so we can listen for changes\n     * on that. In the event `ion-app` is not available then\n     * we can fall back to `body`.\n     */\n    const ionApp = doc.querySelector('ion-app');\n    return ionApp !== null && ionApp !== void 0 ? ionApp : doc.body;\n};\n/**\n * Get the height of ion-app or body.\n * This is used for determining if the webview\n * has resized before the keyboard closed.\n * */\nconst getResizeContainerHeight = (resizeMode) => {\n    const containerElement = getResizeContainer(resizeMode);\n    return containerElement === null ? 0 : containerElement.clientHeight;\n};\n/**\n * Creates a controller that tracks and reacts to opening or closing the keyboard.\n *\n * @internal\n * @param keyboardChangeCallback A function to call when the keyboard opens or closes.\n */\nconst createKeyboardController = async (keyboardChangeCallback) => {\n    let keyboardWillShowHandler;\n    let keyboardWillHideHandler;\n    let keyboardVisible;\n    /**\n     * This lets us determine if the webview content\n     * has resized as a result of the keyboard.\n     */\n    let initialResizeContainerHeight;\n    const init = async () => {\n        const resizeOptions = await Keyboard.getResizeMode();\n        const resizeMode = resizeOptions === undefined ? undefined : resizeOptions.mode;\n        keyboardWillShowHandler = () => {\n            /**\n             * We need to compute initialResizeContainerHeight right before\n             * the keyboard opens to guarantee the resize container is visible.\n             * The resize container may not be visible if we compute this\n             * as soon as the keyboard controller is created.\n             * We should only need to do this once to avoid additional clientHeight\n             * computations.\n             */\n            if (initialResizeContainerHeight === undefined) {\n                initialResizeContainerHeight = getResizeContainerHeight(resizeMode);\n            }\n            keyboardVisible = true;\n            fireChangeCallback(keyboardVisible, resizeMode);\n        };\n        keyboardWillHideHandler = () => {\n            keyboardVisible = false;\n            fireChangeCallback(keyboardVisible, resizeMode);\n        };\n        win === null || win === void 0 ? void 0 : win.addEventListener('keyboardWillShow', keyboardWillShowHandler);\n        win === null || win === void 0 ? void 0 : win.addEventListener('keyboardWillHide', keyboardWillHideHandler);\n    };\n    const fireChangeCallback = (state, resizeMode) => {\n        if (keyboardChangeCallback) {\n            keyboardChangeCallback(state, createResizePromiseIfNeeded(resizeMode));\n        }\n    };\n    /**\n     * Code responding to keyboard lifecycles may need\n     * to show/hide content once the webview has\n     * resized as a result of the keyboard showing/hiding.\n     * createResizePromiseIfNeeded provides a way for code to wait for the\n     * resize event that was triggered as a result of the keyboard.\n     */\n    const createResizePromiseIfNeeded = (resizeMode) => {\n        if (\n        /**\n         * If we are in an SSR environment then there is\n         * no window to resize. Additionally, if there\n         * is no resize mode or the resize mode is \"None\"\n         * then initialResizeContainerHeight will be 0\n         */\n        initialResizeContainerHeight === 0 ||\n            /**\n             * If the keyboard is closed before the webview resizes initially\n             * then the webview will never resize.\n             */\n            initialResizeContainerHeight === getResizeContainerHeight(resizeMode)) {\n            return;\n        }\n        /**\n         * Get the resize container so we can\n         * attach the ResizeObserver below to\n         * the correct element.\n         */\n        const containerElement = getResizeContainer(resizeMode);\n        if (containerElement === null) {\n            return;\n        }\n        /**\n         * Some part of the web content should resize,\n         * and we need to listen for a resize.\n         */\n        return new Promise((resolve) => {\n            const callback = () => {\n                /**\n                 * As per the spec, the ResizeObserver\n                 * will fire when observation starts if\n                 * the observed element is rendered and does not\n                 * have a size of 0 x 0. However, the watched element\n                 * may or may not have resized by the time this first\n                 * callback is fired. As a result, we need to check\n                 * the dimensions of the element.\n                 *\n                 * https://www.w3.org/TR/resize-observer/#intro\n                 */\n                if (containerElement.clientHeight === initialResizeContainerHeight) {\n                    /**\n                     * The resize happened, so stop listening\n                     * for resize on this element.\n                     */\n                    ro.disconnect();\n                    resolve();\n                }\n            };\n            /**\n             * In Capacitor there can be delay between when the window\n             * resizes and when the container element resizes, so we cannot\n             * rely on a 'resize' event listener on the window.\n             * Instead, we need to determine when the container\n             * element resizes using a ResizeObserver.\n             */\n            const ro = new ResizeObserver(callback);\n            ro.observe(containerElement);\n        });\n    };\n    const destroy = () => {\n        win === null || win === void 0 ? void 0 : win.removeEventListener('keyboardWillShow', keyboardWillShowHandler);\n        win === null || win === void 0 ? void 0 : win.removeEventListener('keyboardWillHide', keyboardWillHideHandler);\n        keyboardWillShowHandler = keyboardWillHideHandler = undefined;\n    };\n    const isKeyboardVisible = () => keyboardVisible;\n    await init();\n    return { init, destroy, isKeyboardVisible };\n};\n\nexport { createKeyboardController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Creates a lock controller.\n *\n * Claiming a lock means that nothing else can acquire the lock until it is released.\n * This can momentarily prevent execution of code that needs to wait for the earlier code to finish.\n * For example, this can be used to prevent multiple transitions from occurring at the same time.\n */\nconst createLockController = () => {\n    let waitPromise;\n    /**\n     * When lock() is called, the lock is claimed.\n     * Once a lock has been claimed, it cannot be claimed again until it is released.\n     * When this function gets resolved, the lock is released, allowing it to be claimed again.\n     *\n     * @example ```tsx\n     * const unlock = await this.lockController.lock();\n     * // do other stuff\n     * unlock();\n     * ```\n     */\n    const lock = async () => {\n        const p = waitPromise;\n        let resolve;\n        waitPromise = new Promise((r) => (resolve = r));\n        if (p !== undefined) {\n            await p;\n        }\n        return resolve;\n    };\n    return {\n        lock,\n    };\n};\n\nexport { createLockController as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst spinners = {\n    bubbles: {\n        dur: 1000,\n        circles: 9,\n        fn: (dur, index, total) => {\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            const angle = (2 * Math.PI * index) / total;\n            return {\n                r: 5,\n                style: {\n                    top: `${32 * Math.sin(angle)}%`,\n                    left: `${32 * Math.cos(angle)}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    circles: {\n        dur: 1000,\n        circles: 8,\n        fn: (dur, index, total) => {\n            const step = index / total;\n            const animationDelay = `${dur * step - dur}ms`;\n            const angle = 2 * Math.PI * step;\n            return {\n                r: 5,\n                style: {\n                    top: `${32 * Math.sin(angle)}%`,\n                    left: `${32 * Math.cos(angle)}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    circular: {\n        dur: 1400,\n        elmDuration: true,\n        circles: 1,\n        fn: () => {\n            return {\n                r: 20,\n                cx: 48,\n                cy: 48,\n                fill: 'none',\n                viewBox: '24 24 48 48',\n                transform: 'translate(0,0)',\n                style: {},\n            };\n        },\n    },\n    crescent: {\n        dur: 750,\n        circles: 1,\n        fn: () => {\n            return {\n                r: 26,\n                style: {},\n            };\n        },\n    },\n    dots: {\n        dur: 750,\n        circles: 3,\n        fn: (_, index) => {\n            const animationDelay = -(110 * index) + 'ms';\n            return {\n                r: 6,\n                style: {\n                    left: `${32 - 32 * index}%`,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    lines: {\n        dur: 1000,\n        lines: 8,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${(360 / total) * index + (index < total / 2 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 14,\n                y2: 26,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-small': {\n        dur: 1000,\n        lines: 8,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${(360 / total) * index + (index < total / 2 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 12,\n                y2: 20,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-sharp': {\n        dur: 1000,\n        lines: 12,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 17,\n                y2: 29,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n    'lines-sharp-small': {\n        dur: 1000,\n        lines: 12,\n        fn: (dur, index, total) => {\n            const transform = `rotate(${30 * index + (index < 6 ? 180 : -180)}deg)`;\n            const animationDelay = `${(dur * index) / total - dur}ms`;\n            return {\n                y1: 12,\n                y2: 20,\n                style: {\n                    transform: transform,\n                    'animation-delay': animationDelay,\n                },\n            };\n        },\n    },\n};\nconst SPINNERS = spinners;\n\nexport { SPINNERS as S };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as clamp } from './helpers-da915de8.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { createGesture } from './index-39782642.js';\nimport './gesture-controller-314a54f6.js';\n\nconst createSwipeBackGesture = (el, canStartHandler, onStartHandler, onMoveHandler, onEndHandler) => {\n    const win = el.ownerDocument.defaultView;\n    let rtl = isRTL(el);\n    /**\n     * Determine if a gesture is near the edge\n     * of the screen. If true, then the swipe\n     * to go back gesture should proceed.\n     */\n    const isAtEdge = (detail) => {\n        const threshold = 50;\n        const { startX } = detail;\n        if (rtl) {\n            return startX >= win.innerWidth - threshold;\n        }\n        return startX <= threshold;\n    };\n    const getDeltaX = (detail) => {\n        return rtl ? -detail.deltaX : detail.deltaX;\n    };\n    const getVelocityX = (detail) => {\n        return rtl ? -detail.velocityX : detail.velocityX;\n    };\n    const canStart = (detail) => {\n        /**\n         * The user's locale can change mid-session,\n         * so we need to check text direction at\n         * the beginning of every gesture.\n         */\n        rtl = isRTL(el);\n        return isAtEdge(detail) && canStartHandler();\n    };\n    const onMove = (detail) => {\n        // set the transition animation's progress\n        const delta = getDeltaX(detail);\n        const stepValue = delta / win.innerWidth;\n        onMoveHandler(stepValue);\n    };\n    const onEnd = (detail) => {\n        // the swipe back gesture has ended\n        const delta = getDeltaX(detail);\n        const width = win.innerWidth;\n        const stepValue = delta / width;\n        const velocity = getVelocityX(detail);\n        const z = width / 2.0;\n        const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n        const missing = shouldComplete ? 1 - stepValue : stepValue;\n        const missingDistance = missing * width;\n        let realDur = 0;\n        if (missingDistance > 5) {\n            const dur = missingDistance / Math.abs(velocity);\n            realDur = Math.min(dur, 540);\n        }\n        onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n    };\n    return createGesture({\n        el,\n        gestureName: 'goback-swipe',\n        /**\n         * Swipe to go back should have priority over other horizontal swipe\n         * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n         */\n        gesturePriority: 101,\n        threshold: 10,\n        canStart,\n        onStart: onStartHandler,\n        onMove,\n        onEnd,\n    });\n};\n\nexport { createSwipeBackGesture };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst watchForOptions = (containerEl, tagName, onChange) => {\n    if (typeof MutationObserver === 'undefined') {\n        return;\n    }\n    const mutation = new MutationObserver((mutationList) => {\n        onChange(getSelectedOption(mutationList, tagName));\n    });\n    mutation.observe(containerEl, {\n        childList: true,\n        subtree: true,\n    });\n    return mutation;\n};\nconst getSelectedOption = (mutationList, tagName) => {\n    let newOption;\n    mutationList.forEach((mut) => {\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < mut.addedNodes.length; i++) {\n            newOption = findCheckedOption(mut.addedNodes[i], tagName) || newOption;\n        }\n    });\n    return newOption;\n};\n/**\n * The \"value\" key is only set on some components such as ion-select-option.\n * As a result, we create a default union type of HTMLElement and the \"value\" key.\n * However, implementers are required to provide the appropriate component type\n * such as HTMLIonSelectOptionElement.\n */\nconst findCheckedOption = (node, tagName) => {\n    /**\n     * https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n     * The above check ensures \"node\" is an Element (nodeType 1).\n     */\n    if (node.nodeType !== 1) {\n        return undefined;\n    }\n    // HTMLElement inherits from Element, so we cast \"el\" as T.\n    const el = node;\n    const options = el.tagName === tagName.toUpperCase() ? [el] : Array.from(el.querySelectorAll(tagName));\n    return options.find((o) => o.value === el.value);\n};\n\nexport { watchForOptions as w };\n"], "names": ["signal", "computed", "SignalService", "constructor", "data", "imageData", "transformedData", "isEmpty", "length", "setData", "newData", "title", "formatDate", "update", "index", "page_index", "formattedDate_ToDay", "getDateAsToday", "push", "date", "localStorage", "removeItem", "getData", "currentData", "removeLastIndex", "original<PERSON>ength", "pop", "removeData", "splice", "for<PERSON>ach", "item", "i", "checkDataisEmpty", "removeAllData", "getDateAsDDD_MMM", "newDataDate", "Date", "daysOfWeek", "monthsOfYear", "formattedDate", "getDay", "getMonth", "currentDate", "diffInDays", "Math", "floor", "getTime", "getDate", "day", "padNumber", "month", "year", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "num", "toString", "padStart", "firstEntry", "extractTitleFromUrl", "url", "parts", "split", "getFormattedDate", "dateStr", "setImageData", "getImageData", "transformAndSetData", "set", "getTransformedData", "factory", "ɵfac", "providedIn", "w", "writeTask", "h", "hapticSelectionEnd", "a", "hapticSelectionStart", "b", "hapticSelectionChanged", "createGesture", "createButtonActiveGesture", "el", "isButton", "currentTouchedButton", "initialTouchedButton", "activateButtonAtPoint", "x", "y", "hapticFeedbackFn", "document", "target", "elementFromPoint", "disabled", "clearActiveButton", "setActiveButton", "button", "buttonToModify", "classList", "add", "dispatchClick", "remove", "click", "undefined", "<PERSON><PERSON><PERSON>", "threshold", "onStart", "ev", "currentX", "currentY", "onMove", "onEnd", "c", "win", "getCapacitor", "Capacitor", "g", "compareOptions", "currentValue", "compareValue", "compareWith", "Array", "isArray", "includes", "isOptionSelected", "some", "val", "isRTL", "hostEl", "dir", "toLowerCase", "ION_FOCUSED", "ION_FOCUSABLE", "FOCUS_KEYS", "startFocusVisible", "rootEl", "currentFocus", "keyboardMode", "ref", "shadowRoot", "root", "body", "setFocus", "elements", "pointerDown", "onKeydown", "key", "onFocusin", "<PERSON><PERSON><PERSON>", "toFocus", "filter", "contains", "onFocusout", "activeElement", "addEventListener", "passive", "destroy", "removeEventListener", "ImpactStyle", "NotificationType", "HapticEngine", "getEngine", "capacitor", "isPluginAvailable", "Plugins", "Haptics", "available", "engine", "getPlatform", "navigator", "vibrate", "impact", "options", "style", "notification", "type", "selection", "Light", "selectionStart", "selectionChanged", "selectionEnd", "hapticAvailable", "hapticSelection", "hapticImpact", "I", "d", "componentOnReady", "printRequiredElementError", "ION_CONTENT_TAG_NAME", "ION_CONTENT_ELEMENT_SELECTOR", "ION_CONTENT_CLASS_SELECTOR", "ION_CONTENT_SELECTOR", "isIonContent", "tagName", "getScrollElement", "_ref", "_asyncToGenerator", "Promise", "resolve", "_x", "apply", "arguments", "find<PERSON><PERSON><PERSON><PERSON>nt", "customContentHost", "querySelector", "findClosestIonContent", "closest", "scrollToTop", "durationMs", "content", "scrollTo", "top", "left", "behavior", "scrollByPoint", "scrollBy", "printIonContentErrorMsg", "disableContentScrollY", "contentEl", "ionContent", "initialScrollY", "scrollY", "setProperty", "resetContentScrollY", "removeProperty", "f", "p", "r", "s", "arrowBackSharp", "arrowDown", "caretBackSharp", "caretDownSharp", "caretUpSharp", "checkmarkOutline", "chevronBack", "chevronDown", "chevronExpand", "chevronForward", "chevronForwardOutline", "close", "closeCircle", "closeSharp", "ellipseOutline", "ellipsisHorizontal", "eye", "eyeOff", "menuOutline", "menuSharp", "removeOutline", "reorderThreeOutline", "reorderTwoSharp", "searchOutline", "searchSharp", "e", "j", "k", "l", "m", "n", "o", "q", "t", "u", "v", "K", "Keyboard", "KEYBOARD_DID_OPEN", "KEYBOARD_DID_CLOSE", "KEYBOARD_THRESHOLD", "previousVisualViewport", "currentVisualViewport", "keyboardOpen", "resetKeyboardAssist", "startKeyboardAssist", "nativeEngine", "startNativeListeners", "visualViewport", "copyVisualViewport", "onresize", "trackViewportChanges", "keyboardDidOpen", "keyboardDidResize", "setKeyboardOpen", "keyboardDidClose", "setKeyboardClose", "fireKeyboardOpenEvent", "fireKeyboardCloseEvent", "scaledHeightDifference", "height", "scale", "width", "innerHeight", "nativeEv", "keyboardHeight", "CustomEvent", "detail", "dispatchEvent", "Object", "assign", "round", "offsetTop", "offsetLeft", "pageTop", "pageLeft", "ExceptionCode", "KeyboardResize", "getResizeMode", "catch", "code", "Unimplemented", "doc", "getResizeContainer", "resizeMode", "None", "ionApp", "getResizeContainerHeight", "containerElement", "clientHeight", "createKeyboardController", "keyboardChangeCallback", "keyboardWillShowHandler", "keyboardWillHideHandler", "keyboardVisible", "initialResizeContainerHeight", "init", "_ref2", "resizeOptions", "mode", "fireChangeCallback", "state", "createResizePromiseIfNeeded", "callback", "ro", "disconnect", "ResizeObserver", "observe", "isKeyboardVisible", "createLockController", "waitPromise", "lock", "spinners", "bubbles", "dur", "circles", "fn", "total", "animationDelay", "angle", "PI", "sin", "cos", "step", "circular", "elmDuration", "cx", "cy", "fill", "viewBox", "transform", "crescent", "dots", "_", "lines", "y1", "y2", "SPINNERS", "S", "clamp", "createSwipeBackGesture", "canStartHandler", "onStartHandler", "onMoveHandler", "onEndHandler", "ownerDocument", "defaultView", "rtl", "isAtEdge", "startX", "innerWidth", "getDeltaX", "deltaX", "getVelocityX", "velocityX", "canStart", "delta", "<PERSON><PERSON><PERSON><PERSON>", "velocity", "z", "shouldComplete", "missing", "missingDistance", "realDur", "abs", "min", "gesturePriority", "watchForOptions", "containerEl", "onChange", "MutationObserver", "mutation", "mutationList", "getSelectedOption", "childList", "subtree", "newOption", "mut", "addedNodes", "findCheckedOption", "node", "nodeType", "toUpperCase", "from", "querySelectorAll", "find", "value"], "sourceRoot": "webpack:///", "x_google_ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}