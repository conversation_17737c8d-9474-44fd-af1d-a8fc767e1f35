{"version": 3, "file": "src_app_doc-list_doc-list_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAET;;;AAE9C,MAAME,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,uDAAWA;CACvB,CACF;AAMK,MAAOI,wBAAwB;4BAAxBA,wBAAwB;;mBAAxBA,yBAAwB;AAAA;;QAAxBA;AAAwB;;YAHzBL,yDAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,yDAAY;AAAA;;sHAEXK,wBAAwB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFzBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACbuB;AACF;AAEA;AAEwB;AAEvB;AACS,CAAC;;AAalD,MAAOc,iBAAiB;qBAAjBA,iBAAiB;;mBAAjBA,kBAAiB;AAAA;;QAAjBA;AAAiB;;YAT1BJ,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,8EAAwB,EACxBQ,+DAAY;AAAA;;sHAKHC,iBAAiB;IAAAC,YAAA,GAFbd,uDAAW;IAAAM,OAAA,GAPxBG,yDAAY,EACZC,uDAAW,EACXC,uDAAW,EACXP,8EAAwB,EACxBQ,+DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVO;AAC4D;AACvD;AAC+B;AAEN,CAAE;AAKI;;;;;;;;;;;;;;;;;;ICyBnDU,6DAFJ,2BAAwF,eAC5E,wBACoB;IAC1BA,wDAAA,cAAoC;IACtCA,2DAAA,EAAgB;IAEdA,6DADF,gBAAW,SACL;IAAAA,qDAAA,GAAiB;IAAAA,2DAAA,EAAK;IAC1BA,6DAAA,QAAG;IAAAA,qDAAA,GAAgB;IACrBA,2DADqB,EAAI,EACb;IACZA,6DAAA,cAAwB;IAAAA,qDAAA,IAA2B;IACrDA,2DADqD,EAAM,EAChD;IAETA,6DADF,4BAA6B,2BACiC;IAA3CA,yDAAA,mBAAAM,2EAAA;MAAA,MAAAC,IAAA,GAAAP,4DAAA,CAAAS,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAX,4DAAA;MAAA,OAAAA,0DAAA,CAASW,MAAA,CAAAG,UAAA,CAAAP,IAAA,CAAa;IAAA,EAAC;IACtCP,6DAAA,eAAiC;IAC/BA,wDAAA,oBAAwD;IACxDA,6DAAA,YAAM;IAAAA,qDAAA,gBAAQ;IAElBA,2DAFkB,EAAO,EACjB,EACU;IAClBA,6DAAA,2BAA0E;IAAzDA,yDAAA,mBAAAe,2EAAA;MAAA,MAAAR,IAAA,GAAAP,4DAAA,CAAAS,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAX,4DAAA;MAAA,OAAAA,0DAAA,CAASW,MAAA,CAAAK,iBAAA,CAAAT,IAAA,CAAoB;IAAA,EAAC;IAC7CP,6DAAA,eAAiC;IAC/BA,wDAAA,oBAA+C;IAC/CA,6DAAA,YAAM;IAAAA,qDAAA,iBAAS;IAIvBA,2DAJuB,EAAO,EAClB,EACU,EACD,EACF;;;;IAtBRA,wDAAA,GAA4B;IAA5BA,yDAAA,QAAAmB,QAAA,CAAAC,cAAA,EAAApB,4DAAA,CAA4B;IAG7BA,wDAAA,GAAiB;IAAjBA,gEAAA,CAAAmB,QAAA,CAAAI,KAAA,CAAiB;IAClBvB,wDAAA,GAAgB;IAAhBA,gEAAA,CAAAmB,QAAA,CAAAK,IAAA,CAAgB;IAEGxB,wDAAA,GAA2B;IAA3BA,iEAAA,UAAAmB,QAAA,CAAAO,UAAA,KAA2B;;;AD9B3D,IAAKC,OAGJ;AAHD,WAAKA,OAAO;EACVA,OAAA,yBAAqB;EACrBA,OAAA,uCAAmC;AACrC,CAAC,EAHIA,OAAO,KAAPA,OAAO;AAUN,MAAOjD,WAAW;EAiBtBkD,YACUC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B;IAF9B,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IAdxB,KAAAC,UAAU,GAAqB,EAAE;IACjC,KAAAC,OAAO,GAAGxC,sDAAM,CAACC,0DAAa,CAAC;IAC/B,KAAAwC,aAAa,GAAGzC,sDAAM,CAACI,mEAAa,CAAC;IACrC,KAAAsC,UAAU,GAAG1C,sDAAM,CAACK,6DAAU,CAAC;IAC/B,KAAAsC,iBAAiB,GAAG3C,sDAAM,CAACE,8DAAiB,CAAC;IAE7C,KAAA0C,QAAQ,GAAG,CAAC;IACZ,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,IAAI,CAAC,CAAC;IAQlB;EACF;EAEMC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,6OAAA;MACnBD,KAAI,CAACE,gBAAgB,EAAE;IAAC;EAC1B;EAEMC,QAAQA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAH,6OAAA;MACZ,IAAIG,MAAI,CAACX,aAAa,CAACY,OAAO,EAAE,IAAI,IAAI,IAAID,MAAI,CAACX,aAAa,CAACY,OAAO,EAAE,IAAIC,SAAS,IAAIF,MAAI,CAACX,aAAa,CAACY,OAAO,EAAE,CAACE,MAAM,IAAI,CAAC,EAAE;QACjIH,MAAI,CAACZ,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;OACtC,MAAM;QAAA,IAAAC,qBAAA;QACLL,MAAI,CAACb,UAAU,GAAGa,MAAI,CAACX,aAAa,CAACY,OAAO,EAAE;QAC9C,CAAAI,qBAAA,GAAAL,MAAI,CAACM,eAAe,cAAAD,qBAAA,gBAAAA,qBAAA,GAApBA,qBAAA,CAAsBE,aAAa,CAACC,MAAM,cAAAH,qBAAA,eAA1CA,qBAAA,CAA4CI,MAAM,EAAE;QAEpDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEX,MAAI,CAACb,UAAU,CAAC;;MAG9C;MACAa,MAAI,CAACd,cAAc,CAAC0B,gBAAgB,EAAE,CAACC,SAAS,CAAEC,SAAkB,IAAI;QACtEd,MAAI,CAACN,WAAW,GAAGoB,SAAS;MAC9B,CAAC,CAAC;MAEF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MAEA;IAAA;EACF;EAEMhB,gBAAgBA,CAAA;IAAA,IAAAiB,MAAA;IAAA,OAAAlB,6OAAA;MAAA,IAAAmB,qBAAA;MACpBD,MAAI,CAACP,MAAM,GAAG,IAAIzD,8CAAM,EAAAiE,qBAAA,GAACD,MAAI,CAACT,eAAe,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBT,aAAa,EAAE;QAC5DU,MAAM,EAAE,OAAO;QACfC,UAAU,EAAE,IAAI;QAChBC,EAAE,EAAE;UACFC,IAAI,EAAEA,CAAA,KAAK;YACTV,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAI,CAACP,MAAM,CAAC;UAChD;;OAEH,CAAC;IAAC;EACL;EAEAa,OAAOA,CAAA;IACL,IAAI,CAACjC,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;EACvC;EACAkB,OAAOA,CAAA;IACL,IAAI,CAAClC,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;EACvC;EAEMnC,UAAUA,CAACJ,KAAa;IAAA,IAAA0D,MAAA;IAAA,OAAA1B,6OAAA;MAC5B,MAAM2B,KAAK,SAASD,MAAI,CAACvC,eAAe,CAACyC,MAAM,CAAC;QAC9CC,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,CACN;UACEC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAEP,MAAI,CAACpC,UAAU,CAACtB,KAAK,CAAC,CAACa,KAAK;UACnCqD,WAAW,EAAE;SACd,CACF;QACDC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,UAAU;UAChBE,QAAQ,EAAE,uCAAuC;UACjDC,OAAO,EAAGC,IAAI,IAAI;YAChBd,MAAI,CAACpC,UAAU,CAACtB,KAAK,CAAC,CAACa,KAAK,GAAG2D,IAAI,CAACC,QAAQ;UAC9C;SACD;OAEJ,CAAC;MAEF,MAAMd,KAAK,CAACe,OAAO,EAAE;IAAC;EACxB;EAEMpE,iBAAiBA,CAACN,KAAa;IAAA,IAAA2E,MAAA;IAAA,OAAA3C,6OAAA;MACnC,MAAM2B,KAAK,SAASgB,MAAI,CAACxD,eAAe,CAACyC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/Be,OAAO,EAAE,kDAAkD;QAC3DT,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZI,MAAI,CAACE,WAAW,CAAC7E,KAAK,CAAC;YACvB,IAAI2E,MAAI,CAACrD,UAAU,CAACgB,MAAM,IAAI,CAAC,EAAE;cAC/BwC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;cAC3CJ,MAAI,CAACpD,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;;UAEzC;SACD;OAEJ,CAAC;MAEF,MAAMoB,KAAK,CAACe,OAAO,EAAE;IAAC;EACxB;EAEMG,WAAWA,CAAC7E,KAAa;IAAA,IAAAgF,MAAA;IAAA,OAAAhD,6OAAA;MAAA,IAAAiD,qBAAA,EAAAC,qBAAA;MAC7B,MAAMC,oBAAoB,IAAAF,qBAAA,GAAGD,MAAI,CAACvC,eAAe,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBvC,aAAa,CAACC,MAAM;MACvEwC,oBAAoB,CAACN,WAAW,CAAC7E,KAAK,CAAC;MACvCmF,oBAAoB,CAACvC,MAAM,EAAE;MAC7B,CAAAsC,qBAAA,GAAAC,oBAAoB,CAACC,OAAO,CAACpF,KAAK,GAAG,CAAC,CAAC,cAAAkF,qBAAA,cAAAA,qBAAA,GAAIlF,KAAK,GAAG,CAAC;MACpDgF,MAAI,CAAC1D,UAAU,CAAC+D,MAAM,CAACrF,KAAK,EAAE,CAAC,CAAC;MAChC6C,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEqC,oBAAoB,CAAC;IAAC;EACxE;EAEMG,SAASA,CAACtF,KAAa;IAAA,IAAAuF,MAAA;IAAA,OAAAvD,6OAAA;MAC3B,MAAM2B,KAAK,SAAS4B,MAAI,CAACpE,eAAe,CAACyC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/Be,OAAO,EAAE,kDAAkD;QAC3DT,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAAiB,qBAAA,EAAAC,sBAAA;YACZF,MAAI,CAAC/D,aAAa,CAACkE,UAAU,CAAC1F,KAAK,CAAC;YACpCuF,MAAI,CAACjE,UAAU,GAAGiE,MAAI,CAAC/D,aAAa,CAACY,OAAO,EAAE;YAC9C,CAAAoD,qBAAA,GAAAD,MAAI,CAAC9C,eAAe,cAAA+C,qBAAA,gBAAAA,qBAAA,GAApBA,qBAAA,CAAsB9C,aAAa,CAACC,MAAM,cAAA6C,qBAAA,eAA1CA,qBAAA,CAA4CX,WAAW,CAAC7E,KAAK,CAAC;YAC9D,CAAAyF,sBAAA,GAAAF,MAAI,CAAC9C,eAAe,cAAAgD,sBAAA,gBAAAA,sBAAA,GAApBA,sBAAA,CAAsB/C,aAAa,CAACC,MAAM,cAAA8C,sBAAA,eAA1CA,sBAAA,CAA4C7C,MAAM,EAAE;YACpD,IAAI2C,MAAI,CAACjE,UAAU,CAACgB,MAAM,IAAI,CAAC,EAAE;cAC/BwC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;cAC3CQ,MAAI,CAAChE,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;;UAGzC;SACD;OAEJ,CAAC;MAEF,MAAMoB,KAAK,CAACe,OAAO,EAAE;IAAC;EACxB;EAEMiB,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5D,6OAAA;MAChB,MAAM2B,KAAK,SAASiC,MAAI,CAACzE,eAAe,CAACyC,MAAM,CAAC;QAC9CC,MAAM,EAAE,uBAAuB;QAC/Be,OAAO,EAAE,yDAAyD;QAClET,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YACZ1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC/B;SACD,EACD;UACEsB,IAAI,EAAE,kBAAkB;UACxBE,QAAQ,EAAE,4BAA4B;UACtCC,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAAsB,qBAAA,EAAAC,sBAAA;YACZF,MAAI,CAACpE,aAAa,CAACuE,aAAa,EAAE;YAClCH,MAAI,CAACtE,UAAU,GAAG,EAAE;YACpB,CAAAuE,qBAAA,GAAAD,MAAI,CAACnD,eAAe,cAAAoD,qBAAA,gBAAAA,qBAAA,GAApBA,qBAAA,CAAsBnD,aAAa,CAACC,MAAM,cAAAkD,qBAAA,eAA1CA,qBAAA,CAA4CG,eAAe,EAAE;YAC7D,CAAAF,sBAAA,GAAAF,MAAI,CAACnD,eAAe,cAAAqD,sBAAA,gBAAAA,sBAAA,GAApBA,sBAAA,CAAsBpD,aAAa,CAACC,MAAM,cAAAmD,sBAAA,eAA1CA,sBAAA,CAA4ClD,MAAM,EAAE;YACpDkC,YAAY,CAACC,UAAU,CAAC,kBAAkB,CAAC;YAC3C;YACAa,MAAI,CAACrE,OAAO,CAACgB,YAAY,CAAC,UAAU,CAAC;UACvC;SACD;OAEJ,CAAC;MAEF,MAAMoB,KAAK,CAACe,OAAO,EAAE;IAAC;EACxB;EAEMuB,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlE,6OAAA;MAAA,IAAAmE,qBAAA;MAEnBD,MAAI,CAACE,KAAK,GAAGF,MAAI,CAACzE,UAAU,CAAC4E,aAAa,EAAE,CAAC,CAAC;MAC9C,MAAMC,YAAY,GAAG,GAAGjH,qEAAW,CAACkH,YAAY,IAAIL,MAAI,CAACE,KAAK,EAAE;MAChEvD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwD,YAAY,CAAC;MAEpDJ,MAAI,CAAC9E,gBAAgB,CAACoF,OAAO,CAACF,YAAY,EAAEJ,MAAI,CAACE,KAAK,CAAC;MAEvDF,MAAI,CAAC9E,gBAAgB,CAACqF,SAAS,CAACP,MAAI,CAACE,KAAK,CAAC,CAACpD,SAAS,CAAE4B,OAAO,IAAI;QAChE,IAAIA,OAAO,CAACjD,QAAQ,KAAKU,SAAS,EAAE;UAClC6D,MAAI,CAACvE,QAAQ,GAAGiD,OAAO,CAACjD,QAAQ;UAChCkB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoD,MAAI,CAACvE,QAAQ,CAAC;;MAE/C,CAAC,CAAC;MAEF;MACAuE,MAAI,CAACtE,SAAS,GAAG,IAAI;MACrB,MAAM8E,IAAI,GAAGR,MAAI;MAEjB;MACA,IAAIA,MAAI,CAAC5E,UAAU,CAACqF,IAAI,CAACnC,IAAI,IAAIA,IAAI,CAACoC,aAAa,IAAIvE,SAAS,IAAImC,IAAI,CAACqC,SAAS,IAAIxE,SAAS,IAAImC,IAAI,CAACoC,aAAa,IAAI,EAAE,IAAIpC,IAAI,CAACqC,SAAS,IAAI,EAAE,CAAC,EAAE;QACpJ,MAAMC,YAAY,GAAG;;;;;;OAMpB;QACDZ,MAAI,CAACzE,UAAU,CAACsF,cAAc,CAACD,YAAY,CAAC;QAC5CZ,MAAI,CAACtE,SAAS,GAAG,KAAK;QACtB;;MAGF;MACA,MAAMoF,cAAc,GAAG,EAAAb,qBAAA,GAACD,MAAI,CAAC5E,UAAU,CAAC,CAAC,CAAC,CAACuF,SAAS,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAEc,QAAQ,EAAE;MAEtE,MAAMC,cAAc,GAAgBhB,MAAI,CAAC5E,UAAU,CAAC6F,GAAG,CAAC3C,IAAI,IAAG;QAAA,IAAA4C,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA;QAC7D;QACA,IAAIC,SAAiB;QACrB,IAAIhD,IAAI,CAACoC,aAAa,EAAE;UACtBY,SAAS,GAAGhD,IAAI,CAACoC,aAAa,CAACa,WAAW,EAAE;UAC5C,IAAID,SAAS,KAAK,SAAS,EAAE;YAC3BA,SAAS,GAAI1C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI,WAAW,IAAI5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,GAAI,EAAE,GAAG5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,GAAG,QAAQ;;SAEtO,MAAM;UACLF,SAAS,GAAI1C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI,WAAW,IAAI5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,GAAI,EAAE,GAAG5C,YAAY,CAAC4C,OAAO,CAAC,kBAAkB,CAAC,GAAG,QAAQ;;QAGrO;QACA,IAAIC,mBAAmB,GAAG7C,YAAY,CAAC4C,OAAO,CAAC,qBAAqB,CAAC,IAAI,MAAM,GAAG,IAAI,GAAG,KAAK;QAE9F;QAEA;QACA;QACA;QACA;QACA;QAEA,MAAME,QAAQ,IAAAR,qBAAA,IAAAC,mBAAA,GAAI7C,IAAI,CAACqD,aAAa,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBS,SAAS,EAAAR,oBAAA,GAAC9C,IAAI,CAACqD,aAAa,cAAAP,oBAAA,uBAAlBA,oBAAA,CAAoBS,OAAO,CAAC,mBAAmB,CAAC,CAAC,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAG,CAAC,CAAC;QAC1GvE,OAAO,CAACC,GAAG,CAAC,gBAAgB,GAAAyE,mBAAA,GAAE/C,IAAI,CAACoC,aAAa,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBS,iBAAiB,EAAE,CAAC;QACtEnF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE8E,QAAQ,CAAC;QACnC,OAAO;UACLK,KAAK,EAAEL,QAAQ;UACf;UACAM,UAAU,EAAEV,SAAS;UACrBX,SAAS,EAAErC,IAAI,CAACqC;SACjB;MACH,CAAC,CAAC;MAEFhE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEqF,IAAI,CAACC,SAAS,CAAClB,cAAc,CAAC,CAAC;MAGhE;MACA,MAAMmB,cAAc,GAAIvD,YAAY,CAAC4C,OAAO,CAAC,SAAS,CAAa,IAAIzG,OAAO,CAACqH,eAAe;MAE9FpC,MAAI,CAACzE,UAAU,CAACwE,eAAe,CAACiB,cAAc,EAAEhB,MAAI,CAACE,KAAK,EAAEY,cAAc,EAAEqB,cAAc,CAAC,CAACrF,SAAS,CAClGuF,QAAQ,IAAI;QACX1F,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyF,QAAQ,CAAC;QAC5C,IAAI;UACFrC,MAAI,CAAC1E,aAAa,CAACgH,mBAAmB,CAACtC,MAAI,CAACsC,mBAAmB,CAACD,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;UACvF;UACArC,MAAI,CAACtE,SAAS,GAAG,KAAK;UACtB;UACAsE,MAAI,CAAC3E,OAAO,CAACkH,eAAe,CAAC,kBAAkB,EAAE;YAAEC,KAAK,EAAE;cAAEC,KAAK,EAAEJ,QAAQ,CAAC,OAAO,CAAC;cAAE3B,aAAa,EAAEM,cAAc,CAAC,CAAC,CAAC,CAACgB;YAAU;UAAE,CAAE,CAAC,CAAC,CAAC;UAExI;UACAhC,MAAI,CAAC9E,gBAAgB,CAACwH,KAAK,CAAC1C,MAAI,CAACE,KAAM,CAAC;SACzC,CACD,OAAOyC,CAAC,EAAE;UACR,MAAM/B,YAAY,GAAG;;;;;;;;;WASpB;UACDZ,MAAI,CAACzE,UAAU,CAACsF,cAAc,CAACD,YAAY,CAAC;UAC5CZ,MAAI,CAACtE,SAAS,GAAG,KAAK;UACtBsE,MAAI,CAACvE,QAAQ,GAAG,CAAC;UACjBuE,MAAI,CAAC3E,OAAO,CAACgB,YAAY,CAAC,gBAAgB,CAAC;;MAI/C,CAAC,EACAuG,KAAK,IAAI;QACR;QACA5C,MAAI,CAACtE,SAAS,GAAG,KAAK;QACtB,MAAMkF,YAAY,GAAG;;;;;;;;;SASpB;QACDZ,MAAI,CAACzE,UAAU,CAACsF,cAAc,CAACD,YAAY,CAAC;QAC5CjE,OAAO,CAACiG,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClCjG,OAAO,CAACiG,KAAK,CAAChC,YAAY,CAAC;QAE3B;QACAZ,MAAI,CAAC9E,gBAAgB,CAACwH,KAAK,CAAC1C,MAAI,CAACE,KAAM,CAAC;MAC1C,CAAC,CAAC;MAEJF,MAAI,CAAC9E,gBAAgB,CAACqF,SAAS,CAACP,MAAI,CAACE,KAAK,CAAC,CAACpD,SAAS,CAAE4B,OAAO,IAAI;QAChE,IAAIA,OAAO,CAACjD,QAAQ,KAAKU,SAAS,EAAE;UAClC6D,MAAI,CAACvE,QAAQ,GAAGiD,OAAO,CAACjD,QAAQ;;MAEpC,CAAC,CAAC;IAAC;EACL;EAEMoH,cAAcA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAhH,6OAAA;MAClB,MAAMiH,OAAO,SAASD,OAAI,CAACtH,iBAAiB,CAACkC,MAAM,CAAC;QAClDgB,OAAO,EAAE,eAAe;QACxBsE,OAAO,EAAE;QACT;OACD,CAAC;MACF,MAAMD,OAAO,CAACvE,OAAO,EAAE;MACvB,OAAOuE,OAAO;IAAC;EACjB;EAGAT,mBAAmBA,CAACW,YAAiB;IACnC,IAAI;MACF,MAAMC,eAAe,GAAyBD,YAAY,CAAChC,GAAG,CAAC,CAACkC,IAAS,EAAErJ,KAAa,KAAI;QAAA,IAAAsJ,UAAA;QAC1F,MAAMC,OAAO,IAAAD,UAAA,GAAGD,IAAI,CAAC7E,IAAI,cAAA8E,UAAA,uBAATA,UAAA,CAAWC,OAAO;QAClC,MAAMC,QAAQ,GAAGH,IAAI,CAAC7E,IAAI,CAACiF,KAAK,CAC7BC,MAAM,CAAEC,OAAY,IACnBA,OAAO,CAACC,WAAW,IACnBD,OAAO,CAACE,QAAQ,IAChBF,OAAO,CAACG,QAAQ,IAChBH,OAAO,CAACI,GAAG,IACXJ,OAAO,CAACK,GAAG,IACXL,OAAO,CAACM,SAAS,CAClB,CAAC;QAAA,CACD9C,GAAG,CAAEwC,OAAY,KAAM;UACtBC,WAAW,EAAED,OAAO,CAACC,WAAW;UAChCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,GAAGK,QAAQ,CAACP,OAAO,CAACE,QAAQ,CAAC,GAAG,IAAI;UAC9DM,UAAU,EAAER,OAAO,CAACG,QAAQ,IAAI,IAAI;UACpCC,GAAG,EAAEJ,OAAO,CAACI,GAAG,GAAGK,UAAU,CAACT,OAAO,CAACI,GAAG,CAAC,GAAG,IAAI;UACjDC,GAAG,EAAEL,OAAO,CAACK,GAAG,GAAGI,UAAU,CAACT,OAAO,CAACK,GAAG,CAAC,GAAG,IAAI;UACjDK,KAAK,EAAEV,OAAO,CAACM,SAAS,GAAGG,UAAU,CAACT,OAAO,CAACM,SAAS,CAAC,GAAG;SAC5D,CAAC,CAAC;QACL,OAAO;UACLhC,KAAK,EAAEsB,OAAO,CAACe,eAAe,CAACC,MAAM;UACrC1J,KAAK,EAAE,QAAQ,IAAI,CAACW,aAAa,CAACgJ,UAAU,EAAE,EAAE;UAChD1J,IAAI,EAAE,IAAI,CAACU,aAAa,CAACiJ,gBAAgB,CAAClB,OAAO,CAACmB,WAAW,CAAC;UAC9D1J,UAAU,EAAEhB,KAAK,GAAG,CAAC;UACrBwJ;SACD;MACH,CAAC,CAAC;MAEF,OAAOJ,eAAe;KAEvB,CAAC,OAAOP,CAAC,EAAE;MACVhG,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE+F,CAAC,CAAC;MACxB,MAAM/B,YAAY,GAAG;;;;;;;;;OASpB;MACD,IAAI,CAACrF,UAAU,CAACsF,cAAc,CAACD,YAAY,CAAC;MAC5C,IAAI,CAAClF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACD,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACJ,OAAO,CAACgB,YAAY,CAAC,gBAAgB,CAAC;MAE3C,OAAO,EAAE;;EAEb;;eA7ZWvE,WAAW;;mBAAXA,YAAW,EAAAsB,gEAAA,CAAAf,4DAAA,GAAAe,gEAAA,CAAAuL,yEAAA,GAAAvL,gEAAA,CAAAyL,qEAAA;AAAA;;QAAX/M,YAAW;EAAAiN,SAAA;EAAAC,SAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MC3BpB9L,6DAFJ,oBAA+C,kBAChC,gBACA;MAAAA,qDAAA,sBAAe;MAAAA,2DAAA,EAAY;MAEpCA,6DADF,qBAAwB,oBACU;MAApBA,yDAAA,mBAAAgM,iDAAA;QAAA,OAASD,GAAA,CAAA5H,OAAA,EAAS;MAAA,EAAC;MAC7BnE,wDAAA,yBAGmB;MAI3BA,2DAHM,EAAa,EACD,EACF,EACH;MAEbA,6DAAA,qBAAyE;MACvEA,wDAAA,wBAAuC;MAuBvCA,6DAAA,YAA0B;MAAAA,qDAAA,aAAK;MAAAA,2DAAA,EAAK;MACpCA,6DAAA,cAAsB;MACpBA,yDAAA,KAAAkM,wCAAA,+BAAwF;MA2B5FlM,2DADE,EAAM,EACM;MAIdA,6DAAA,cAA+D;MAC7DA,wDAAA,2BAA2D;MAC7DA,2DAAA,EAAM;MAKAA,6DAHN,qBAA+C,mBAChC,mBACE,sBACyE;MAClFA,wDAAA,2BAAgD;MAClDA,2DAAA,EAAa;MACbA,6DAAA,sBAAmE;MAA5BA,yDAAA,mBAAAmM,kDAAA;QAAA,OAASJ,GAAA,CAAApF,eAAA,EAAiB;MAAA,EAAC;MAChE3G,wDAAA,2BAAkD;MAClDA,6DAAA,YAAM;MAAAA,qDAAA,gBAAQ;MAChBA,2DADgB,EAAO,EACV;MACbA,6DAAA,sBAAsE;MAAzBA,yDAAA,mBAAAoM,kDAAA;QAAA,OAASL,GAAA,CAAA1F,YAAA,EAAc;MAAA,EAAC;MACnErG,wDAAA,2BAAiD;MAIzDA,2DAHM,EAAa,EACD,EACF,EACH;;;MA1FDA,yDAAA,YAAAA,8DAAA,IAAAsM,GAAA,EAAAP,GAAA,CAAAzJ,SAAA,EAAkC;MAcjCtC,wDAAA,GAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,IAAAsM,GAAA,EAAAP,GAAA,CAAAzJ,SAAA,EAAkC;MA0BetC,wDAAA,GAAe;MAAfA,yDAAA,YAAA+L,GAAA,CAAA/J,UAAA,CAAe;MA+BjDhC,wDAAA,EAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,KAAAsM,GAAA,EAAAP,GAAA,CAAAzJ,SAAA,EAAkC;MAC1CtC,wDAAA,EAAqB;MAArBA,yDAAA,aAAA+L,GAAA,CAAA1J,QAAA,CAAqB;MAG7BrC,wDAAA,EAAkC;MAAlCA,yDAAA,YAAAA,8DAAA,KAAAsM,GAAA,EAAAP,GAAA,CAAAzJ,SAAA,EAAkC;MAGYtC,wDAAA,GAA+B;MAA/BA,yDAAA,eAAAA,8DAAA,KAAAwM,GAAA,EAA+B;;;;;;;;;;;;;;;;;;;;;;AC7E9C;;AAMrC,MAAOhB,gBAAgB;EAI3B5J,YAAA;IAFQ,KAAA8K,QAAQ,GAAsC,EAAE;EAGxD;EAEOxF,OAAOA,CAACyF,GAAW,EAAE7F,KAAa;IACvC,IAAI,CAAC8F,MAAM,GAAG,IAAIC,SAAS,CAACF,GAAG,CAAC;IAEhC,IAAI,CAACC,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAI;MAC7BxJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEuJ,KAAK,CAAC;IACzD,CAAC;IAED,IAAI,CAACH,MAAM,CAACI,SAAS,GAAID,KAAK,IAAI;MAChC,MAAM7H,IAAI,GAAG2D,IAAI,CAACoE,KAAK,CAACF,KAAK,CAAC7H,IAAI,CAAC;MACnC,IAAIA,IAAI,CAACgI,MAAM,IAAI,IAAI,CAACR,QAAQ,CAACxH,IAAI,CAACgI,MAAM,CAAC,EAAE;QAC7C,IAAI,CAACR,QAAQ,CAACxH,IAAI,CAACgI,MAAM,CAAC,CAACC,IAAI,CAACjI,IAAI,CAAC;OACtC,MAAM,IAAI,IAAI,CAACwH,QAAQ,CAAC5F,KAAK,CAAC,EAAE;QAC/B,IAAI,CAAC4F,QAAQ,CAAC5F,KAAK,CAAC,CAACqG,IAAI,CAACjI,IAAI,CAAC;;IAEnC,CAAC;IAED,IAAI,CAAC0H,MAAM,CAACQ,OAAO,GAAIL,KAAK,IAAI;MAC9BxJ,OAAO,CAACiG,KAAK,CAAC,2BAA2B,EAAEuD,KAAK,CAAC;MACjD,IAAI,CAACM,SAAS,CAACV,GAAG,EAAE7F,KAAK,CAAC;IAC5B,CAAC;IAED,IAAI,CAAC8F,MAAM,CAACU,OAAO,GAAIP,KAAK,IAAI;MAC9BxJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuJ,KAAK,CAAC;MAClD,IAAI,CAACM,SAAS,CAACV,GAAG,EAAE7F,KAAK,CAAC;IAC5B,CAAC;EACH;EAEQuG,SAASA,CAACV,GAAW,EAAE7F,KAAa;IAC1CyG,UAAU,CAAC,MAAK;MACd,IAAI,CAACrG,OAAO,CAACyF,GAAG,EAAE7F,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEO0G,IAAIA,CAACtI,IAAS;IAAA,IAAAuI,YAAA;IACnB,IAAI,EAAAA,YAAA,OAAI,CAACb,MAAM,cAAAa,YAAA,uBAAXA,YAAA,CAAaC,UAAU,MAAKb,SAAS,CAACc,IAAI,EAAE;MAAA,IAAAC,aAAA;MAC9C,CAAAA,aAAA,OAAI,CAAChB,MAAM,cAAAgB,aAAA,eAAXA,aAAA,CAAaJ,IAAI,CAAC3E,IAAI,CAACC,SAAS,CAAC5D,IAAI,CAAC,CAAC;KACxC,MAAM;MACL3B,OAAO,CAACiG,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEOrC,SAASA,CAACL,KAAa;IAC5B,IAAI,CAAC,IAAI,CAAC4F,QAAQ,CAAC5F,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC4F,QAAQ,CAAC5F,KAAK,CAAC,GAAG,IAAI2F,yCAAO,EAAO;;IAE3C,OAAO,IAAI,CAACC,QAAQ,CAAC5F,KAAK,CAAC,CAAC+G,YAAY,EAAE;EAC5C;EAEOvE,KAAKA,CAACxC,KAAa;IACxB,IAAI,IAAI,CAAC8F,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACtD,KAAK,EAAE;MACnB,OAAO,IAAI,CAACoD,QAAQ,CAAC5F,KAAK,CAAC;;EAE/B;;oBA5DW0E,gBAAgB;;mBAAhBA,iBAAgB;AAAA;;SAAhBA,iBAAgB;EAAAsC,OAAA,EAAhBtC,iBAAgB,CAAAuC,IAAA;EAAAC,UAAA,EAFf;AAAM;;;;;;;;;;;;;;;;ACLpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["./src/app/doc-list/doc-list-routing.module.ts", "./src/app/doc-list/doc-list.module.ts", "./src/app/doc-list/doc-list.page.ts", "./src/app/doc-list/doc-list.page.html", "./src/app/services/websocket.service.ts", "./node_modules/swiper/swiper.mjs"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { DocListPage } from './doc-list.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: DocListPage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class DocListPageRoutingModule {}\r\n", "import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { DocListPageRoutingModule } from './doc-list-routing.module';\r\n\r\nimport { DocListPage } from './doc-list.page';\r\nimport { SharedModule } from '../shared/shared.module'; // Import SharedModule\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    DocListPageRoutingModule,\r\n    SharedModule\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  declarations: [DocListPage]\r\n})\r\nexport class DocListPageModule {}\r\n", "import {\r\n  Component,\r\n  ViewChild,\r\n  <PERSON><PERSON><PERSON>w<PERSON>nit,\r\n  ElementRef,\r\n  OnInit,\r\n  inject,\r\n} from '@angular/core';\r\nimport { NavController, LoadingController, AlertController } from '@ionic/angular';\r\nimport Swiper from 'swiper';\r\nimport { SignalService } from '../services/signal.service';\r\nimport { ProcessDocData } from 'src/models/ProcessDocData';\r\nimport { ApiService } from '../services/api.service';  // Import ApiService\r\nimport { ImageData } from 'src/models/ImageData';\r\nimport { TransformedDocData } from 'src/models/TransformedDocData';\r\nimport { NetworkService } from '../services/network.service';\r\nimport { WebSocketService } from '../services/websocket.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nenum OcrMode {\r\n  STANDARD = 'STANDARD',\r\n  MINDEE_ADVANCED = 'MINDEE_ADVANCED'\r\n}\r\n\r\n@Component({\r\n  selector: 'app-doc-list',\r\n  templateUrl: './doc-list.page.html',\r\n  styleUrls: ['./doc-list.page.scss'],\r\n})\r\nexport class DocListPage implements AfterViewInit, OnInit {\r\n  @ViewChild('swiperContainer', { static: true }) swiperContainer:\r\n    | ElementRef\r\n    | undefined;\r\n  swiper: Swiper | undefined;\r\n\r\n  slidesData: ProcessDocData[] = [];\r\n  navCtrl = inject(NavController);\r\n  signalService = inject(SignalService);\r\n  apiService = inject(ApiService);\r\n  loadingController = inject(LoadingController);\r\n\r\n  progress = 0;\r\n  isLoading = false;\r\n  isConnected = true; // Track network status\r\n  jobId: string | undefined; // Add this line to store the job ID\r\n\r\n  constructor(\r\n    private alertController: AlertController,\r\n    private webSocketService: WebSocketService,\r\n    private networkService: NetworkService,\r\n  ) {\r\n    // this.jobId = this.apiService.generateJobId(); // Generate job ID once\r\n  }\r\n\r\n  async ngAfterViewInit() {\r\n    this.initializeSwiper();\r\n  }\r\n\r\n  async ngOnInit() {\r\n    if (this.signalService.getData() == null || this.signalService.getData() == undefined || this.signalService.getData().length == 0) {\r\n      this.navCtrl.navigateRoot('/scan-bl');\r\n    } else {\r\n      this.slidesData = this.signalService.getData();\r\n      this.swiperContainer?.nativeElement.swiper?.update();\r\n\r\n      console.log('Slides data:', this.slidesData);\r\n    }\r\n\r\n    // Subscribe to the network status\r\n    this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n      this.isConnected = connected;\r\n    });\r\n\r\n    // // const jobId = this.apiService.generateJobId();  // Get the job ID\r\n    // const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n    // this.webSocketService.connect(websocketUrl, this.jobId);\r\n    // this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n    //   // console.log('Received message:', message);\r\n    //   if (message.progress !== undefined) {\r\n    //     this.progress = message.progress;\r\n    //   }\r\n\r\n    //   console.log(\"progress __ :\" , this.progress)\r\n\r\n    // });\r\n  }\r\n\r\n  async initializeSwiper() {\r\n    this.swiper = new Swiper(this.swiperContainer?.nativeElement, {\r\n      effect: 'cards',\r\n      grabCursor: true,\r\n      on: {\r\n        init: () => {\r\n          console.log('Swiper initialized', this.swiper);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  data_bl() {\r\n    this.navCtrl.navigateRoot('/data-bl'); \r\n  }\r\n  scan_bl() {\r\n    this.navCtrl.navigateRoot('/scan-bl');  \r\n  }\r\n\r\n  async renameCard(index: number) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Renommer le document',\r\n      inputs: [\r\n        {\r\n          name: 'newTitle',\r\n          type: 'text',\r\n          value: this.slidesData[index].title,\r\n          placeholder: 'New Title',\r\n        },\r\n      ],\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button-rename-doc cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Renommer',\r\n          cssClass: 'custom-alert-button-rename-doc rename',\r\n          handler: (data) => {\r\n            this.slidesData[index].title = data.newTitle;\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async confirmDeleteCard(index: number) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.removeSlide(index);\r\n            if (this.slidesData.length == 0) {\r\n              localStorage.removeItem('selectedSupplier');\r\n              this.navCtrl.navigateRoot('/scan-bl');\r\n            }\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async removeSlide(index: number) {\r\n    const bottomSwiperInstance = this.swiperContainer?.nativeElement.swiper;\r\n    bottomSwiperInstance.removeSlide(index);\r\n    bottomSwiperInstance.update();\r\n    bottomSwiperInstance.slideTo(index - 1) ?? index > 0\r\n    this.slidesData.splice(index, 1);\r\n    console.log('Slide removed and swiper updated', bottomSwiperInstance);\r\n  }\r\n\r\n  async removeDoc(index: number) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer ce document ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.signalService.removeData(index);\r\n            this.slidesData = this.signalService.getData();\r\n            this.swiperContainer?.nativeElement.swiper?.removeSlide(index);\r\n            this.swiperContainer?.nativeElement.swiper?.update();\r\n            if (this.slidesData.length == 0) {\r\n              localStorage.removeItem('selectedSupplier');\r\n              this.navCtrl.navigateRoot('/scan-bl');\r\n            }\r\n\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async removeAllDoc() {\r\n    const alert = await this.alertController.create({\r\n      header: 'Supprimer le document',\r\n      message: `Êtes-vous sûr de vouloir supprimer Tous les documents ?`,\r\n      buttons: [\r\n        {\r\n          text: 'Annuler',\r\n          role: 'cancel',\r\n          cssClass: 'custom-alert-button cancel',\r\n          handler: () => {\r\n            console.log('Confirm Cancel');\r\n          },\r\n        },\r\n        {\r\n          text: 'Oui, Supprimer !',\r\n          cssClass: 'custom-alert-button danger',\r\n          handler: () => {\r\n            this.signalService.removeAllData();\r\n            this.slidesData = [];\r\n            this.swiperContainer?.nativeElement.swiper?.removeAllSlides();\r\n            this.swiperContainer?.nativeElement.swiper?.update();\r\n            localStorage.removeItem('selectedSupplier');\r\n            // redirect to scan-bl\r\n            this.navCtrl.navigateRoot('/scan-bl');\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n\r\n  async processOcrMulti() {\r\n\r\n    this.jobId = this.apiService.generateJobId(); // Generate job ID\r\n    const websocketUrl = `${environment.webSocketUrl}/${this.jobId}`;\r\n    console.log('WebSocket URL doc-list:', websocketUrl);\r\n\r\n    this.webSocketService.connect(websocketUrl, this.jobId);\r\n\r\n    this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n      if (message.progress !== undefined) {\r\n        this.progress = message.progress;\r\n        console.log(\"progress __ :\", this.progress);\r\n      }\r\n    });\r\n\r\n    // const loading = await this.presentLoading(); // Show loading spinner\r\n    this.isLoading = true;\r\n    const that = this;\r\n\r\n    // check if data.supplier_name && data.random_id exists\r\n    if (this.slidesData.some(data => data.supplier_name == undefined || data.random_id == undefined || data.supplier_name == '' || data.random_id == '')) {\r\n      const errorMessage = `\r\n      <h3>Le fournisseur ou l'image est incorrect</h3>\r\n      <ul>\r\n        <li>Verifier le founerisseur selectionné</li>\r\n        <li>Verifier que l'image est un document</li>\r\n      </ul>\r\n      `;\r\n      this.apiService.showErrorAlert(errorMessage);\r\n      this.isLoading = false;\r\n      return;\r\n    }\r\n\r\n    // Get random_id from the first image since all images have the same random_id\r\n    const globalRandomId = (this.slidesData[0].random_id ?? '').toString();\r\n\r\n    const imageDataArray: ImageData[] = this.slidesData.map(data => {\r\n      // Determine model name\r\n      let modelName: string;\r\n      if (data.supplier_name) {\r\n        modelName = data.supplier_name.toUpperCase();\r\n        if (modelName === 'UNKNOWN') {\r\n          modelName = (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';\r\n        }\r\n      } else {\r\n        modelName = (localStorage.getItem('selectedSupplier') && localStorage.getItem('selectedSupplier') != 'undefined' && localStorage.getItem('selectedSupplier') != 'AUTRE') ? '' + localStorage.getItem('selectedSupplier') : 'GLOBAL';\r\n      }\r\n\r\n      // get it from the local storage (force the supplier to be global for apply the Advanced OCR 'Mindee')\r\n      let forceSupplierGlobal = localStorage.getItem('forceSupplierGlobal') == 'true' ? true : false;\r\n\r\n      // modelName = 'UNKNOWN' // -- for test error handling\r\n\r\n      // Determine image URL\r\n      // const models = ['GPM', 'SOPHADIMS', 'COOPER']\r\n      // const imageUrl = models.includes(data.supplier_name?.toLocaleUpperCase() ?? '')\r\n      //   ? (data.cropped_image?.substring(data.cropped_image?.indexOf('smart_crop_output')) ?? '')\r\n      //   : (data.filtered_image?.substring(data.filtered_image?.indexOf('magic_pro_filter_output')) ?? '');\r\n      \r\n      const imageUrl = (data.cropped_image?.substring(data.cropped_image?.indexOf('smart_crop_output')) ?? ''); // Force to use smart_crop_output image \r\n      console.log('supplier_name:', data.supplier_name?.toLocaleUpperCase());\r\n      console.log('Image URL:', imageUrl);\r\n      return {\r\n        image: imageUrl, // ( case of ['GPM', 'SOPHADIMS', 'COOPER'] suppliers )\r\n        // model_name: forceSupplierGlobal == true ? 'GLOBAL' : modelName, // force the supplier to be global for apply the Advanced OCR 'Mindee'\r\n        model_name: modelName, // force the supplier to be global for apply the Advanced OCR 'Mindee'\r\n        random_id: data.random_id\r\n      };\r\n    });\r\n\r\n    console.log('Image data array:', JSON.stringify(imageDataArray));\r\n\r\n\r\n    // Get the current OCR mode from localStorage or use default\r\n    const currentOcrMode = (localStorage.getItem('ocrMode') as OcrMode) || OcrMode.MINDEE_ADVANCED;\r\n\r\n    this.apiService.processOcrMulti(imageDataArray, this.jobId, globalRandomId, currentOcrMode).subscribe(\r\n      (response) => {\r\n        console.log('OCR Multi response:', response);\r\n        try {\r\n          this.signalService.transformAndSetData(this.transformAndSetData(response['responses']));\r\n          // loading.dismiss();\r\n          this.isLoading = false;\r\n          // this.navCtrl.navigateForward('/data-bl', { state: { data: this.signalService.getTransformedData() } }); // Redirect to data-bl page\r\n          this.navCtrl.navigateForward('/data-bl-success', { state: { BL_id: response['ID_BL'], supplier_name: imageDataArray[0].model_name } }); // Redirect to data-bl-success page\r\n\r\n          // Disconnect WebSocket after completion\r\n          this.webSocketService.close(this.jobId!);\r\n        }\r\n        catch (e) {\r\n          const errorMessage = `\r\n          <h3>Erreur de traitement du document</h3>\r\n          <ul>\r\n            <li>Verifier que l'image est un document</li>\r\n            <li>Verifier le founerisseur selectionné</li>\r\n            <li>Verifier le bon cadrage du document</li>\r\n            <li>Verifier la qualité de l'image</li>\r\n            <li>Supprimer les objets inutiles dans l'image </li>\r\n          </ul>\r\n          `;\r\n          this.apiService.showErrorAlert(errorMessage);\r\n          this.isLoading = false;\r\n          this.progress = 0;\r\n          this.navCtrl.navigateRoot('/request-error');\r\n        }\r\n\r\n\r\n      },\r\n      (error) => {\r\n        // loading.dismiss();\r\n        this.isLoading = false;\r\n        const errorMessage = `\r\n        <h3>Erreur de traitement du document</h3>\r\n        <ul>\r\n          <li>Verifier que l'image est un document</li>\r\n          <li>Verifier le founerisseur selectionné</li>\r\n          <li>Verifier le bon cadrage du document</li>\r\n          <li>Verifier la qualité de l'image</li>\r\n          <li>Supprimer les objets inutiles dans l'image </li>\r\n        </ul>\r\n        `;\r\n        this.apiService.showErrorAlert(errorMessage);\r\n        console.error('API error:', error);\r\n        console.error(errorMessage);\r\n\r\n        // // Disconnect WebSocket after completion\r\n        this.webSocketService.close(this.jobId!);\r\n      });\r\n\r\n    this.webSocketService.onMessage(this.jobId).subscribe((message) => {\r\n      if (message.progress !== undefined) {\r\n        this.progress = message.progress;\r\n      }\r\n    });\r\n  }\r\n\r\n  async presentLoading() {\r\n    const loading = await this.loadingController.create({\r\n      message: 'Chargement...',\r\n      spinner: 'circles',\r\n      // duration: 30000 // Optional: specify a timeout for the loading spinner\r\n    });\r\n    await loading.present();\r\n    return loading;\r\n  }\r\n\r\n\r\n  transformAndSetData(responseData: any) {\r\n    try {\r\n      const transformedData: TransformedDocData[] = responseData.map((item: any, index: number) => {\r\n        const general = item.data?.general;\r\n        const products = item.data.table\r\n          .filter((product: any) =>\r\n            product.designation ||\r\n            product.quantity ||\r\n            product.date_per ||\r\n            product.ppv ||\r\n            product.pph ||\r\n            product.total_ttc\r\n          ) // Filter out products with all fields null\r\n          .map((product: any) => ({\r\n            designation: product.designation,\r\n            quantity: product.quantity ? parseInt(product.quantity) : null,\r\n            expiryDate: product.date_per || null,\r\n            ppv: product.ppv ? parseFloat(product.ppv) : null,\r\n            pph: product.pph ? parseFloat(product.pph) : null,\r\n            total: product.total_ttc ? parseFloat(product.total_ttc) : null,\r\n          }));\r\n        return {\r\n          image: general.images_url_path.origin,\r\n          title: `Scan ${this.signalService.formatDate()}`,\r\n          date: this.signalService.getFormattedDate(general.date_export),\r\n          page_index: index + 1,\r\n          products,\r\n        };\r\n      });\r\n\r\n      return transformedData;\r\n\r\n    } catch (e) {\r\n      console.log('Error:', e);\r\n      const errorMessage = `\r\n      <h3>Erreur de traitement du document</h3>\r\n      <ul>\r\n        <li>Verifier que l'image est un document</li>\r\n        <li>Verifier le founerisseur selectionné</li>\r\n        <li>Verifier le bon cadrage du document</li>\r\n        <li>Verifier la qualité de l'image</li>\r\n        <li>Supprimer les objets inutiles dans l'image </li>\r\n      </ul>\r\n      `;\r\n      this.apiService.showErrorAlert(errorMessage);\r\n      this.isLoading = false;\r\n      this.progress = 0;\r\n      this.navCtrl.navigateRoot('/request-error');\r\n\r\n      return [];\r\n    }\r\n  }\r\n}\r\n", "<ion-header [ngClass]=\"{'loading': isLoading}\">\r\n  <ion-toolbar>\r\n    <ion-title>Liste des pages</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"scan_bl()\">\r\n        <app-custom-icon\r\n          name=\"camera-plus\"\r\n          class=\"camera-plus\"\r\n        ></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [ngClass]=\"{'loading': isLoading}\" class=\"doc-list-wrapper\">\r\n  <app-check-network></app-check-network>\r\n  <!-- <h2 class=\"section-title\">Récent</h2>\r\n  <swiper-container effect=\"cards\" grab-cursor=\"true\" class=\"swiper\" #swiperContainer >\r\n    <swiper-slide *ngFor=\"let slide of slidesData\" class=\"test\">\r\n      <ion-row>\r\n        <ion-col size=\"12\" class=\"slide-content\">\r\n          <ion-card class=\"card-doc\">\r\n            <img [src]=\"slide.filtered_image\" class=\"slide-image\" />\r\n            <div class=\"content-global-card\">\r\n              <ion-card-header>\r\n                <ion-card-subtitle>{{ slide.title }}</ion-card-subtitle>\r\n              </ion-card-header>\r\n              <ion-card-content>\r\n                <span>{{ slide.date }}</span>\r\n                <span>Page {{ slide.page_index }}</span>\r\n              </ion-card-content>\r\n            </div>\r\n          </ion-card>\r\n        </ion-col>\r\n      </ion-row>\r\n    </swiper-slide>\r\n  </swiper-container> -->\r\n\r\n  <h2 class=\"section-title\">Pages</h2>\r\n  <div class=\"doc-list\">\r\n    <ion-item-sliding class=\"document-card\" *ngFor=\"let slide of slidesData; let i = index\">\r\n      <ion-item>\r\n        <ion-thumbnail slot=\"start\">\r\n          <img [src]=\"slide.filtered_image\" />\r\n        </ion-thumbnail>\r\n        <ion-label>\r\n          <h3>{{ slide.title }}</h3>\r\n          <p>{{ slide.date }}</p>\r\n        </ion-label>\r\n        <div class=\"page-count\">Page {{ slide.page_index }}</div>\r\n      </ion-item>\r\n      <ion-item-options side=\"end\">\r\n        <ion-item-option (click)=\"renameCard(i)\" class=\"renameCard\">\r\n          <div class=\"content-item-option\">\r\n            <ion-icon slot=\"start\" name=\"create-outline\"></ion-icon>\r\n            <span>Renommer</span>\r\n          </div>\r\n        </ion-item-option>\r\n        <ion-item-option (click)=\"confirmDeleteCard(i)\" class=\"confirmDeleteCard\">\r\n          <div class=\"content-item-option\">\r\n            <ion-icon slot=\"start\" name=\"trash\"></ion-icon>\r\n            <span>Supprimer</span>\r\n          </div>\r\n        </ion-item-option>\r\n      </ion-item-options>\r\n    </ion-item-sliding>\r\n  </div>\r\n</ion-content>\r\n\r\n\r\n\r\n<div class=\"alert-progress\" [ngClass]=\"{'loading': isLoading}\">\r\n  <app-custom-alert [progress]=\"progress\"></app-custom-alert>\r\n</div>\r\n\r\n<ion-footer [ngClass]=\"{'loading': isLoading}\">\r\n  <ion-toolbar>\r\n    <ion-buttons>\r\n      <ion-button class=\"menu-button active\" size=\"small\" [routerLink]=\"['/process-doc']\">\r\n        <app-custom-icon name=\"files\"></app-custom-icon>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button-middle\" (click)=\"processOcrMulti()\">\r\n        <app-custom-icon name=\"extract\"></app-custom-icon>\r\n        <span>EXTRAIRE</span>\r\n      </ion-button>\r\n      <ion-button class=\"menu-button\" size=\"small\" (click)=\"removeAllDoc()\">\r\n        <app-custom-icon name=\"delete\"></app-custom-icon>\r\n      </ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-footer>\r\n", "import { Injectable } from '@angular/core';\r\nimport { Observable, Subject } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WebSocketService {\r\n  private socket?: WebSocket;\r\n  private subjects: { [jobId: string]: Subject<any> } = {};\r\n\r\n  constructor() {\r\n  }\r\n\r\n  public connect(url: string, jobId: string): void {\r\n    this.socket = new WebSocket(url);\r\n\r\n    this.socket.onopen = (event) => {\r\n      console.log('WebSocket connection established:', event);\r\n    };\r\n\r\n    this.socket.onmessage = (event) => {\r\n      const data = JSON.parse(event.data);\r\n      if (data.job_id && this.subjects[data.job_id]) {\r\n        this.subjects[data.job_id].next(data);\r\n      } else if (this.subjects[jobId]) {\r\n        this.subjects[jobId].next(data);\r\n      }\r\n    };\r\n\r\n    this.socket.onerror = (event) => {\r\n      console.error('WebSocket error observed:', event);\r\n      this.reconnect(url, jobId);\r\n    };\r\n\r\n    this.socket.onclose = (event) => {\r\n      console.log('WebSocket connection closed:', event);\r\n      this.reconnect(url, jobId);\r\n    };\r\n  }\r\n\r\n  private reconnect(url: string, jobId: string): void {\r\n    setTimeout(() => {\r\n      this.connect(url, jobId);\r\n    }, 1000); // Retry connection after 1 second\r\n  }\r\n\r\n  public send(data: any): void {\r\n    if (this.socket?.readyState === WebSocket.OPEN) {\r\n      this.socket?.send(JSON.stringify(data));\r\n    } else {\r\n      console.error('WebSocket connection is not open.');\r\n    }\r\n  }\r\n\r\n  public onMessage(jobId: string): Observable<any> {\r\n    if (!this.subjects[jobId]) {\r\n      this.subjects[jobId] = new Subject<any>();\r\n    }\r\n    return this.subjects[jobId].asObservable();\r\n  }\r\n\r\n  public close(jobId: string): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      delete this.subjects[jobId];\r\n    }\r\n  }\r\n\r\n}\r\n", "/**\n * Swiper 11.1.4\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2024 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: May 30, 2024\n */\n\nexport { S as Swiper, S as default } from './shared/swiper-core.mjs';\n"], "names": ["RouterModule", "DocListPage", "routes", "path", "component", "DocListPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports", "CommonModule", "FormsModule", "IonicModule", "SharedModule", "DocListPageModule", "declarations", "inject", "NavController", "LoadingController", "Swiper", "SignalService", "ApiService", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "DocListPage_ion_item_sliding_12_Template_ion_item_option_click_12_listener", "i_r2", "ɵɵrestoreView", "_r1", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "renameCard", "DocListPage_ion_item_sliding_12_Template_ion_item_option_click_17_listener", "confirmDeleteCard", "ɵɵadvance", "ɵɵproperty", "slide_r4", "filtered_image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "date", "ɵɵtextInterpolate1", "page_index", "OcrMode", "constructor", "alertController", "webSocketService", "networkService", "slidesData", "navCtrl", "signalService", "apiService", "loadingController", "progress", "isLoading", "isConnected", "ngAfterViewInit", "_this", "_asyncToGenerator", "initializeSwiper", "ngOnInit", "_this2", "getData", "undefined", "length", "navigateRoot", "_this2$swiperContaine", "swiper<PERSON><PERSON><PERSON>", "nativeElement", "swiper", "update", "console", "log", "getNetworkStatus", "subscribe", "connected", "_this3", "_this3$swiperContaine", "effect", "grabCursor", "on", "init", "data_bl", "scan_bl", "_this4", "alert", "create", "header", "inputs", "name", "type", "value", "placeholder", "buttons", "text", "role", "cssClass", "handler", "data", "newTitle", "present", "_this5", "message", "removeSlide", "localStorage", "removeItem", "_this6", "_this6$swiperContaine", "_bottomSwiperInstance", "bottomSwiperInstance", "slideTo", "splice", "removeDoc", "_this7", "_this7$swiperContaine", "_this7$swiperContaine2", "removeData", "removeAllDoc", "_this8", "_this8$swiperContaine", "_this8$swiperContaine2", "removeAllData", "removeAllSlides", "processOcrMulti", "_this9", "_this9$slidesData$0$r", "jobId", "generateJobId", "websocketUrl", "webSocketUrl", "connect", "onMessage", "that", "some", "supplier_name", "random_id", "errorMessage", "showError<PERSON><PERSON>t", "globalRandomId", "toString", "imageDataArray", "map", "_data$cropped_image$s", "_data$cropped_image", "_data$cropped_image2", "_data$supplier_name", "modelName", "toUpperCase", "getItem", "forceSupplierGlobal", "imageUrl", "cropped_image", "substring", "indexOf", "toLocaleUpperCase", "image", "model_name", "JSON", "stringify", "currentOcrMode", "MINDEE_ADVANCED", "response", "transformAndSetData", "navigateForward", "state", "BL_id", "close", "e", "error", "presentLoading", "_this10", "loading", "spinner", "responseData", "transformedData", "item", "_item$data", "general", "products", "table", "filter", "product", "designation", "quantity", "date_per", "ppv", "pph", "total_ttc", "parseInt", "expiryDate", "parseFloat", "total", "images_url_path", "origin", "formatDate", "getFormattedDate", "date_export", "ɵɵdirectiveInject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "WebSocketService", "i3", "NetworkService", "selectors", "viewQuery", "DocListPage_Query", "rf", "ctx", "DocListPage_Template_ion_button_click_5_listener", "ɵɵtemplate", "DocListPage_ion_item_sliding_12_Template", "DocListPage_Template_ion_button_click_20_listener", "DocListPage_Template_ion_button_click_24_listener", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c2", "Subject", "subjects", "url", "socket", "WebSocket", "onopen", "event", "onmessage", "parse", "job_id", "next", "onerror", "reconnect", "onclose", "setTimeout", "send", "_this$socket", "readyState", "OPEN", "_this$socket2", "asObservable", "factory", "ɵfac", "providedIn", "S", "default"], "sourceRoot": "webpack:///", "x_google_ignoreList": [5]}