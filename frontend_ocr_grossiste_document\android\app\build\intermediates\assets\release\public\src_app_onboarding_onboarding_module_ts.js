"use strict";
(self["webpackChunkapp"] = self["webpackChunkapp"] || []).push([["src_app_onboarding_onboarding_module_ts"],{

/***/ 48446:
/*!*********************************************************!*\
  !*** ./src/app/onboarding/onboarding-routing.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingPageRoutingModule: () => (/* binding */ OnboardingPageRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _onboarding_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onboarding.page */ 87744);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 37580);
var _OnboardingPageRoutingModule;




const routes = [{
  path: '',
  component: _onboarding_page__WEBPACK_IMPORTED_MODULE_0__.OnboardingPage
}];
class OnboardingPageRoutingModule {}
_OnboardingPageRoutingModule = OnboardingPageRoutingModule;
_OnboardingPageRoutingModule.ɵfac = function OnboardingPageRoutingModule_Factory(t) {
  return new (t || _OnboardingPageRoutingModule)();
};
_OnboardingPageRoutingModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
  type: _OnboardingPageRoutingModule
});
_OnboardingPageRoutingModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
  imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](OnboardingPageRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 97543:
/*!*************************************************!*\
  !*** ./src/app/onboarding/onboarding.module.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingPageModule: () => (/* binding */ OnboardingPageModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 60316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 34456);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _onboarding_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onboarding-routing.module */ 48446);
/* harmony import */ var _onboarding_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./onboarding.page */ 87744);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
var _OnboardingPageModule;






class OnboardingPageModule {}
_OnboardingPageModule = OnboardingPageModule;
_OnboardingPageModule.ɵfac = function OnboardingPageModule_Factory(t) {
  return new (t || _OnboardingPageModule)();
};
_OnboardingPageModule.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
  type: _OnboardingPageModule
});
_OnboardingPageModule.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
  imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _onboarding_routing_module__WEBPACK_IMPORTED_MODULE_0__.OnboardingPageRoutingModule]
});
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](OnboardingPageModule, {
    declarations: [_onboarding_page__WEBPACK_IMPORTED_MODULE_1__.OnboardingPage],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _ionic_angular__WEBPACK_IMPORTED_MODULE_5__.IonicModule, _onboarding_routing_module__WEBPACK_IMPORTED_MODULE_0__.OnboardingPageRoutingModule]
  });
})();

/***/ }),

/***/ 87744:
/*!***********************************************!*\
  !*** ./src/app/onboarding/onboarding.page.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingPage: () => (/* binding */ OnboardingPage)
/* harmony export */ });
/* harmony import */ var C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 89204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 37580);
/* harmony import */ var _services_storage_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/storage.service */ 57291);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ionic/angular */ 4059);
/* harmony import */ var _ionic_angular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ionic/angular */ 21507);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 95072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 60316);

var _OnboardingPage;





const _c0 = ["swiper"];
function OnboardingPage_swiper_slide_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "swiper-slide", 10)(1, "ion-row")(2, "ion-col", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](3, "img", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 13)(5, "h2");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const slide_r2 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("src", slide_r2.image, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](slide_r2.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](slide_r2.description);
  }
}
class OnboardingPage {
  constructor(storageService, navCtrl, router) {
    this.storageService = storageService;
    this.navCtrl = navCtrl;
    this.router = router;
    this.slidesData = [
    // {
    //   image: 'assets/onboarding_images/slide1.svg',
    //   title: 'Capturez vos documents facilement',
    //   description: 'Simplifiez la gestion de vos Bons de Livraison avec notre technologie OCR avancée'
    // },
    // {
    //   image: 'assets/onboarding_images/slide2.svg',
    //   title: 'Scannez vos documents',
    //   description: 'Jusqu\'à 5 pages par Bon de Livraison. Assurez-vous que toutes les pages appartiennent au même Bon de Livraison pour une meilleure précision.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide3.svg',
    //   title: 'Recadrez vos documents',
    //   description: 'Utilisez notre outil de recadrage pour sélectionner uniquement les parties importantes du document avant d\'extraire les données.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide4.svg',
    //   title: 'Choisissez une source d\'image',
    //   description: 'Caméra ou Galerie. Prenez une photo directement ou sélectionnez une image existante depuis votre galerie.'
    // },
    // {
    //   image: 'assets/onboarding_images/slide5.svg',
    //   title: 'Identifiez le fournisseur',
    //   description: 'Pour des résultats optimaux. Choisissez le fournisseur du document avant de soumettre le formulaire.'
    // },
    {
      image: 'assets/onboarding_images/windoc.svg',
      title: 'Scannez votre BL avec WinDoc',
      description: "Utilisez l'application WinDoc pour capturer une image claire de votre Bon de Livraison (BL). L'application détecte automatiquement les contours et améliore l'image pour une meilleure reconnaissance."
    }, {
      image: 'assets/onboarding_images/slide2.svg',
      title: 'Extraction et vérification des données',
      description: 'WinDoc extrait automatiquement les informations clés de votre BL, comme la désignation, la quantité et le PPV. Vérifiez et complétez les informations si nécessaire avant de soumettre.'
    }, {
      image: 'assets/onboarding_images/winpluspharm.svg',
      title: 'Retrouvez vos résultats sur WinPlusPharma',
      description: 'Une fois le BL soumis, retrouvez les résultats directement dans votre espace personnel sur WinPlusPharma.'
    }
    // Add other slides here...
    ];
  }
  ngAfterViewInit() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
    }
  }
  skip() {
    // this.navCtrl.navigateRoot('/scan-bl');
    this.completeOnboarding();
  }
  welcome() {
    this.navCtrl.navigateRoot('/welcome');
  }
  next() {
    if (this.swiper && this.swiper.nativeElement) {
      const swiperInstance = this.swiper.nativeElement.swiper;
      const currentIndex = swiperInstance.activeIndex;
      const totalSlides = swiperInstance.slides.length - 1;
      if (currentIndex === totalSlides) {
        // // Navigate to 'scan-bl' page if it's the last slide
        // this.welcome()
        // Call completeOnboarding on the last slide
        this.completeOnboarding();
      } else {
        // Otherwise, navigate to the next slide
        swiperInstance.slideNext();
      }
    }
  }
  completeOnboarding() {
    var _this = this;
    return (0,C_Users_abder_Downloads_Work_Abderrahmane_ouhna_OCR_DOCUMENT_GROSSISTE_Frontend_ocr_grossiste_document_frontend_ocr_grossiste_document_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      console.log('Setting hasSeenOnboarding to true');
      // await this.storageService.set('hasSeenOnboarding', true);
      _this.router.navigate(['/welcome']); // Redirect to the main app
    })();
  }
}
_OnboardingPage = OnboardingPage;
_OnboardingPage.ɵfac = function OnboardingPage_Factory(t) {
  return new (t || _OnboardingPage)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_storage_service__WEBPACK_IMPORTED_MODULE_1__.StorageService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_ionic_angular__WEBPACK_IMPORTED_MODULE_3__.NavController), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router));
};
_OnboardingPage.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
  type: _OnboardingPage,
  selectors: [["app-onboarding"]],
  viewQuery: function OnboardingPage_Query(rf, ctx) {
    if (rf & 1) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵviewQuery"](_c0, 7);
    }
    if (rf & 2) {
      let _t;
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵloadQuery"]()) && (ctx.swiper = _t.first);
    }
  },
  decls: 15,
  vars: 1,
  consts: [["swiper", ""], [1, "fix-wrapper"], ["fullscreen", ""], [1, "onboarding-wrapper"], ["pagination", "true"], ["class", "test", 4, "ngFor", "ngForOf"], [1, "buttons_nav"], ["fill", "solid", 1, "next-button", 3, "click"], ["slot", "end", "name", "arrow-forward-outline"], ["fill", "clear", 1, "skip-button", 3, "click"], [1, "test"], ["size", "12", 1, "slide-content"], [1, "slide-image", 3, "src"], [1, "content-slide"]],
  template: function OnboardingPage_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 1);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](1, "ion-header");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "ion-content", 2)(3, "div", 3)(4, "swiper-container", 4, 0);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, OnboardingPage_swiper_slide_6_Template, 9, 3, "swiper-slide", 5);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "ion-footer")(8, "div", 6)(9, "ion-button", 7);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function OnboardingPage_Template_ion_button_click_9_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx.next());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "span");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "SUIVANT");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](12, "ion-icon", 8);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "ion-button", 9);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function OnboardingPage_Template_ion_button_click_13_listener() {
        _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r1);
        return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx.skip());
      });
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "PASSER");
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
    }
    if (rf & 2) {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.slidesData);
    }
  },
  dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonButton, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonCol, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonContent, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonFooter, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonHeader, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonIcon, _ionic_angular__WEBPACK_IMPORTED_MODULE_6__.IonRow],
  styles: ["@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);*[_ngcontent-%COMP%] {\n  font-family: \"Inter\", sans-serif;\n  font-optical-sizing: auto;\n}\n\n.footer-md[_ngcontent-%COMP%], .header-md[_ngcontent-%COMP%] {\n  box-shadow: none;\n}\n\n.fix-wrapper[_ngcontent-%COMP%] {\n  --ion-background-color: transparent;\n  display: flex;\n  flex-direction: column;\n  background: url(\"/assets/onboarding_images/bg_onboarding.png\") no-repeat;\n  background-size: cover;\n  height: 100dvh;\n}\n\n.onboarding-wrapper[_ngcontent-%COMP%] {\n  height: 100%;\n}\n\nion-footer[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 20px;\n}\n\n.slide-image[_ngcontent-%COMP%] {\n  width: 350px;\n  height: 350px;\n  margin: 0 auto 20px;\n}\n\nion-footer[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n  background: transparent;\n  --background: transparent;\n  --ion-background-color: transparent;\n}\n\nion-toolbar[_ngcontent-%COMP%] {\n  --background: transparent;\n  --ion-color-primary: #2f4fcd;\n}\n\nion-button[_ngcontent-%COMP%] {\n  margin: 10px;\n}\n\nion-button.next-button[_ngcontent-%COMP%] {\n  --background: #2f4fcd;\n  --background-activated: #1e3aa8;\n  --border-radius: 8px;\n  --color: #fff;\n  width: 80%;\n}\n\nion-button.skip-button[_ngcontent-%COMP%] {\n  --color: #2f4fcd;\n  width: 20%;\n}\n\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin: 20px 0;\n  gap: 5px;\n}\n\n.pagination[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  display: block;\n  width: 10px;\n  height: 10px;\n  background-color: #ccc;\n  border-radius: 50%;\n}\n\n.pagination[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%] {\n  background-color: #2f4fcd;\n}\n\n.buttons_nav[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  gap: 5px;\n  background: transparent;\n}\n\n\n\nswiper-container[_ngcontent-%COMP%] {\n  height: 100%;\n  --swiper-pagination-bullet-inactive-color: var(--ion-color-step-200, #cccccc);\n  --swiper-pagination-color: var(--ion-color-primary, #2f4fcd);\n  --swiper-pagination-progressbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.25);\n  --swiper-scrollbar-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.1);\n  --swiper-scrollbar-drag-bg-color: rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.5);\n}\n\nswiper-slide[_ngcontent-%COMP%] {\n  display: flex;\n  position: relative;\n  flex-direction: column;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  font-size: 18px;\n  text-align: center;\n  box-sizing: border-box;\n  height: calc(100vh - 90px);\n  overflow-y: auto;\n}\n\nswiper-slide[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%] {\n  height: 100%;\n}\n\n  swiper-slide ion-row ion-col {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n  swiper-slide ion-col {\n  display: flex !important;\n  flex-direction: column;\n  justify-content: space-evenly;\n  align-items: center;\n}\n\n  swiper-slide img {\n  width: auto;\n  max-width: 100%;\n  height: auto;\n  max-height: 100%;\n  margin-bottom: 10%;\n}\n\n  swiper-slide .content-slide {\n  color: #000;\n  text-align: left;\n  padding: 0 15px;\n  margin-bottom: 100px;\n}\n\n  swiper-slide .content-slide h2 {\n  font-weight: bold;\n}\n\n  swiper-slide .content-slide p {\n  letter-spacing: 0.9px;\n  font-size: 16px;\n  padding-right: 20px;\n}\n\n.next-button[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  flex-direction: row;\n  align-items: center;\n}\n\n.skip-button[_ngcontent-%COMP%] {\n  font-size: 16px !important;\n  font-weight: bold;\n  opacity: 0.7;\n}\n\nswiper-container[_ngcontent-%COMP%]::part(bullet-active) {\n  width: 70px !important;\n  height: 5px !important;\n  border-radius: 8px !important;\n}\n\nion-footer[_ngcontent-%COMP%]::before {\n  \n\n  \n\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-size: cover;\n  transform: rotate(180deg);\n  z-index: -1;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
});

/***/ })

}]);
//# sourceMappingURL=src_app_onboarding_onboarding_module_ts.js.map