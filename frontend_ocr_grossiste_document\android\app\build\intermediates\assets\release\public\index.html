<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8"/>
  <title>WinPlusDoc</title>

  <base href="/"/>

  <meta name="color-scheme" content="light dark"/>
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
  <meta name="format-detection" content="telephone=no"/>
  <meta name="msapplication-tap-highlight" content="no"/>

  <link rel="icon" type="image/png" href="assets/icon/favicon.png"/>

  <!-- add to homescreen for ios -->
  <meta name="apple-mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-status-bar-style" content="black"/>

  <!-- Force light mode -->
  <meta name="color-scheme" content="light">

  <!-- Dynamically load the package from node_modules -->
  <!-- <script type="module" src="http://***************:3333/build/image-cropper-component.esm.js"></script> -->
  <script type="module" src="assets/image-cropper-component/image-cropper-component.esm.js"></script>
  <!-- Load OpenCV.js -->
  <script>
    function onOpenCVReady() {
      if (window.cv) {
        console.log('OpenCV.js is ready');
      }
    }
  </script>
  <script async="" src="https://docs.opencv.org/master/opencv.js" onload="onOpenCVReady()"></script>

  <!-- <script nomodule src="assets/image-cropper/image-cropper-component.js"></script> -->
  
  <!-- Force light mode -->
  <!-- <meta name="color-scheme" content="light">

  <script type="module" src="assets/image-cropper/image-cropper-component.esm.js"></script>
    <script nomodule src="assets/image-cropper/image-cropper-component.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-core@3.0.30/dist/core.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-license@3.0.20/dist/license.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-document-normalizer@2.0.20/dist/ddn.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dynamsoft-capture-vision-router@2.0.30/dist/cvr.js"></script> -->
<link rel="stylesheet" href="styles.css"></head>

<body>
  <app-root></app-root>
<script src="runtime.js" type="module"></script><script src="polyfills.js" type="module"></script><script src="scripts.js" defer></script><script src="vendor.js" type="module"></script><script src="main.js" type="module"></script></body>

</html>
