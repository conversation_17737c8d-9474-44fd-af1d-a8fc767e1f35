{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-avatar_3_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC0E;AACb;AACC;AAE9D,MAAMS,YAAY,GAAG,2PAA2P;AAChR,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,2PAA2P;AAC/Q,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQd,qDAAC,CAACE,iDAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,4DAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,qDAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDJ,MAAM,CAACM,KAAK,GAAG;EACXC,GAAG,EAAEV,kBAAkB;EACvBW,EAAE,EAAET;AACR,CAAC;AAED,MAAMU,WAAW,GAAG,q4BAAq4B;AACz5B,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,k7BAAk7B;AACr8B,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBZ,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACY,KAAK,GAAGC,SAAS;EAC1B;EACAZ,MAAMA,CAAA,EAAG;IACL,MAAMa,IAAI,GAAGvB,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQJ,qDAAC,CAACE,iDAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEV,qDAAkB,CAAC,IAAI,CAACmB,KAAK,EAAE;QACjG,CAACE,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAE3B,qDAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDS,KAAK,CAACP,KAAK,GAAG;EACVC,GAAG,EAAEG,iBAAiB;EACtBF,EAAE,EAAEI;AACR,CAAC;AAED,MAAMK,YAAY,GAAG,6QAA6Q;AAClS,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,SAAS,GAAG,MAAM;EACpBlB,WAAWA,CAACC,OAAO,EAAE;IACjBd,qDAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQd,qDAAC,CAACE,iDAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,4DAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,qDAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDe,SAAS,CAACb,KAAK,GAAGY,kBAAkB", "sources": ["./node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host } from './index-c71c5417.js';\nimport { b as getIonMode } from './ionic-global-b9c0d1da.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\n\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\nconst IonAvatarIosStyle0 = avatarIosCss;\n\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\nconst IonAvatarMdStyle0 = avatarMdCss;\n\nconst Avatar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'dc1e3cd535e419eebe5599574fd2393ebfde8bbc', class: getIonMode(this) }, h(\"slot\", { key: 'edb8441c063ea592b41345ea97d88ecd90cb3052' })));\n    }\n};\nAvatar.style = {\n    ios: IonAvatarIosStyle0,\n    md: IonAvatarMdStyle0\n};\n\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\nconst IonBadgeIosStyle0 = badgeIosCss;\n\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\nconst IonBadgeMdStyle0 = badgeMdCss;\n\nconst Badge = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '1253618692342bcf9487188402dc3d49ae0de480', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: '71d65e203965ea37b94504a8a0a96beb52d4e356' })));\n    }\n};\nBadge.style = {\n    ios: IonBadgeIosStyle0,\n    md: IonBadgeMdStyle0\n};\n\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\nconst IonThumbnailStyle0 = thumbnailCss;\n\nconst Thumbnail = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'ea55000055f941b0c79561e8934be6242ec8e114', class: getIonMode(this) }, h(\"slot\", { key: 'a4f934f442797f5c66a77e0ef8920fdd07c204f2' })));\n    }\n};\nThumbnail.style = IonThumbnailStyle0;\n\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };\n"], "names": ["r", "registerInstance", "h", "f", "Host", "b", "getIonMode", "c", "createColorClasses", "avatarIosCss", "IonAvatarIosStyle0", "avatarMdCss", "IonAvatarMdStyle0", "Avatar", "constructor", "hostRef", "render", "key", "class", "style", "ios", "md", "badgeIosCss", "IonBadgeIosStyle0", "badgeMdCss", "IonBadgeMdStyle0", "Badge", "color", "undefined", "mode", "thumbnailCss", "IonThumbnailStyle0", "<PERSON><PERSON><PERSON><PERSON>", "ion_avatar", "ion_badge", "ion_thumbnail"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}