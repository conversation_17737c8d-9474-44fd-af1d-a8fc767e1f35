{"version": 3, "file": "default-src_app_shared_shared_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;ICAAA,4DAAA,aAA6C;IAC3CA,uDAAA,qBAA4C;IAC5CA,4DAAA,QAAG;IAAAA,oDAAA,oBAAa;IAClBA,0DADkB,EAAI,EAChB;;;ADOA,MAAOK,qBAAqB;EAKhCC,YAAoBC,QAAkB,EAAUC,cAA8B;IAA1D,KAAAD,QAAQ,GAARA,QAAQ;IAAoB,KAAAC,cAAc,GAAdA,cAAc;IAH9D,KAAAC,WAAW,GAAG,KAAK,CAAC,CAAC;EAG6D;EAElFC,QAAQA,CAAA;IAKN,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE,CAACC,IAAI,CAAC,MAAK;MAC9B,IAAI,CAACJ,cAAc,CAACK,gBAAgB,EAAE,CAACC,SAAS,CAAEC,SAAkB,IAAI;QACtE,IAAI,CAACN,WAAW,GAAGM,SAAS;QAC5BC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACR,WAAW,CAAC;MAClD,CAAC,CAAC;MAEF,IAAI,CAACD,cAAc,CAACU,SAAS,CAACJ,SAAS,CAACK,MAAM,IAAG;QAC/C,IAAI,CAACV,WAAW,GAAGU,MAAM;QACzBH,OAAO,CAACC,GAAG,CAACE,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;yBAvBWd,qBAAqB;;mBAArBA,sBAAqB,EAAAL,+DAAA,CAAAqB,oDAAA,GAAArB,+DAAA,CAAAuB,qEAAA;AAAA;;QAArBlB,sBAAqB;EAAAoB,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCVlC/B,wDAAA,IAAAkC,oCAAA,iBAA6C;;;MAAvClC,wDAAA,UAAAgC,GAAA,CAAAvB,WAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;ACQlB,MAAO2B,oBAAoB;EAM/B9B,YAAoB+B,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAJ1B,KAAAC,MAAM,GAAY,eAAe;IACjC,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAC,QAAQ,GAAW,CAAC;EAE0B;EAEvDC,OAAOA,CAAA;IACL,IAAI,CAACJ,eAAe,CAACI,OAAO,EAAE;EAChC;;wBAVWL,oBAAoB;;mBAApBA,qBAAoB,EAAApC,+DAAA,CAAAqB,2DAAA;AAAA;;QAApBe,qBAAoB;EAAAX,SAAA;EAAAkB,MAAA;IAAAL,MAAA;IAAAC,OAAA;IAAAC,QAAA;EAAA;EAAAd,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAe,8BAAAb,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCPjC/B,4DAAA,aAAkC;MAChCA,uDAAA,4BAA+D;MACjEA,0DAAA,EAAM;;;MADgBA,uDAAA,EAAqB;MAArBA,wDAAA,aAAAgC,GAAA,CAAAQ,QAAA,CAAqB;;;;;;;;;;;;;;;;;;;;;;;;ACMrC,MAAOM,mBAAmB;EAK9BxC,YAAoByC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;EAAiB;EAE9CrC,QAAQA,CAAA;IACN,IAAI,CAACsC,OAAO,GAAG,IAAI,CAACD,SAAS,CAACE,8BAA8B,CAAC,gBAAgB,IAAI,CAACC,IAAI,MAAM,CAAC;EAC/F;;uBATWJ,mBAAmB;;mBAAnBA,oBAAmB,EAAA9C,+DAAA,CAAAqB,mEAAA;AAAA;;QAAnByB,oBAAmB;EAAArB,SAAA;EAAAkB,MAAA;IAAAO,IAAA;EAAA;EAAAxB,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAuB,6BAAArB,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAHnB/B,uDAAA,aAAsD;;;MAAjCA,wDAAhB,QAAAgC,GAAA,CAAAgB,OAAA,EAAAhD,2DAAA,CAAe,QAAAgC,GAAA,CAAAkB,IAAA,CAAa;;;;;;;;;;;;;;;;;;;;;;;ACExC,MAAOI,sBAAsB;EAKjChD,YAAA;IAHS,KAAAkC,QAAQ,GAAW,CAAC;IAC7B,KAAAe,kBAAkB,GAAY,CAAC;EAEf;EAEhB7C,QAAQA,CAAA;IACN8C,WAAW,CAAC,MAAK;MACf;MACA,IAAI,CAACD,kBAAkB,GAAGE,IAAI,CAACC,KAAK,CAAC,IAAI,CAAClB,QAAQ,CAAC;IACrD,CAAC,EAAE,GAAG,CAAC;EACT;;0BAZWc,sBAAsB;;mBAAtBA,uBAAsB;AAAA;;QAAtBA,uBAAsB;EAAA7B,SAAA;EAAAkB,MAAA;IAAAH,QAAA;EAAA;EAAAd,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAA8B,gCAAA5B,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCL/B/B,4DAFJ,aAA6B,aACI,QAC1B;MAAAA,oDAAA,oBAAa;MAAAA,0DAAA,EAAI;;MAclBA,4DAZF,aAWC,cAKE;MACCA,uDAAA,0BAQE;MACJA,0DAAA,EAAO;MACPA,4DAAA,cAIC;MACCA,uDAAA,0BAQE;MACJA,0DAAA,EAAO;MACPA,4DAAA,cAIC;MACCA,uDAAA,2BAQE;MAGRA,0DAFI,EAAO,EACH,EACF;;MAENA,uDAAA,2BAA8D;MAC9DA,4DAAA,SAAG;MAAAA,oDAAA,IAAiC;MACtCA,0DADsC,EAAI,EACpC;;;MAFcA,uDAAA,IAAwB;MAAxBA,wDAAA,UAAAgC,GAAA,CAAAQ,QAAA,OAAwB;MACvCxC,uDAAA,GAAiC;MAAjCA,gEAAA,KAAAgC,GAAA,CAAAuB,kBAAA,cAAiC;;;;;;;;;;;;;;;;;;;;;;;;ACzDhC,MAAOM,mBAAmB;EAG9BvD,YAAoB+B,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;EAAoB;EAEvDI,OAAOA,CAAA;IACL,IAAI,CAACJ,eAAe,CAACI,OAAO,EAAE;EAChC;;uBAPWoB,mBAAmB;;mBAAnBA,oBAAmB,EAAA7D,+DAAA,CAAAqB,2DAAA;AAAA;;QAAnBwC,oBAAmB;EAAApC,SAAA;EAAAkB,MAAA;IAAAmB,QAAA;EAAA;EAAApC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAkC,6BAAAhC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCN5B/B,4DAFJ,iBAAY,kBACG,gBACA;MAAAA,oDAAA,YAAK;MAAAA,0DAAA,EAAY;MAE1BA,4DADF,qBAAwB,oBAC8B;MAAxCA,wDAAA,mBAAAiE,yDAAA;QAAA,OAASjC,GAAA,CAAAS,OAAA,EAAS;MAAA,EAAC;MAAqBzC,uDAAA,kBAAiD;MAG3GA,0DAH2G,EAAa,EACtG,EACF,EACH;MAGXA,4DADF,qBAAiC,aACF;MAC3BA,uDAAA,aAA8C;MAElDA,0DADE,EAAM,EACM;;;MAFLA,uDAAA,GAAgB;MAAhBA,wDAAA,QAAAgC,GAAA,CAAA8B,QAAA,EAAA9D,2DAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVsB;AAC4B;AACM;AACpC;AACuC;AACN;AACH;;AAOrE,MAAOoE,YAAY;gBAAZA,YAAY;;mBAAZA,aAAY;AAAA;;QAAZA;AAAY;;YAHbF,yDAAY,EAAEC,uDAAW;AAAA;;sHAGxBC,YAAY;IAAAC,YAAA,GAJRvB,mFAAmB,EAAEzC,yFAAqB,EAAEiD,4FAAsB,EAAElB,sFAAoB,EAAEyB,mFAAmB;IAAAS,OAAA,GAClHJ,yDAAY,EAAEC,uDAAW;IAAAI,OAAA,GACzBzB,mFAAmB,EAAEzC,yFAAqB,EAAEiD,4FAAsB,EAAElB,sFAAoB,EAAEyB,mFAAmB;EAAA;AAAA", "sources": ["./src/app/check-network/check-network.component.ts", "./src/app/check-network/check-network.component.html", "./src/app/custom-alert/custom-alert.component.ts", "./src/app/custom-alert/custom-alert.component.html", "./src/app/custom-icon/custom-icon.component.ts", "./src/app/custom-loading/custom-loading.component.ts", "./src/app/custom-loading/custom-loading.component.html", "./src/app/image-modal/image-modal.component.ts", "./src/app/image-modal/image-modal.component.html", "./src/app/shared/shared.module.ts"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { NetworkService } from '../services/network.service';\r\nimport { Platform } from '@ionic/angular';\r\nimport { Network } from '@capacitor/network';\r\n\r\n@Component({\r\n  selector: 'app-check-network',\r\n  templateUrl: './check-network.component.html',\r\n  styleUrls: ['./check-network.component.scss'],\r\n})\r\nexport class CheckNetworkComponent  implements OnInit {\r\n\r\n  isConnected = false; // Track network status\r\n\r\n\r\n  constructor(private platform: Platform, private networkService: NetworkService) { }\r\n\r\n  ngOnInit() {\r\n\r\n    \r\n\r\n\r\n    this.platform.ready().then(() => {\r\n      this.networkService.getNetworkStatus().subscribe((connected: boolean) => {\r\n        this.isConnected = connected;\r\n        console.log('Network status:', this.isConnected);\r\n      });\r\n\r\n      this.networkService.isOnline$.subscribe(result => {\r\n        this.isConnected = result;\r\n        console.log(result ? 'User is online' : 'User is offline');\r\n      });\r\n    });\r\n  }\r\n\r\n}\r\n", "<div *ngIf=\"!isConnected\" class=\"connecting\">\r\n  <ion-spinner name=\"crescent\" ></ion-spinner>\r\n  <p>Connecting...</p>\r\n</div>", "import { Component, Input} from '@angular/core';\r\nimport { ModalController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-custom-alert',\r\n  templateUrl: './custom-alert.component.html',\r\n  styleUrls: ['./custom-alert.component.scss'],\r\n})\r\nexport class CustomAlertComponent {\r\n\r\n  @Input() header?: string = \"Chargement...\";\r\n  @Input() message?: string = \"\";\r\n  @Input() progress: number = 0;\r\n\r\n  constructor(private modalController: ModalController) {}\r\n\r\n  dismiss() {\r\n    this.modalController.dismiss();\r\n  }\r\n}\r\n", "\r\n<div class=\"wrapper-progress-bar\">\r\n  <app-custom-loading [progress]=\"progress\"></app-custom-loading>\r\n</div>\r\n\r\n\r\n\r\n", "import { Component, Input, OnInit } from '@angular/core';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-custom-icon',\r\n  template: '<img [src]=\"iconUrl\" [alt]=\"name\" class=\"custom-icon\">',\r\n  styleUrls: ['./custom-icon.component.scss'],\r\n})\r\nexport class CustomIconComponent  implements OnInit {\r\n\r\n  @Input() name: string | undefined;\r\n  iconUrl: SafeResourceUrl | undefined;\r\n\r\n  constructor(private sanitizer: DomSanitizer) {}\r\n\r\n  ngOnInit() {\r\n    this.iconUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`assets/icons/${this.name}.svg`);\r\n  }\r\n\r\n}\r\n", "import { Component, OnInit, Input  } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-custom-loading',\r\n  templateUrl: './custom-loading.component.html',\r\n  styleUrls: ['./custom-loading.component.scss'],\r\n})\r\nexport class CustomLoadingComponent  implements OnInit {\r\n\r\n  @Input() progress: number = 0;\r\n  progress_displayed : number = 0\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n    setInterval(() => {\r\n      // progress_displayed for be integer not have coma\r\n      this.progress_displayed = Math.round(this.progress);\r\n    }, 100);\r\n  }\r\n\r\n}\r\n", "<div class=\"loading-content\">\r\n  <div class=\"head-progress-bar\">\r\n    <p>Chargement...</p>\r\n    <!-- <ion-spinner name=\"circles\"></ion-spinner> -->\r\n    <svg\r\n      class=\"circles\"\r\n      version=\"1.1\"\r\n      id=\"L7\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n      x=\"0px\"\r\n      y=\"0px\"\r\n      viewBox=\"0 0 100 100\"\r\n      enable-background=\"new 0 0 100 100\"\r\n      xml:space=\"preserve\"\r\n    >\r\n      <path\r\n        fill=\"#fff\"\r\n        d=\"M31.6,3.5C5.9,13.6-6.6,42.7,3.5,68.4c10.1,25.7,39.2,38.3,64.9,28.1l-3.1-7.9c-21.3,8.4-45.4-2-53.8-23.3\r\n    c-8.4-21.3,2-45.4,23.3-53.8L31.6,3.5z\"\r\n      >\r\n        <animateTransform\r\n          attributeName=\"transform\"\r\n          attributeType=\"XML\"\r\n          type=\"rotate\"\r\n          dur=\"2s\"\r\n          from=\"0 50 50\"\r\n          to=\"360 50 50\"\r\n          repeatCount=\"indefinite\"\r\n        />\r\n      </path>\r\n      <path\r\n        fill=\"#fff\"\r\n        d=\"M42.3,39.6c5.7-4.3,13.9-3.1,18.1,2.7c4.3,5.7,3.1,13.9-2.7,18.1l4.1,5.5c8.8-6.5,10.6-19,4.1-27.7\r\n    c-6.5-8.8-19-10.6-27.7-4.1L42.3,39.6z\"\r\n      >\r\n        <animateTransform\r\n          attributeName=\"transform\"\r\n          attributeType=\"XML\"\r\n          type=\"rotate\"\r\n          dur=\"1s\"\r\n          from=\"0 50 50\"\r\n          to=\"-360 50 50\"\r\n          repeatCount=\"indefinite\"\r\n        />\r\n      </path>\r\n      <path\r\n        fill=\"#fff\"\r\n        d=\"M82,35.7C74.1,18,53.4,10.1,35.7,18S10.1,46.6,18,64.3l7.6-3.4c-6-13.5,0-29.3,13.5-35.3s29.3,0,35.3,13.5\r\n    L82,35.7z\"\r\n      >\r\n        <animateTransform\r\n          attributeName=\"transform\"\r\n          attributeType=\"XML\"\r\n          type=\"rotate\"\r\n          dur=\"2s\"\r\n          from=\"0 50 50\"\r\n          to=\"360 50 50\"\r\n          repeatCount=\"indefinite\"\r\n        />\r\n      </path>\r\n    </svg>\r\n  </div>\r\n\r\n  <ion-progress-bar [value]=\"progress / 100\"></ion-progress-bar>\r\n  <p>{{ progress_displayed }}% complet</p>\r\n</div>\r\n", "import { Component, Input } from '@angular/core';\r\nimport { ModalController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-image-modal',\r\n  templateUrl: './image-modal.component.html',\r\n  styleUrls: ['./image-modal.component.scss'],\r\n})\r\nexport class ImageModalComponent {\r\n  @Input() imageSrc: string | undefined;\r\n\r\n  constructor(private modalController: ModalController) {}\r\n\r\n  dismiss() {\r\n    this.modalController.dismiss();\r\n  }\r\n}\r\n", "<ion-header>\r\n  <ion-toolbar>\r\n    <ion-title>Image</ion-title>\r\n    <ion-buttons slot=\"end\">\r\n      <ion-button (click)=\"dismiss()\" class=\"close-modal\"><ion-icon name=\"close-circle-outline\"></ion-icon></ion-button>\r\n    </ion-buttons>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content class=\"ion-padding\">\r\n  <div class=\"image-container\">\r\n    <img [src]=\"imageSrc\" class=\"enlarged-image\"/>\r\n  </div>\r\n</ion-content>\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { CustomIconComponent } from '../custom-icon/custom-icon.component';\r\nimport { CheckNetworkComponent } from '../check-network/check-network.component';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CustomLoadingComponent } from '../custom-loading/custom-loading.component';\r\nimport { CustomAlertComponent } from '../custom-alert/custom-alert.component';\r\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\r\n\r\n@NgModule({\r\n  declarations: [CustomIconComponent, CheckNetworkComponent, CustomLoadingComponent, CustomAlertComponent, ImageModalComponent ],\r\n  imports: [CommonModule, IonicModule],\r\n  exports: [CustomIconComponent, CheckNetworkComponent, CustomLoadingComponent, CustomAlertComponent, ImageModalComponent] // Export it so it can be used in other modules\r\n})\r\nexport class SharedModule {}"], "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "CheckNetworkComponent", "constructor", "platform", "networkService", "isConnected", "ngOnInit", "ready", "then", "getNetworkStatus", "subscribe", "connected", "console", "log", "isOnline$", "result", "ɵɵdirectiveInject", "i1", "Platform", "i2", "NetworkService", "selectors", "decls", "vars", "consts", "template", "CheckNetworkComponent_Template", "rf", "ctx", "ɵɵtemplate", "CheckNetworkComponent_div_0_Template", "ɵɵproperty", "CustomAlertComponent", "modalController", "header", "message", "progress", "dismiss", "ModalController", "inputs", "CustomAlertComponent_Template", "ɵɵadvance", "CustomIconComponent", "sanitizer", "iconUrl", "bypassSecurityTrustResourceUrl", "name", "Dom<PERSON><PERSON><PERSON>zer", "CustomIconComponent_Template", "ɵɵsanitizeUrl", "CustomLoadingComponent", "progress_displayed", "setInterval", "Math", "round", "CustomLoadingComponent_Template", "ɵɵtextInterpolate1", "ImageModalComponent", "imageSrc", "ImageModalComponent_Template", "ɵɵlistener", "ImageModalComponent_Template_ion_button_click_5_listener", "CommonModule", "IonicModule", "SharedModule", "declarations", "imports", "exports"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}