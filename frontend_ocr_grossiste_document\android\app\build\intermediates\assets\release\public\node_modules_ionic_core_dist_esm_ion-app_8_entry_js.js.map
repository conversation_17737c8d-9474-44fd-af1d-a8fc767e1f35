{"version": 3, "file": "node_modules_ionic_core_dist_esm_ion-app_8_entry_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAC8J;AACnF;AAChB;AACgC;AAC0D;AACtG;AACiC;AAC+B;AAC7B;AACR;AACoB;AACpB;AACpB;AACzB;AACG;AACC;AAEjC,MAAM6C,MAAM,GAAG,uSAAuS;AACtT,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;EACnC;EACAC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACf;MACIC,GAAG,eAAAC,6OAAA,CAAC,aAAY;QACZ,MAAMC,QAAQ,GAAG/B,4DAAU,CAACgC,MAAM,EAAE,QAAQ,CAAC;QAC7C,IAAI,CAAClC,wDAAM,CAACmC,UAAU,CAAC,UAAU,CAAC,EAAE;UAChC,iLAA6B,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,aAAa,CAACtC,wDAAM,CAAC,CAAC;QAChF;QACA,IAAIA,wDAAM,CAACmC,UAAU,CAAC,WAAW,EAAEF,QAAQ,CAAC,EAAE;UAC1C,2LAAkC,CAACG,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACE,cAAc,CAAC,CAAC,CAAC;QAChF;QACA,IAAIvC,wDAAM,CAACmC,UAAU,CAAC,YAAY,EAAEK,cAAc,CAAC,CAAC,CAAC,EAAE;UACnD;AACpB;AACA;AACA;UACoB,MAAMC,QAAQ,GAAGvC,4DAAU,CAACgC,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;UAC9D,6LAAmC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACK,eAAe,CAAC1C,wDAAM,EAAEyC,QAAQ,CAAC,CAAC;QAClG;QACA,MAAME,wBAAwB,SAAS,qIAA4C;QACnF,MAAMC,gCAAgC,GAAGX,QAAQ,IAAIvC,wFAAqB,CAAC,CAAC;QAC5E,IAAIM,wDAAM,CAACmC,UAAU,CAAC,oBAAoB,EAAES,gCAAgC,CAAC,EAAE;UAC3ED,wBAAwB,CAACE,uBAAuB,CAAC,CAAC;QACtD,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB,IAAInD,wFAAqB,CAAC,CAAC,EAAE;YACzBE,qDAAe,CAAC,iKAAiK,CAAC;UACtL;UACA+C,wBAAwB,CAACG,uBAAuB,CAAC,CAAC;QACtD;QACA,IAAI,OAAOZ,MAAM,KAAK,WAAW,EAAE;UAC/B,wIAAgC,CAACE,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACU,mBAAmB,CAACb,MAAM,CAAC,CAAC;QACzF;QACA,6IAAqC,CAACE,IAAI,CAAEC,MAAM,IAAMP,KAAI,CAACkB,YAAY,GAAGX,MAAM,CAACY,iBAAiB,CAAC,CAAE,CAAC;MAC5G,CAAC,EAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApB,6OAAA;MACrB,IAAIoB,MAAI,CAACJ,YAAY,EAAE;QACnBI,MAAI,CAACJ,YAAY,CAACE,QAAQ,CAACC,QAAQ,CAAC;MACxC;IAAC;EACL;EACAE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,UAAU,EAAE,IAAI;QAChB,yBAAyB,EAAEtD,wDAAM,CAACmC,UAAU,CAAC,wBAAwB;MACzE;IAAE,CAAC,CAAC;EACZ;EACA,IAAIsB,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAMuD,cAAc,GAAGA,CAAA,KAAM;EACzB;AACJ;AACA;EACI,MAAMkB,aAAa,GAAGxD,4DAAU,CAACgC,MAAM,EAAE,KAAK,CAAC,IAAIhC,4DAAU,CAACgC,MAAM,EAAE,QAAQ,CAAC;EAC/E,IAAIwB,aAAa,EAAE;IACf,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMC,kBAAkB,GAAGzD,4DAAU,CAACgC,MAAM,EAAE,SAAS,CAAC,IAAIhC,4DAAU,CAACgC,MAAM,EAAE,WAAW,CAAC;EAC3F,IAAIyB,kBAAkB,EAAE;IACpB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAM5B,GAAG,GAAI6B,QAAQ,IAAK;EACtB,IAAI,qBAAqB,IAAI1B,MAAM,EAAE;IACjCA,MAAM,CAAC2B,mBAAmB,CAACD,QAAQ,CAAC;EACxC,CAAC,MACI;IACDE,UAAU,CAACF,QAAQ,EAAE,EAAE,CAAC;EAC5B;AACJ,CAAC;AACDlC,GAAG,CAACqC,KAAK,GAAGtC,YAAY;AAExB,MAAMuC,aAAa,GAAG,8pFAA8pF;AACprF,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,YAAY,GAAG,y9FAAy9F;AAC9+F,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,OAAO,GAAG,MAAM;EAClBzC,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACyC,QAAQ,GAAG,KAAK;EACzB;EACAhB,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,CAAC,kBAAkB,GAAG,IAAI,CAACe;MAC/B;IAAE,CAAC,EAAExF,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACDa,OAAO,CAACL,KAAK,GAAG;EACZO,GAAG,EAAEL,mBAAmB;EACxBM,EAAE,EAAEJ;AACR,CAAC;AAED,MAAMK,UAAU,GAAG,wmFAAwmF;AAC3nF,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,OAAO,GAAG,MAAM;EAClB/C,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAAC+C,cAAc,GAAGxF,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACyF,SAAS,GAAGzF,qDAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC0F,YAAY,GAAG1F,qDAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAAC2F,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG;MACVC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAEC,SAAS;MAChBC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,IAAI,EAAEX,SAAS;MACfb,WAAW,EAAE;IACjB,CAAC;IACD,IAAI,CAACyB,KAAK,GAAGZ,SAAS;IACtB,IAAI,CAACa,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,kBAAkB,GAAG,OAAO;IACjC,IAAI,CAACC,eAAe,GAAGf,SAAS;IAChC,IAAI,CAACgB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC3B,aAAa,GAAG,IAAI,CAAC3B,EAAE,CAACuD,OAAO,CAAC,kCAAkC,CAAC,KAAK,IAAI;IACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI5G,uDAAY,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMwD,WAAW,GAAI,IAAI,CAAC3B,WAAW,GAAG,IAAI,CAAC7B,EAAE,CAACuD,OAAO,CAAC,UAAU,CAAE;MACpE,IAAIC,WAAW,KAAK,IAAI,EAAE;QACtB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,CAACC,gBAAgB,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;QAC3CF,WAAW,CAACG,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAACF,gBAAgB,CAAC;MAC1E;IACJ;EACJ;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIlH,uDAAY,CAAC,IAAI,CAACqD,EAAE,CAAC,EAAE;MACvB;AACZ;AACA;AACA;AACA;AACA;MACY,MAAM;QAAE6B,WAAW;QAAE4B;MAAiB,CAAC,GAAG,IAAI;MAC9C,IAAI5B,WAAW,KAAK,IAAI,IAAI4B,gBAAgB,KAAKtB,SAAS,EAAE;QACxDN,WAAW,CAACiC,mBAAmB,CAAC,iBAAiB,EAAEL,gBAAgB,CAAC;MACxE;MACA,IAAI,CAAC5B,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC4B,gBAAgB,GAAGtB,SAAS;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACnC,aAAa,EAAE;MACpBoC,YAAY,CAAC,IAAI,CAACpC,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACA,aAAa,GAAGvB,UAAU,CAAC,MAAM;MAClC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACL,EAAE,CAACiE,YAAY,KAAK,IAAI,EAAE;QAC/B;MACJ;MACA,IAAI,CAACP,MAAM,CAAC,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;EACX;EACAQ,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEhB;IAAgB,CAAC,GAAG,IAAI;IAChC,MAAMrD,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,OAAO6G,eAAe,KAAKf,SAAS,GAAGtC,IAAI,KAAK,KAAK,IAAIpD,4DAAU,CAAC,KAAK,CAAC,GAAGyG,eAAe;EAChG;EACAQ,MAAMA,CAAA,EAAG;IACL;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ;MACI,IAAI,IAAI,CAACV,UAAU,EAAE;QACjBpH,qDAAQ,CAAC,MAAM,IAAI,CAACuI,cAAc,CAAC,CAAC,CAAC;MACzC,CAAC,MACI,IAAI,IAAI,CAAC1C,IAAI,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,EAAE;QAC5C,IAAI,CAACD,IAAI,GAAG,IAAI,CAACC,OAAO,GAAG,CAAC;QAC5B5F,qDAAW,CAAC,IAAI,CAAC;MACrB;IACJ;EACJ;EACAqI,cAAcA,CAAA,EAAG;IACb,MAAMC,IAAI,GAAGC,cAAc,CAAC,IAAI,CAACrE,EAAE,CAAC;IACpC,MAAMsE,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACxE,EAAE,CAACyE,SAAS,EAAE,CAAC,CAAC;IAC1C,MAAMC,MAAM,GAAGH,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACO,YAAY,GAAGL,GAAG,GAAG,IAAI,CAACtE,EAAE,CAAC2E,YAAY,EAAE,CAAC,CAAC;IAC1E,MAAMC,KAAK,GAAGN,GAAG,KAAK,IAAI,CAAC7C,IAAI,IAAIiD,MAAM,KAAK,IAAI,CAAChD,OAAO;IAC1D,IAAIkD,KAAK,EAAE;MACP,IAAI,CAACnD,IAAI,GAAG6C,GAAG;MACf,IAAI,CAAC5C,OAAO,GAAGgD,MAAM;MACrB5I,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACA+I,QAAQA,CAACC,EAAE,EAAE;IACT,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAC5D,WAAW;IACrC,IAAI,CAACC,UAAU,GAAGwD,SAAS;IAC3B,IAAIG,WAAW,EAAE;MACb,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA,IAAI,CAAC,IAAI,CAAC3D,MAAM,IAAI,IAAI,CAAC6B,YAAY,EAAE;MACnC,IAAI,CAAC7B,MAAM,GAAG,IAAI;MAClB5F,qDAAQ,CAAEwJ,EAAE,IAAK;QACb,IAAI,CAAC5D,MAAM,GAAG,KAAK;QACnB,IAAI,CAACM,MAAM,CAACI,KAAK,GAAG4C,EAAE;QACtBO,kBAAkB,CAAC,IAAI,CAACvD,MAAM,EAAE,IAAI,CAACwD,QAAQ,EAAEF,EAAE,EAAEF,WAAW,CAAC;QAC/D,IAAI,CAAC/D,SAAS,CAACoE,IAAI,CAAC,IAAI,CAACzD,MAAM,CAAC;MACpC,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUvE,gBAAgBA,CAAA,EAAG;IAAA,IAAAiI,MAAA;IAAA,OAAAjH,6OAAA;MACrB;AACR;AACA;AACA;MACQ,IAAI,CAACiH,MAAI,CAACF,QAAQ,EAAE;QAChB,MAAM,IAAIG,OAAO,CAAEC,OAAO,IAAK9I,uDAAgB,CAAC4I,MAAI,CAACxF,EAAE,EAAE0F,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAACF,QAAQ,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACUK,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArH,6OAAA;MACzB,IAAI,CAACqH,MAAI,CAACC,mBAAmB,EAAE;QAC3B,MAAM,IAAIJ,OAAO,CAAEC,OAAO,IAAK9I,uDAAgB,CAACgJ,MAAI,CAAC5F,EAAE,EAAE0F,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACE,MAAI,CAACC,mBAAmB,CAAC;IAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,GAAG,CAAC,EAAE;IACtB,OAAO,IAAI,CAACC,aAAa,CAAC7D,SAAS,EAAE,CAAC,EAAE4D,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;EACUE,cAAcA,CAACF,QAAQ,GAAG,CAAC,EAAE;IAAA,IAAAG,MAAA;IAAA,OAAA3H,6OAAA;MAC/B,MAAM+G,QAAQ,SAASY,MAAI,CAAC3I,gBAAgB,CAAC,CAAC;MAC9C,MAAM4I,CAAC,GAAGb,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;MACvD,OAAOH,MAAI,CAACF,aAAa,CAAC7D,SAAS,EAAEgE,CAAC,EAAEJ,QAAQ,CAAC;IAAC;EACtD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUO,aAAaA,CAACC,CAAC,EAAEJ,CAAC,EAAEJ,QAAQ,EAAE;IAAA,IAAAS,MAAA;IAAA,OAAAjI,6OAAA;MAChC,MAAM+G,QAAQ,SAASkB,MAAI,CAACjJ,gBAAgB,CAAC,CAAC;MAC9C,OAAOiJ,MAAI,CAACR,aAAa,CAACO,CAAC,GAAGjB,QAAQ,CAACtD,UAAU,EAAEmE,CAAC,GAAGb,QAAQ,CAACvD,SAAS,EAAEgE,QAAQ,CAAC;IAAC;EACzF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUC,aAAaA,CAACO,CAAC,EAAEJ,CAAC,EAAEJ,QAAQ,GAAG,CAAC,EAAE;IAAA,IAAAU,MAAA;IAAA,OAAAlI,6OAAA;MACpC,MAAMyB,EAAE,SAASyG,MAAI,CAAClJ,gBAAgB,CAAC,CAAC;MACxC,IAAIwI,QAAQ,GAAG,EAAE,EAAE;QACf,IAAII,CAAC,IAAI,IAAI,EAAE;UACXnG,EAAE,CAAC+B,SAAS,GAAGoE,CAAC;QACpB;QACA,IAAII,CAAC,IAAI,IAAI,EAAE;UACXvG,EAAE,CAACgC,UAAU,GAAGuE,CAAC;QACrB;QACA;MACJ;MACA,IAAIb,OAAO;MACX,IAAIpD,SAAS,GAAG,CAAC;MACjB,MAAMoE,OAAO,GAAG,IAAIjB,OAAO,CAAEvK,CAAC,IAAMwK,OAAO,GAAGxK,CAAE,CAAC;MACjD,MAAMyL,KAAK,GAAG3G,EAAE,CAAC+B,SAAS;MAC1B,MAAM6E,KAAK,GAAG5G,EAAE,CAACgC,UAAU;MAC3B,MAAMY,MAAM,GAAGuD,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGQ,KAAK,GAAG,CAAC;MACxC,MAAMhE,MAAM,GAAG4D,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGK,KAAK,GAAG,CAAC;MACxC;MACA,MAAMC,IAAI,GAAI9B,SAAS,IAAK;QACxB,MAAM+B,UAAU,GAAGvC,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE,CAAChC,SAAS,GAAGzC,SAAS,IAAIyD,QAAQ,CAAC,GAAG,CAAC;QACtE,MAAMiB,MAAM,GAAGzC,IAAI,CAAC0C,GAAG,CAACH,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC;QAC1C,IAAIlE,MAAM,KAAK,CAAC,EAAE;UACd5C,EAAE,CAAC+B,SAAS,GAAGwC,IAAI,CAAC2C,KAAK,CAACF,MAAM,GAAGpE,MAAM,GAAG+D,KAAK,CAAC;QACtD;QACA,IAAIhE,MAAM,KAAK,CAAC,EAAE;UACd3C,EAAE,CAACgC,UAAU,GAAGuC,IAAI,CAAC2C,KAAK,CAACF,MAAM,GAAGrE,MAAM,GAAGiE,KAAK,CAAC;QACvD;QACA,IAAII,MAAM,GAAG,CAAC,EAAE;UACZ;UACA;UACAG,qBAAqB,CAACN,IAAI,CAAC;QAC/B,CAAC,MACI;UACDnB,OAAO,CAAC,CAAC;QACb;MACJ,CAAC;MACD;MACAyB,qBAAqB,CAAE/B,EAAE,IAAK;QAC1B9C,SAAS,GAAG8C,EAAE;QACdyB,IAAI,CAACzB,EAAE,CAAC;MACZ,CAAC,CAAC;MACF,OAAOsB,OAAO;IAAC;EACnB;EACAvB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC7D,WAAW,GAAG,IAAI;IACvB,IAAI,CAACJ,cAAc,CAACqE,IAAI,CAAC;MACrBjE,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf+F,aAAa,CAAC,IAAI,CAAC/F,QAAQ,CAAC;IAChC;IACA;IACA,IAAI,CAACA,QAAQ,GAAGgG,WAAW,CAAC,MAAM;MAC9B,IAAI,IAAI,CAAC9F,UAAU,GAAGyD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;QACpC,IAAI,CAACpB,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,EAAE,GAAG,CAAC;EACX;EACAA,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxC,QAAQ,EACb+F,aAAa,CAAC,IAAI,CAAC/F,QAAQ,CAAC;IAChC,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,GAAG,KAAK;MACxB,IAAI,CAACF,YAAY,CAACmE,IAAI,CAAC;QACnBjE,WAAW,EAAE;MACjB,CAAC,CAAC;IACN;EACJ;EACA1B,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEqD,kBAAkB;MAAEtB,aAAa;MAAEwB,OAAO;MAAEC,OAAO;MAAEpD;IAAG,CAAC,GAAG,IAAI;IACxE,MAAMsH,GAAG,GAAGrK,mDAAK,CAAC+C,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMH,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM6G,eAAe,GAAG,IAAI,CAACgB,qBAAqB,CAAC,CAAC;IACpD,MAAMqD,gBAAgB,GAAG1H,IAAI,KAAK,KAAK;IACvC,IAAI,CAAC6D,MAAM,CAAC,CAAC;IACb,OAAQtI,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAE0H,IAAI,EAAE7F,aAAa,GAAG,MAAM,GAAGQ,SAAS;MAAEpC,KAAK,EAAE7C,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QAC3I,CAAClD,IAAI,GAAG,IAAI;QACZ,gBAAgB,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE,CAAC;QACrDyH,UAAU,EAAEvE,eAAe;QAC3B,CAAE,WAAUoE,GAAI,EAAC,GAAG;MACxB,CAAC,CAAC;MAAEhH,KAAK,EAAE;QACP,cAAc,EAAG,GAAE,IAAI,CAACmB,IAAK,IAAG;QAChC,iBAAiB,EAAG,GAAE,IAAI,CAACC,OAAQ;MACvC;IAAE,CAAC,EAAEtG,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4H,GAAG,EAAG1H,EAAE,IAAM,IAAI,CAAC6F,mBAAmB,GAAG7F,EAAG;MAAE2H,EAAE,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAC,EAAE3E,kBAAkB,KAAK,QAAQ,GAAG7H,qDAAC,CAAC,MAAM,EAAE;MAAEyM,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG,IAAI,EAAEzM,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QACtS,cAAc,EAAE,IAAI;QACpB,UAAU,EAAEoD,OAAO;QACnB,UAAU,EAAEC,OAAO;QACnBqE,UAAU,EAAE,CAACtE,OAAO,IAAIC,OAAO,KAAKF;MACxC,CAAC;MAAEwE,GAAG,EAAGpC,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA,QAAS;MAAET,QAAQ,EAAE,IAAI,CAACxB,YAAY,GAAIyB,EAAE,IAAK,IAAI,CAACD,QAAQ,CAACC,EAAE,CAAC,GAAG3C,SAAS;MAAEyF,IAAI,EAAE;IAAS,CAAC,EAAExM,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAEyH,gBAAgB,GAAInM,qDAAC,CAAC,KAAK,EAAE;MAAE2E,KAAK,EAAE;IAAoB,CAAC,EAAE3E,qDAAC,CAAC,KAAK,EAAE;MAAE2E,KAAK,EAAE;IAAmB,CAAC,CAAC,EAAE3E,qDAAC,CAAC,KAAK,EAAE;MAAE2E,KAAK,EAAE;IAAoB,CAAC,CAAC,CAAC,GAAI,IAAI,EAAEkD,kBAAkB,KAAK,OAAO,GAAG7H,qDAAC,CAAC,MAAM,EAAE;MAAEyM,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;EAChb;EACA,IAAI7H,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,MAAMsM,gBAAgB,GAAI9H,EAAE,IAAK;EAC7B,IAAI+H,EAAE;EACN,IAAI/H,EAAE,CAACgI,aAAa,EAAE;IAClB;IACA,OAAOhI,EAAE,CAACgI,aAAa;EAC3B;EACA,IAAI,CAACD,EAAE,GAAG/H,EAAE,CAACiI,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,EAAE;IACnE;IACA,OAAOlI,EAAE,CAACiI,UAAU,CAACC,IAAI;EAC7B;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAM7D,cAAc,GAAIrE,EAAE,IAAK;EAC3B,MAAMmI,IAAI,GAAGnI,EAAE,CAACuD,OAAO,CAAC,UAAU,CAAC;EACnC,IAAI4E,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,MAAM/D,IAAI,GAAGpE,EAAE,CAACuD,OAAO,CAAC,4DAA4D,CAAC;EACrF,IAAIa,IAAI,EAAE;IACN,OAAOA,IAAI;EACf;EACA,OAAO0D,gBAAgB,CAAC9H,EAAE,CAAC;AAC/B,CAAC;AACD;AACA,MAAMqF,kBAAkB,GAAGA,CAACvD,MAAM,EAAE9B,EAAE,EAAEoI,SAAS,EAAElD,WAAW,KAAK;EAC/D,MAAMmD,KAAK,GAAGvG,MAAM,CAACS,QAAQ;EAC7B,MAAM+F,KAAK,GAAGxG,MAAM,CAACU,QAAQ;EAC7B,MAAM+F,KAAK,GAAGzG,MAAM,CAACe,WAAW;EAChC,MAAMN,QAAQ,GAAGvC,EAAE,CAACgC,UAAU;EAC9B,MAAMQ,QAAQ,GAAGxC,EAAE,CAAC+B,SAAS;EAC7B,MAAMyG,SAAS,GAAGJ,SAAS,GAAGG,KAAK;EACnC,IAAIrD,WAAW,EAAE;IACb;IACApD,MAAM,CAACQ,SAAS,GAAG8F,SAAS;IAC5BtG,MAAM,CAACM,MAAM,GAAGG,QAAQ;IACxBT,MAAM,CAACO,MAAM,GAAGG,QAAQ;IACxBV,MAAM,CAACW,SAAS,GAAGX,MAAM,CAACY,SAAS,GAAG,CAAC;EAC3C;EACAZ,MAAM,CAACe,WAAW,GAAGuF,SAAS;EAC9BtG,MAAM,CAACS,QAAQ,GAAGT,MAAM,CAACE,UAAU,GAAGO,QAAQ;EAC9CT,MAAM,CAACU,QAAQ,GAAGV,MAAM,CAACC,SAAS,GAAGS,QAAQ;EAC7CV,MAAM,CAACa,MAAM,GAAGJ,QAAQ,GAAGT,MAAM,CAACM,MAAM;EACxCN,MAAM,CAACc,MAAM,GAAGJ,QAAQ,GAAGV,MAAM,CAACO,MAAM;EACxC,IAAImG,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,GAAG,EAAE;IAClC,MAAM/F,SAAS,GAAG,CAACF,QAAQ,GAAG8F,KAAK,IAAIG,SAAS;IAChD,MAAM9F,SAAS,GAAG,CAACF,QAAQ,GAAG8F,KAAK,IAAIE,SAAS;IAChD1G,MAAM,CAACW,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGX,MAAM,CAACW,SAAS,GAAG,GAAG;IAC3DX,MAAM,CAACY,SAAS,GAAGA,SAAS,GAAG,GAAG,GAAGZ,MAAM,CAACY,SAAS,GAAG,GAAG;EAC/D;AACJ,CAAC;AACDzB,OAAO,CAACX,KAAK,GAAGU,gBAAgB;AAEhC,MAAMyH,gBAAgB,GAAGA,CAACnD,QAAQ,EAAEoD,MAAM,KAAK;EAC3C9M,qDAAQ,CAAC,MAAM;IACX,MAAMmG,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAM4G,SAAS,GAAGrD,QAAQ,CAACc,YAAY,GAAGd,QAAQ,CAACe,YAAY;IAC/D;AACR;AACA;AACA;IACQ,MAAMuC,YAAY,GAAG,EAAE;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,SAAS,GAAGF,SAAS,GAAGC,YAAY;IAC1C,MAAME,eAAe,GAAG/G,SAAS,GAAG8G,SAAS;IAC7C,MAAME,KAAK,GAAGlM,uDAAK,CAAC,CAAC,EAAE,CAAC,GAAGiM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IAC7D5M,qDAAS,CAAC,MAAM;MACZ0M,MAAM,CAACpI,KAAK,CAAC0I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,YAAY,GAAG,kqBAAkqB;AACvrB,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,yfAAyf;AAC7gB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBpL,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAoL,MAAA;IACjBpO,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACqL,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,sBAAsB,GAAG,MAAM;MAChC,MAAM5J,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;MAC7B,IAAIwD,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEe;MAAS,CAAC,GAAG,IAAI;MACzB,MAAM8I,OAAO,GAAG9I,QAAQ,KAAK,MAAM;MACnC,IAAI,CAAC+I,wBAAwB,CAAC,CAAC;MAC/B,IAAID,OAAO,EAAE;QACT,MAAME,MAAM,GAAG,IAAI,CAAC5J,EAAE,CAACuD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAMsG,SAAS,GAAGD,MAAM,GAAGxM,qDAAc,CAACwM,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZxM,qDAAuB,CAAC,IAAI,CAAC2C,EAAE,CAAC;UAChC;QACJ;QACA,IAAI,CAAC8J,eAAe,CAACD,SAAS,CAAC;MACnC;IACJ,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAAxL,6OAAA,CAAG,WAAOsL,SAAS,EAAK;QACxC,MAAMvE,QAAQ,GAAIiE,MAAI,CAACjE,QAAQ,SAAS/H,qDAAgB,CAACsM,SAAS,CAAE;QACpE;AACZ;AACA;QACYN,MAAI,CAACS,qBAAqB,GAAG,MAAM;UAC/BvB,gBAAgB,CAACnD,QAAQ,EAAEiE,MAAI,CAACvJ,EAAE,CAAC;QACvC,CAAC;QACDsF,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAE4F,MAAI,CAACS,qBAAqB,CAAC;QAC/DvB,gBAAgB,CAACnD,QAAQ,EAAEiE,MAAI,CAACvJ,EAAE,CAAC;MACvC,CAAC;MAAA,iBAAAiK,EAAA;QAAA,OAAAF,KAAA,CAAAG,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACxJ,QAAQ,GAAGuB,SAAS;IACzB,IAAI,CAACkI,WAAW,GAAG,KAAK;EAC5B;EACAjM,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACqL,sBAAsB,CAAC,CAAC;EACjC;EACAa,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACb,sBAAsB,CAAC,CAAC;EACjC;EACMnG,iBAAiBA,CAAA,EAAG;IAAA,IAAAiH,MAAA;IAAA,OAAAhM,6OAAA;MACtBgM,MAAI,CAACf,YAAY,SAAShM,mEAAwB;QAAA,IAAAgN,KAAA,GAAAjM,6OAAA,CAAC,WAAOkM,YAAY,EAAEC,aAAa,EAAK;UACtF;AACZ;AACA;AACA;AACA;UACY,IAAID,YAAY,KAAK,KAAK,IAAIC,aAAa,KAAKvI,SAAS,EAAE;YACvD,MAAMuI,aAAa;UACvB;UACAH,MAAI,CAACH,eAAe,GAAGK,YAAY,CAAC,CAAC;QACzC,CAAC;QAAA,iBAAAE,GAAA,EAAAC,GAAA;UAAA,OAAAJ,KAAA,CAAAN,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACAvG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC4F,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACqB,OAAO,CAAC,CAAC;IAC/B;EACJ;EACAlB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACrE,QAAQ,IAAI,IAAI,CAAC0E,qBAAqB,EAAE;MAC7C,IAAI,CAAC1E,QAAQ,CAACxB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACkG,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAG7H,SAAS;IAC1C;EACJ;EACAvC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEyK,WAAW;MAAEzJ;IAAS,CAAC,GAAG,IAAI;IACtC,MAAMf,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAM8L,IAAI,GAAG,IAAI,CAACnI,EAAE,CAACuD,OAAO,CAAC,UAAU,CAAC;IACxC,MAAMuH,MAAM,GAAG3C,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC4C,aAAa,CAAC,sBAAsB,CAAC;IACrG,OAAQ3P,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAE0H,IAAI,EAAE,aAAa;MAAEzH,KAAK,EAAE;QACvF,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,UAASA,IAAK,EAAC,GAAG,IAAI;QACxB,CAAE,oBAAmB,GAAGwK,WAAW;QACnC,CAAE,sBAAqBxK,IAAK,EAAC,GAAGwK,WAAW;QAC3C,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAACD,eAAe,KAAK,CAACU,MAAM,IAAIA,MAAM,CAACE,IAAI,KAAK,QAAQ,CAAC;QAC1F,CAAE,mBAAkBpK,QAAS,EAAC,GAAGA,QAAQ,KAAKuB;MAClD;IAAE,CAAC,EAAEtC,IAAI,KAAK,KAAK,IAAIwK,WAAW,IAAIjP,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxM;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD8N,MAAM,CAAChJ,KAAK,GAAG;EACXO,GAAG,EAAEsI,kBAAkB;EACvBrI,EAAE,EAAEuI;AACR,CAAC;AAED,MAAM4B,UAAU,GAAG,sBAAsB;AACzC,MAAMC,YAAY,GAAIC,OAAO,IAAK;EAC9B,MAAMC,WAAW,GAAGC,QAAQ,CAACN,aAAa,CAAE,GAAEI,OAAQ,qBAAoB,CAAC;EAC3E,IAAIC,WAAW,KAAK,IAAI,EAAE;IACtB,OAAOA,WAAW;EACtB;EACA,MAAME,QAAQ,GAAGD,QAAQ,CAACE,aAAa,CAACJ,OAAO,CAAC;EAChDG,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;EAC5CH,QAAQ,CAAChL,KAAK,CAAC0I,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC;EAC7CqC,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;EACnC,OAAOA,QAAQ;AACnB,CAAC;AACD,MAAMM,iBAAiB,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE;IACX;EACJ;EACA,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,gBAAgB,CAAC,aAAa,CAAC;EACzD,OAAO;IACH/L,EAAE,EAAE6L,QAAQ;IACZC,QAAQ,EAAEE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,GAAG,CAAEC,OAAO,IAAK;MAC5C,MAAMC,UAAU,GAAGD,OAAO,CAACpB,aAAa,CAAC,WAAW,CAAC;MACrD,OAAO;QACH/K,EAAE,EAAEmM,OAAO;QACXE,UAAU,EAAEF,OAAO,CAACG,UAAU,CAACvB,aAAa,CAAC,qBAAqB,CAAC;QACnEqB,UAAU;QACVG,YAAY,EAAEH,UAAU,GAAGA,UAAU,CAACE,UAAU,CAACvB,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI;QACvFyB,YAAY,EAAER,KAAK,CAACC,IAAI,CAACE,OAAO,CAACJ,gBAAgB,CAAC,aAAa,CAAC;MACpE,CAAC;IACL,CAAC;EACL,CAAC;AACL,CAAC;AACD,MAAMU,mBAAmB,GAAGA,CAACnH,QAAQ,EAAEoH,iBAAiB,EAAE7C,SAAS,KAAK;EACpEjO,qDAAQ,CAAC,MAAM;IACX,MAAMmG,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAMgH,KAAK,GAAGlM,uDAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAACkF,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC;IACjD;IACA,MAAM4K,eAAe,GAAG9C,SAAS,CAACkB,aAAa,CAAC,gCAAgC,CAAC;IACjF,IAAI4B,eAAe,KAAK,IAAI,EAAE;MAC1B3Q,qDAAS,CAAC,MAAM;QACZ4Q,gBAAgB,CAACF,iBAAiB,CAACZ,QAAQ,EAAE/C,KAAK,CAAC;MACvD,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAM8D,2BAA2B,GAAGA,CAAChB,QAAQ,EAAEiB,OAAO,KAAK;EACvD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIjB,QAAQ,CAACjL,QAAQ,KAAK,MAAM,EAAE;IAC9B;EACJ;EACA,IAAIkM,OAAO,KAAK3K,SAAS,EAAE;IACvB0J,QAAQ,CAACvL,KAAK,CAACyM,cAAc,CAAC,iBAAiB,CAAC;EACpD,CAAC,MACI;IACDlB,QAAQ,CAACvL,KAAK,CAAC0I,WAAW,CAAC,iBAAiB,EAAE8D,OAAO,CAAC7D,QAAQ,CAAC,CAAC,CAAC;EACrE;AACJ,CAAC;AACD,MAAM+D,+BAA+B,GAAGA,CAAClI,EAAE,EAAEmI,eAAe,EAAElL,SAAS,KAAK;EACxE,IAAI,CAAC+C,EAAE,CAAC,CAAC,CAAC,CAACoI,cAAc,EAAE;IACvB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMnE,KAAK,GAAGjE,EAAE,CAAC,CAAC,CAAC,CAACqI,iBAAiB,GAAG,GAAG,IAAIpL,SAAS,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG+C,EAAE,CAAC,CAAC,CAAC,CAACqI,iBAAiB,IAAI,GAAG,GAAI,EAAE;EAC9GN,2BAA2B,CAACI,eAAe,CAACjN,EAAE,EAAE+I,KAAK,KAAK,CAAC,GAAG5G,SAAS,GAAG4G,KAAK,CAAC;AACpF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqE,yBAAyB,GAAGA,CAACtI,EAAE;AAAE;AACvCmI,eAAe,EAAEP,iBAAiB,EAAEpH,QAAQ,KAAK;EAC7CtJ,qDAAS,CAAC,MAAM;IACZ,MAAM+F,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpCiL,+BAA+B,CAAClI,EAAE,EAAEmI,eAAe,EAAElL,SAAS,CAAC;IAC/D,MAAMG,KAAK,GAAG4C,EAAE,CAAC,CAAC,CAAC;IACnB,MAAMuI,YAAY,GAAGnL,KAAK,CAACoL,gBAAgB;IAC3C,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,KAAK,GAAGH,YAAY,CAACI,MAAM;IACjE,MAAMC,QAAQ,GAAGxL,KAAK,CAACyL,UAAU,CAACH,KAAK,GAAGtL,KAAK,CAACyL,UAAU,CAACF,MAAM;IACjE,MAAMG,YAAY,GAAGL,gBAAgB,KAAK,CAAC,IAAIG,QAAQ,KAAK,CAAC;IAC7D,MAAMG,QAAQ,GAAGtJ,IAAI,CAACuJ,GAAG,CAACT,YAAY,CAACU,IAAI,GAAG7L,KAAK,CAAC8L,kBAAkB,CAACD,IAAI,CAAC;IAC5E,MAAME,SAAS,GAAG1J,IAAI,CAACuJ,GAAG,CAACT,YAAY,CAACa,KAAK,GAAGhM,KAAK,CAAC8L,kBAAkB,CAACE,KAAK,CAAC;IAC/E,MAAMC,mBAAmB,GAAGZ,gBAAgB,GAAG,CAAC,KAAKM,QAAQ,IAAI,CAAC,IAAII,SAAS,IAAI,CAAC,CAAC;IACrF,IAAIL,YAAY,IAAIO,mBAAmB,EAAE;MACrC;IACJ;IACA,IAAIjM,KAAK,CAACgL,cAAc,EAAE;MACtBkB,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCmB,eAAe,CAAC1B,iBAAiB,CAAC;IACtC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM2B,oBAAoB,GAAIhB,YAAY,CAAC9G,CAAC,KAAK,CAAC,IAAI8G,YAAY,CAAClH,CAAC,KAAK,CAAC,IAAMkH,YAAY,CAACG,KAAK,KAAK,CAAC,IAAIH,YAAY,CAACI,MAAM,KAAK,CAAE;MACtI,IAAIY,oBAAoB,IAAItM,SAAS,GAAG,CAAC,EAAE;QACvCqM,eAAe,CAACnB,eAAe,CAAC;QAChCmB,eAAe,CAAC1B,iBAAiB,EAAE,KAAK,CAAC;QACzCG,2BAA2B,CAACI,eAAe,CAACjN,EAAE,CAAC;MACnD;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMoO,eAAe,GAAGA,CAACE,WAAW,EAAEC,MAAM,GAAG,IAAI,KAAK;EACpD,MAAM1C,QAAQ,GAAGyC,WAAW,CAACtO,EAAE;EAC/B,IAAIuO,MAAM,EAAE;IACR1C,QAAQ,CAACL,SAAS,CAACgD,MAAM,CAAC,mCAAmC,CAAC;IAC9D3C,QAAQ,CAAC4C,eAAe,CAAC,aAAa,CAAC;EAC3C,CAAC,MACI;IACD5C,QAAQ,CAACL,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAC3DI,QAAQ,CAAC6C,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAChD;AACJ,CAAC;AACD,MAAM9B,gBAAgB,GAAGA,CAACd,QAAQ,GAAG,EAAE,EAAE/C,KAAK,GAAG,CAAC,EAAEjL,UAAU,GAAG,KAAK,KAAK;EACvEgO,QAAQ,CAAC6C,OAAO,CAAExC,OAAO,IAAK;IAC1B,MAAMyC,QAAQ,GAAGzC,OAAO,CAACC,UAAU;IACnC,MAAMyC,QAAQ,GAAG1C,OAAO,CAACI,YAAY;IACrC,IAAI,CAACqC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,OAAO,EAAE;MACxC;IACJ;IACAD,QAAQ,CAACvO,KAAK,CAACxC,UAAU,GAAGA,UAAU,GAAGmN,UAAU,GAAG,EAAE;IACxD4D,QAAQ,CAACvO,KAAK,CAACyO,SAAS,GAAI,WAAUhG,KAAM,KAAIA,KAAM,MAAK;EAC/D,CAAC,CAAC;AACN,CAAC;AACD,MAAMiG,gBAAgB,GAAGA,CAAC1J,QAAQ,EAAEoD,MAAM,EAAEuG,cAAc,KAAK;EAC3DrT,qDAAQ,CAAC,MAAM;IACX,MAAMmG,SAAS,GAAGuD,QAAQ,CAACvD,SAAS;IACpC,MAAMmN,YAAY,GAAGxG,MAAM,CAACrC,YAAY;IACxC,MAAMwC,SAAS,GAAGoG,cAAc,GAAGA,cAAc,CAAC5I,YAAY,GAAG,CAAC;IAClE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI4I,cAAc,KAAK,IAAI,IAAIlN,SAAS,GAAG8G,SAAS,EAAE;MAClDH,MAAM,CAACpI,KAAK,CAAC0I,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC;MAChD1D,QAAQ,CAAChF,KAAK,CAAC0I,WAAW,CAAC,WAAW,EAAG,SAAQkG,YAAa,iBAAgB,CAAC;MAC/E;IACJ;IACA,MAAMpG,eAAe,GAAG/G,SAAS,GAAG8G,SAAS;IAC7C,MAAMD,YAAY,GAAG,EAAE;IACvB,MAAMG,KAAK,GAAGlM,uDAAK,CAAC,CAAC,EAAEiM,eAAe,GAAGF,YAAY,EAAE,CAAC,CAAC;IACzD5M,qDAAS,CAAC,MAAM;MACZsJ,QAAQ,CAAChF,KAAK,CAACyM,cAAc,CAAC,WAAW,CAAC;MAC1CrE,MAAM,CAACpI,KAAK,CAAC0I,WAAW,CAAC,iBAAiB,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMkG,YAAY,GAAG,+lEAA+lE;AACpnE,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,sgBAAsgB;AAC1hB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBrR,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAqR,OAAA;IACjBrU,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACsR,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAApR,6OAAA,CAAG,WAAOsL,SAAS,EAAEoF,cAAc,EAAK;QACxD,MAAM3J,QAAQ,GAAIkK,OAAI,CAAClK,QAAQ,SAAS/H,qDAAgB,CAACsM,SAAS,CAAE;QACpE;AACZ;AACA;QACY2F,OAAI,CAACxF,qBAAqB,GAAG,MAAM;UAC/BgF,gBAAgB,CAACQ,OAAI,CAAClK,QAAQ,EAAEkK,OAAI,CAACxP,EAAE,EAAEiP,cAAc,CAAC;QAC5D,CAAC;QACD3J,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAE6L,OAAI,CAACxF,qBAAqB,CAAC;QAC/DgF,gBAAgB,CAACQ,OAAI,CAAClK,QAAQ,EAAEkK,OAAI,CAACxP,EAAE,EAAEiP,cAAc,CAAC;MAC5D,CAAC;MAAA,iBAAAW,GAAA,EAAAC,GAAA;QAAA,OAAAF,KAAA,CAAAzF,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACvJ,QAAQ,GAAGuB,SAAS;IACzB,IAAI,CAACkI,WAAW,GAAG,KAAK;EAC5B;EACAyF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,mBAAmB,GAAG3S,uDAAqB,CAAC,IAAI,CAACkD,EAAE,CAAC;EAC7D;EACA5B,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC2R,sBAAsB,CAAC,CAAC;EACjC;EACAzF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyF,sBAAsB,CAAC,CAAC;EACjC;EACAnM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACoM,wBAAwB,CAAC,CAAC;EACnC;EACMD,sBAAsBA,CAAA,EAAG;IAAA,IAAAE,OAAA;IAAA,OAAA1R,6OAAA;MAC3B,MAAMsB,IAAI,GAAGxD,4DAAU,CAAC4T,OAAI,CAAC;MAC7B,IAAIpQ,IAAI,KAAK,KAAK,EAAE;QAChB;MACJ;MACA,MAAM;QAAEe;MAAS,CAAC,GAAGqP,OAAI;MACzB,MAAMC,WAAW,GAAGtP,QAAQ,KAAK,UAAU;MAC3C,MAAM8I,OAAO,GAAG9I,QAAQ,KAAK,MAAM;MACnCqP,OAAI,CAACD,wBAAwB,CAAC,CAAC;MAC/B,IAAIE,WAAW,EAAE;QACb,MAAMtG,MAAM,GAAGqG,OAAI,CAACjQ,EAAE,CAACuD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAMsG,SAAS,GAAGD,MAAM,GAAGxM,qDAAc,CAACwM,MAAM,CAAC,GAAG,IAAI;QACxD;QACA5N,qDAAS,CAAC,MAAM;UACZ,MAAMmU,KAAK,GAAGjF,YAAY,CAAC,WAAW,CAAC;UACvCiF,KAAK,CAACrB,IAAI,GAAG,OAAO;UACpB5D,YAAY,CAAC,iBAAiB,CAAC;QACnC,CAAC,CAAC;QACF,MAAM+E,OAAI,CAACG,mBAAmB,CAACvG,SAAS,EAAED,MAAM,CAAC;MACrD,CAAC,MACI,IAAIF,OAAO,EAAE;QACd,MAAME,MAAM,GAAGqG,OAAI,CAACjQ,EAAE,CAACuD,OAAO,CAAC,uCAAuC,CAAC;QACvE,MAAMsG,SAAS,GAAGD,MAAM,GAAGxM,qDAAc,CAACwM,MAAM,CAAC,GAAG,IAAI;QACxD,IAAI,CAACC,SAAS,EAAE;UACZxM,qDAAuB,CAAC4S,OAAI,CAACjQ,EAAE,CAAC;UAChC;QACJ;QACA,MAAMiP,cAAc,GAAGpF,SAAS,CAACkB,aAAa,CAAC,iCAAiC,CAAC;QACjF,MAAMkF,OAAI,CAACP,eAAe,CAAC7F,SAAS,EAAEoF,cAAc,CAAC;MACzD;IAAC;EACL;EACAe,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACK,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACC,UAAU,CAAC,CAAC;MACtC,IAAI,CAACD,oBAAoB,GAAGlO,SAAS;IACzC;IACA,IAAI,IAAI,CAACmD,QAAQ,IAAI,IAAI,CAAC0E,qBAAqB,EAAE;MAC7C,IAAI,CAAC1E,QAAQ,CAACxB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACkG,qBAAqB,CAAC;MACvE,IAAI,CAACA,qBAAqB,GAAG7H,SAAS;IAC1C;IACA,IAAI,IAAI,CAACoO,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC/E,SAAS,CAACgD,MAAM,CAAC,sBAAsB,CAAC;MACnE,IAAI,CAAC+B,qBAAqB,GAAGpO,SAAS;IAC1C;EACJ;EACMiO,mBAAmBA,CAACvG,SAAS,EAAED,MAAM,EAAE;IAAA,IAAA4G,OAAA;IAAA,OAAAjS,6OAAA;MACzC,IAAI,CAACsL,SAAS,IAAI,CAACD,MAAM,EAAE;QACvBvM,qDAAuB,CAACmT,OAAI,CAACxQ,EAAE,CAAC;QAChC;MACJ;MACA,IAAI,OAAOyQ,oBAAoB,KAAK,WAAW,EAAE;QAC7C;MACJ;MACAD,OAAI,CAAClL,QAAQ,SAAS/H,qDAAgB,CAACsM,SAAS,CAAC;MACjD,MAAM6G,OAAO,GAAG9G,MAAM,CAACmC,gBAAgB,CAAC,YAAY,CAAC;MACrDyE,OAAI,CAACD,qBAAqB,GAAGvE,KAAK,CAACC,IAAI,CAACyE,OAAO,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAAChQ,QAAQ,KAAK,UAAU,CAAC;MACjG,IAAI,CAAC4P,OAAI,CAACD,qBAAqB,EAAE;QAC7B;MACJ;MACA,MAAMtD,eAAe,GAAGrB,iBAAiB,CAAC4E,OAAI,CAACD,qBAAqB,CAAC;MACrE,MAAM7D,iBAAiB,GAAGd,iBAAiB,CAAC4E,OAAI,CAACxQ,EAAE,CAAC;MACpD,IAAI,CAACiN,eAAe,IAAI,CAACP,iBAAiB,EAAE;QACxC;MACJ;MACA0B,eAAe,CAACnB,eAAe,EAAE,KAAK,CAAC;MACvCJ,2BAA2B,CAACI,eAAe,CAACjN,EAAE,EAAE,CAAC,CAAC;MAClD;AACR;AACA;AACA;AACA;AACA;MACQ,MAAM6Q,mBAAmB,GAAI/L,EAAE,IAAK;QAChCsI,yBAAyB,CAACtI,EAAE,EAAEmI,eAAe,EAAEP,iBAAiB,EAAE8D,OAAI,CAAClL,QAAQ,CAAC;MACpF,CAAC;MACDkL,OAAI,CAACH,oBAAoB,GAAG,IAAII,oBAAoB,CAACI,mBAAmB,EAAE;QACtEC,IAAI,EAAEjH,SAAS;QACfkH,SAAS,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MAC1D,CAAC,CAAC;MACFP,OAAI,CAACH,oBAAoB,CAACW,OAAO,CAACtE,iBAAiB,CAACZ,QAAQ,CAACY,iBAAiB,CAACZ,QAAQ,CAACmF,MAAM,GAAG,CAAC,CAAC,CAACjR,EAAE,CAAC;MACvG;AACR;AACA;AACA;AACA;MACQwQ,OAAI,CAACxG,qBAAqB,GAAG,MAAM;QAC/ByC,mBAAmB,CAAC+D,OAAI,CAAClL,QAAQ,EAAEoH,iBAAiB,EAAE7C,SAAS,CAAC;MACpE,CAAC;MACD2G,OAAI,CAAClL,QAAQ,CAAC3B,gBAAgB,CAAC,QAAQ,EAAE6M,OAAI,CAACxG,qBAAqB,CAAC;MACpEhO,qDAAS,CAAC,MAAM;QACZ,IAAIwU,OAAI,CAACD,qBAAqB,KAAKpO,SAAS,EAAE;UAC1CqO,OAAI,CAACD,qBAAqB,CAAC/E,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACpE;MACJ,CAAC,CAAC;IAAC;EACP;EACA7L,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEyK,WAAW;MAAEoF;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAM5P,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuE,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,MAAM;IACxC;IACA,MAAMsQ,QAAQ,GAAG/T,qDAAW,CAAC,UAAU,EAAE,IAAI,CAAC6C,EAAE,CAAC,GAAG,MAAM,GAAG,QAAQ;IACrE,OAAQ5E,qDAAC,CAACE,iDAAI,EAAE6V,MAAM,CAACC,MAAM,CAAC;MAAEtR,GAAG,EAAE,0CAA0C;MAAE0H,IAAI,EAAE0J,QAAQ;MAAEnR,KAAK,EAAE;QAChG,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAE,UAASA,IAAK,EAAC,GAAG,IAAI;QACxB,CAAE,oBAAmB,GAAG,IAAI,CAACwK,WAAW;QACxC,CAAE,mBAAkBzJ,QAAS,EAAC,GAAG,IAAI;QACrC,CAAE,sBAAqBf,IAAK,EAAC,GAAG,IAAI,CAACwK;MACzC;IAAE,CAAC,EAAEoF,mBAAmB,CAAC,EAAE5P,IAAI,KAAK,KAAK,IAAIwK,WAAW,IAAIjP,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC9N;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD+T,MAAM,CAACjP,KAAK,GAAG;EACXO,GAAG,EAAEuO,kBAAkB;EACvBtO,EAAE,EAAEwO;AACR,CAAC;AAED,MAAM+B,eAAe,GAAG,4FAA4F;AACpH,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBrT,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACqT,cAAc,GAAG9V,qDAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC+V,gBAAgB,GAAG/V,qDAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACgW,eAAe,GAAGhW,qDAAW,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACiW,cAAc,GAAG/T,gEAAoB,CAAC,CAAC;IAC5C,IAAI,CAACgU,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAAC/R,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACwV,QAAQ,GAAG1P,SAAS;IACzB,IAAI,CAAC2P,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG5P,SAAS;IAC1B,IAAI,CAAC6P,YAAY,GAAG7P,SAAS;EACjC;EACA8P,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACH,YAAY,KAAK7P,SAAS,CAAC;IACxD;EACJ;EACMmB,iBAAiBA,CAAA,EAAG;IAAA,IAAA8O,OAAA;IAAA,OAAA7T,6OAAA;MACtB,MAAM8T,OAAO,GAAGA,CAAA,KAAM;QAClBD,OAAI,CAACR,4BAA4B,GAAG,IAAI;QACxC,IAAIQ,OAAI,CAACJ,YAAY,EAAE;UACnBI,OAAI,CAACJ,YAAY,CAACK,OAAO,CAAC,CAAC;QAC/B;MACJ,CAAC;MACDD,OAAI,CAACF,OAAO,GAAG,OAAO,0IAAkC,EAAEI,sBAAsB,CAACF,OAAI,CAACpS,EAAE,EAAE,MAAM,CAACoS,OAAI,CAACR,4BAA4B,IAAI,CAAC,CAACQ,OAAI,CAACJ,YAAY,IAAII,OAAI,CAACJ,YAAY,CAACO,QAAQ,CAAC,CAAC,EAAE,MAAMF,OAAO,CAAC,CAAC,EAAGxL,IAAI,IAAK;QAAE,IAAIkB,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGqK,OAAI,CAACI,GAAG,MAAM,IAAI,IAAIzK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0K,YAAY,CAAC5L,IAAI,CAAC;MAAE,CAAC,EAAE,CAAC6L,cAAc,EAAE7L,IAAI,EAAE8L,GAAG,KAAK;QACjV,IAAIP,OAAI,CAACI,GAAG,EAAE;UACVJ,OAAI,CAACI,GAAG,CAACI,QAAQ,CAAC,MAAM;YACpBR,OAAI,CAACR,4BAA4B,GAAG,KAAK;YACzC,IAAIQ,OAAI,CAACJ,YAAY,EAAE;cACnBI,OAAI,CAACJ,YAAY,CAACa,KAAK,CAACH,cAAc,CAAC;YAC3C;UACJ,CAAC,EAAE;YAAEI,eAAe,EAAE;UAAK,CAAC,CAAC;UAC7B;UACA,IAAIC,YAAY,GAAGL,cAAc,GAAG,CAAC,KAAK,GAAG,KAAK;UAClD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;UACgB,IAAI,CAACA,cAAc,EAAE;YACjBN,OAAI,CAACI,GAAG,CAACQ,MAAM,CAAC,gCAAgC,CAAC;YACjDD,YAAY,IAAItV,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,MACI;YACDkM,YAAY,IAAItV,6DAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEoJ,IAAI,CAAC,CAAC,CAAC,CAAC;UAC1F;UACAuL,OAAI,CAACI,GAAG,CAACS,WAAW,CAACP,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEK,YAAY,EAAEJ,GAAG,CAAC;QACnE,CAAC,MACI;UACDP,OAAI,CAACR,4BAA4B,GAAG,KAAK;QAC7C;MACJ,CAAC,CAAC;MACFQ,OAAI,CAACH,mBAAmB,CAAC,CAAC;IAAC;EAC/B;EACAnC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC0B,cAAc,CAACjM,IAAI,CAAC,CAAC;EAC9B;EACA3B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACsO,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACrH,OAAO,CAAC,CAAC;MACtB,IAAI,CAACqH,OAAO,GAAG/P,SAAS;IAC5B;EACJ;EACA;EACM+Q,MAAMA,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAA/U,6OAAA;MACtC,MAAMgV,MAAM,SAASD,OAAI,CAAC3B,cAAc,CAAC6B,IAAI,CAAC,CAAC;MAC/C,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAI;QACAA,OAAO,SAASH,OAAI,CAACxV,UAAU,CAACqV,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAChE,CAAC,CACD,OAAO1X,CAAC,EAAE;QACN+X,OAAO,CAACC,KAAK,CAAChY,CAAC,CAAC;MACpB;MACA4X,MAAM,CAAC,CAAC;MACR,OAAOE,OAAO;IAAC;EACnB;EACA;EACMG,UAAUA,CAACjM,EAAE,EAAEkM,MAAM,EAAEC,SAAS,EAAE/B,SAAS,EAAE;IAAA,IAAAgC,OAAA;IAAA,OAAAxV,6OAAA;MAC/C,MAAMkV,OAAO,SAASM,OAAI,CAACC,OAAO,CAACrM,EAAE,EAAEkM,MAAM,EAAE;QAC3C9N,QAAQ,EAAE+N,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG3R,SAAS;QAC9C2R,SAAS,EAAEA,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS;QACpDG,gBAAgB,EAAElC;MACtB,CAAC,CAAC;MACF,OAAO;QACH0B,OAAO;QACPS,OAAO,EAAEH,OAAI,CAACI;MAClB,CAAC;IAAC;EACN;EACA;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAA9V,6OAAA;MACf,MAAMgQ,MAAM,GAAG8F,OAAI,CAACF,QAAQ;MAC5B,OAAO5F,MAAM,GACP;QACE5G,EAAE,EAAE4G,MAAM,CAACpD,OAAO;QAClB+I,OAAO,EAAE3F,MAAM;QACfsF,MAAM,EAAEQ,OAAI,CAACC;MACjB,CAAC,GACCnS,SAAS;IAAC;EACpB;EACM6R,OAAOA,CAACO,SAAS,EAAEV,MAAM,EAAER,IAAI,EAAE;IAAA,IAAAmB,OAAA;IAAA,OAAAjW,6OAAA;MACnC,IAAIiW,OAAI,CAACC,eAAe,KAAKF,SAAS,IAAIvX,uDAAqB,CAAC6W,MAAM,EAAEW,OAAI,CAACF,YAAY,CAAC,EAAE;QACxF,OAAO,KAAK;MAChB;MACA;MACA,MAAMlB,SAAS,GAAGoB,OAAI,CAACL,QAAQ;MAC/B,MAAMhB,UAAU,SAASzV,mEAAe,CAAC8W,OAAI,CAAC3C,QAAQ,EAAE2C,OAAI,CAACxU,EAAE,EAAEuU,SAAS,EAAE,CAAC,UAAU,EAAE,oBAAoB,CAAC,EAAEV,MAAM,CAAC;MACvHW,OAAI,CAACC,eAAe,GAAGF,SAAS;MAChCC,OAAI,CAACL,QAAQ,GAAGhB,UAAU;MAC1BqB,OAAI,CAACF,YAAY,GAAGT,MAAM;MAC1B;MACA,MAAMW,OAAI,CAACtB,MAAM,CAACC,UAAU,EAAEC,SAAS,EAAEC,IAAI,CAAC;MAC9C,MAAM1V,mEAAe,CAAC6W,OAAI,CAAC3C,QAAQ,EAAEuB,SAAS,CAAC;MAC/C,OAAO,IAAI;IAAC;EAChB;EACMtV,UAAUA,CAACqV,UAAU,EAAEC,SAAS,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAqB,OAAA;IAAA,OAAAnW,6OAAA;MAC/C,IAAI6U,SAAS,KAAKD,UAAU,EAAE;QAC1B,OAAO,KAAK;MAChB;MACA;MACAuB,OAAI,CAACjD,gBAAgB,CAAClM,IAAI,CAAC,CAAC;MAC5B,MAAM;QAAEvF,EAAE;QAAEH;MAAK,CAAC,GAAG6U,OAAI;MACzB,MAAM5C,QAAQ,GAAG4C,OAAI,CAAC5C,QAAQ,IAAIvV,wDAAM,CAACmC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;MACrE,MAAMuV,gBAAgB,GAAGZ,IAAI,CAACY,gBAAgB,IAAIS,OAAI,CAAC3C,SAAS,IAAIxV,wDAAM,CAACoY,GAAG,CAAC,cAAc,CAAC;MAC9F,MAAM7W,sDAAU,CAACqT,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEvR,IAAI;QAC/CiS,QAAQ;QACRqB,UAAU;QACVC,SAAS;QAAE1K,MAAM,EAAE1I,EAAE;QACrB;AACZ;AACA;AACA;AACA;QACY4U,QAAQ,EAAEjY,uDAAY,CAACqD,EAAE,CAAC;QAAE6U,gBAAgB,EAAExB,IAAI,CAACyB,iBAAiB,GAC7DtC,GAAG,IAAK;UACP;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIA,GAAG,KAAKrQ,SAAS,IAAI,CAACuS,OAAI,CAAC9C,4BAA4B,EAAE;YACzD8C,OAAI,CAAC9C,4BAA4B,GAAG,IAAI;YACxCY,GAAG,CAACI,QAAQ,CAAC,MAAM;cACf8B,OAAI,CAAC9C,4BAA4B,GAAG,KAAK;cACzC,IAAI8C,OAAI,CAAC1C,YAAY,EAAE;gBACnB0C,OAAI,CAAC1C,YAAY,CAACa,KAAK,CAAC,KAAK,CAAC;cAClC;YACJ,CAAC,EAAE;cAAEC,eAAe,EAAE;YAAK,CAAC,CAAC;YAC7B;AACxB;AACA;AACA;AACA;AACA;YACwBN,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B,CAAC,MACI;YACDyB,OAAI,CAAClC,GAAG,GAAGA,GAAG;UAClB;QACJ,CAAC,GACCrQ;MAAU,CAAC,EAAEkR,IAAI,CAAC,EAAE;QAAEY;MAAiB,CAAC,CAAC,CAAC;MACpD;MACAS,OAAI,CAAChD,eAAe,CAACnM,IAAI,CAAC,CAAC;MAC3B,OAAO,IAAI;IAAC;EAChB;EACA3F,MAAMA,CAAA,EAAG;IACL,OAAOxE,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC;EACzE;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuZ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACDxD,YAAY,CAACjR,KAAK,GAAGgR,qBAAqB;AAE1C,MAAM0D,WAAW,GAAG,ujEAAujE;AAC3kE,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,grBAAgrB;AACnsB,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,YAAY,GAAG,MAAM;EACvBlX,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACkX,QAAQ,GAAG3Z,qDAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACqH,KAAK,GAAGZ,SAAS;IACtB,IAAI,CAAC2M,IAAI,GAAG3M,SAAS;EACzB;EACAmT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAjS,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACiS,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAMzG,IAAI,GAAG,IAAI,CAAC0G,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACH,QAAQ,CAAC9P,IAAI,CAAC;MACf,CAAE,SAAQuJ,IAAK,EAAC,GAAG;IACvB,CAAC,CAAC;EACN;EACA0G,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC1G,IAAI,KAAK3M,SAAS,GAAG,IAAI,CAAC2M,IAAI,GAAG,SAAS;EAC1D;EACAlP,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyS,IAAI,GAAG,IAAI,CAAC0G,OAAO,CAAC,CAAC;IAC3B,OAAQpa,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE7C,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QACjG,CAAClD,IAAI,GAAG,IAAI;QACZ,CAAE,SAAQiP,IAAK,EAAC,GAAG,IAAI;QACvB,WAAW,EAAEzD,QAAQ,CAACoK,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,EAAEra,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAgB,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACpK;EACA,IAAIE,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWuZ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa;IAC1B,CAAC;EAAE;AACP,CAAC;AACDK,YAAY,CAAC9U,KAAK,GAAG;EACjBO,GAAG,EAAEoU,iBAAiB;EACtBnU,EAAE,EAAEqU;AACR,CAAC;AAED,MAAMO,aAAa,GAAG,0xFAA0xF;AAChzF,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,YAAY,GAAG,u0EAAu0E;AAC51E,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,OAAO,GAAG,MAAM;EAClB5X,WAAWA,CAACC,OAAO,EAAE;IACjBhD,qDAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAAC4X,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACjT,KAAK,GAAGZ,SAAS;EAC1B;EACA2N,iBAAiBA,CAAA,EAAG;IAChB,MAAMmG,OAAO,GAAGjK,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjM,EAAE,CAAC+L,gBAAgB,CAAC,aAAa,CAAC,CAAC;IACnE,MAAMmK,YAAY,GAAGD,OAAO,CAACtF,IAAI,CAAEwF,MAAM,IAAK;MAC1C,OAAOA,MAAM,CAACnL,IAAI,KAAK,OAAO;IAClC,CAAC,CAAC;IACF,IAAIkL,YAAY,EAAE;MACdA,YAAY,CAAC1K,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACpD;IACA,MAAM2K,eAAe,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC;IACzC,MAAMC,WAAW,GAAGF,eAAe,CAACzF,IAAI,CAAEwF,MAAM,IAAKA,MAAM,CAACnL,IAAI,KAAK,KAAK,CAAC,IACvEoL,eAAe,CAACzF,IAAI,CAAEwF,MAAM,IAAKA,MAAM,CAACnL,IAAI,KAAK,SAAS,CAAC,IAC3DoL,eAAe,CAACzF,IAAI,CAAEwF,MAAM,IAAKA,MAAM,CAACnL,IAAI,KAAK,WAAW,CAAC;IACjE,IAAIsL,WAAW,EAAE;MACbA,WAAW,CAAC9K,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClD;EACJ;EACA8K,aAAaA,CAACzR,EAAE,EAAE;IACdA,EAAE,CAAC0R,eAAe,CAAC,CAAC;IACpB,MAAMrL,OAAO,GAAGrG,EAAE,CAAC2R,MAAM,CAACtL,OAAO;IACjC,MAAMuL,aAAa,GAAG5R,EAAE,CAAChD,MAAM;IAC/B,MAAM6U,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAACb,cAAc,CAACpB,GAAG,CAACxJ,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI0L,cAAc,GAAG,KAAK;IAC1B1F,MAAM,CAAC2F,IAAI,CAACJ,aAAa,CAAC,CAAC/H,OAAO,CAAE7O,GAAG,IAAK;MACxC,MAAMiX,QAAQ,GAAI,WAAUjX,GAAI,EAAC;MACjC,MAAMkX,QAAQ,GAAGN,aAAa,CAAC5W,GAAG,CAAC;MACnC,IAAIkX,QAAQ,KAAKJ,WAAW,CAACG,QAAQ,CAAC,EAAE;QACpCF,cAAc,GAAG,IAAI;MACzB;MACA,IAAIG,QAAQ,EAAE;QACVL,SAAS,CAACI,QAAQ,CAAC,GAAG,IAAI;MAC9B;IACJ,CAAC,CAAC;IACF,IAAIF,cAAc,EAAE;MAChB,IAAI,CAACd,cAAc,CAACkB,GAAG,CAAC9L,OAAO,EAAEwL,SAAS,CAAC;MAC3C7a,qDAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACA8D,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxD,4DAAU,CAAC,IAAI,CAAC;IAC7B,MAAMua,WAAW,GAAG,CAAC,CAAC;IACtB,IAAI,CAACb,cAAc,CAACpH,OAAO,CAAEuI,KAAK,IAAK;MACnC/F,MAAM,CAACC,MAAM,CAACwF,WAAW,EAAEM,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,OAAQ9b,qDAAC,CAACE,iDAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEoR,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwF,WAAW,CAAC,EAAE1Z,qDAAkB,CAAC,IAAI,CAAC6F,KAAK,EAAE;QAC/I,CAAClD,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE1C,qDAAW,CAAC,aAAa,EAAE,IAAI,CAAC6C,EAAE;MACpD,CAAC,CAAC;IAAE,CAAC,EAAE5E,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAqB,CAAC,CAAC,EAAE3E,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAoB,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE+H,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEzM,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE+H,IAAI,EAAE;IAAY,CAAC,CAAC,EAAEzM,qDAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAkB,CAAC,EAAE3E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE1E,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE+H,IAAI,EAAE;IAAU,CAAC,CAAC,EAAEzM,qDAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE+H,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EAC9pB;EACA,IAAI7H,EAAEA,CAAA,EAAG;IAAE,OAAOxE,qDAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDsa,OAAO,CAACxV,KAAK,GAAG;EACZO,GAAG,EAAE8U,mBAAmB;EACxB7U,EAAE,EAAE+U;AACR,CAAC", "sources": ["./node_modules/@ionic/core/dist/esm/ion-app_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement, d as createEvent, e as readTask, j as forceUpdate, w as writeTask } from './index-c71c5417.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-7f93f261.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { b as getIonMode, c as config, a as isPlatform } from './ionic-global-b9c0d1da.js';\nimport { k as hasLazyBuild, c as componentOnReady, j as clamp, i as inheritAriaAttributes, s as shallowEqualStringMap } from './helpers-da915de8.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-5cc724f3.js';\nimport { c as createKeyboardController } from './keyboard-controller-ec5c2bfa.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-63d1a679.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { t as transition } from './index-92f14156.js';\nimport './index-a5d50daf.js';\nimport './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\n\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\nconst IonAppStyle0 = appCss;\n\nconst App = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    componentDidLoad() {\n        {\n            rIC(async () => {\n                const isHybrid = isPlatform(window, 'hybrid');\n                if (!config.getBoolean('_testing')) {\n                    import('./index-79b30591.js').then((module) => module.startTapClick(config));\n                }\n                if (config.getBoolean('statusTap', isHybrid)) {\n                    import('./status-tap-53150009.js').then((module) => module.startStatusTap());\n                }\n                if (config.getBoolean('inputShims', needInputShims())) {\n                    /**\n                     * needInputShims() ensures that only iOS and Android\n                     * platforms proceed into this block.\n                     */\n                    const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n                    import('./input-shims-0314bbe5.js').then((module) => module.startInputShims(config, platform));\n                }\n                const hardwareBackButtonModule = await import('./hardware-back-button-7f93f261.js');\n                const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n                if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n                    hardwareBackButtonModule.startHardwareBackButton();\n                }\n                else {\n                    /**\n                     * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n                     * then the close watcher will not be used.\n                     */\n                    if (shouldUseCloseWatcher()) {\n                        printIonWarning('experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n                    }\n                    hardwareBackButtonModule.blockHardwareBackButton();\n                }\n                if (typeof window !== 'undefined') {\n                    import('./keyboard-52278bd7.js').then((module) => module.startKeyboardAssist(window));\n                }\n                import('./focus-visible-dd40d69f.js').then((module) => (this.focusVisible = module.startFocusVisible()));\n            });\n        }\n    }\n    /**\n     * @internal\n     * Used to set focus on an element that uses `ion-focusable`.\n     * Do not use this if focusing the element as a result of a keyboard\n     * event as the focus utility should handle this for us. This method\n     * should be used when we want to programmatically focus an element as\n     * a result of another user action. (Ex: We focus the first element\n     * inside of a popover when the user presents it, but the popover is not always\n     * presented as a result of keyboard action.)\n     */\n    async setFocus(elements) {\n        if (this.focusVisible) {\n            this.focusVisible.setFocus(elements);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'a562850f242d9d45573e35efdd4bd178254677ef', class: {\n                [mode]: true,\n                'ion-page': true,\n                'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding'),\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nconst needInputShims = () => {\n    /**\n     * iOS always needs input shims\n     */\n    const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n    if (needsShimsIOS) {\n        return true;\n    }\n    /**\n     * Android only needs input shims when running\n     * in the browser and only if the browser is using the\n     * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n     */\n    const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n    if (isAndroidMobileWeb) {\n        return true;\n    }\n    return false;\n};\nconst rIC = (callback) => {\n    if ('requestIdleCallback' in window) {\n        window.requestIdleCallback(callback);\n    }\n    else {\n        setTimeout(callback, 32);\n    }\n};\nApp.style = IonAppStyle0;\n\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\nconst IonButtonsIosStyle0 = buttonsIosCss;\n\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\nconst IonButtonsMdStyle0 = buttonsMdCss;\n\nconst Buttons = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.collapse = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '4e5ce9abb3f56e8c91e3eb58c2819300e61eba24', class: {\n                [mode]: true,\n                ['buttons-collapse']: this.collapse,\n            } }, h(\"slot\", { key: 'f2f360ec888b6e946b512fed07431fab47c61482' })));\n    }\n};\nButtons.style = {\n    ios: IonButtonsIosStyle0,\n    md: IonButtonsMdStyle0\n};\n\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\nconst IonContentStyle0 = contentCss;\n\nconst Content = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n        this.ionScroll = createEvent(this, \"ionScroll\", 7);\n        this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n        this.watchDog = null;\n        this.isScrolling = false;\n        this.lastScroll = 0;\n        this.queued = false;\n        this.cTop = -1;\n        this.cBottom = -1;\n        this.isMainContent = true;\n        this.resizeTimeout = null;\n        this.tabsElement = null;\n        // Detail is used in a hot loop in the scroll event, by allocating it here\n        // V8 will be able to inline any read/write to it since it's a monomorphic class.\n        // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n        this.detail = {\n            scrollTop: 0,\n            scrollLeft: 0,\n            type: 'scroll',\n            event: undefined,\n            startX: 0,\n            startY: 0,\n            startTime: 0,\n            currentX: 0,\n            currentY: 0,\n            velocityX: 0,\n            velocityY: 0,\n            deltaX: 0,\n            deltaY: 0,\n            currentTime: 0,\n            data: undefined,\n            isScrolling: true,\n        };\n        this.color = undefined;\n        this.fullscreen = false;\n        this.fixedSlotPlacement = 'after';\n        this.forceOverscroll = undefined;\n        this.scrollX = false;\n        this.scrollY = true;\n        this.scrollEvents = false;\n    }\n    connectedCallback() {\n        this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n        /**\n         * The fullscreen content offsets need to be\n         * computed after the tab bar has loaded. Since\n         * lazy evaluation means components are not hydrated\n         * at the same time, we need to wait for the ionTabBarLoaded\n         * event to fire. This does not impact dist-custom-elements\n         * because there is no hydration there.\n         */\n        if (hasLazyBuild(this.el)) {\n            /**\n             * We need to cache the reference to the tabs.\n             * If just the content is unmounted then we won't\n             * be able to query for the closest tabs on disconnectedCallback\n             * since the content has been removed from the DOM tree.\n             */\n            const closestTabs = (this.tabsElement = this.el.closest('ion-tabs'));\n            if (closestTabs !== null) {\n                /**\n                 * When adding and removing the event listener\n                 * we need to make sure we pass the same function reference\n                 * otherwise the event listener will not be removed properly.\n                 * We can't only pass `this.resize` because \"this\" in the function\n                 * context becomes a reference to IonTabs instead of IonContent.\n                 *\n                 * Additionally, we listen for ionTabBarLoaded on the IonTabs\n                 * instance rather than the IonTabBar instance. It's possible for\n                 * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n                 * bubbles, we can catch any instances of child tab bars loading by listening\n                 * on IonTabs.\n                 */\n                this.tabsLoadCallback = () => this.resize();\n                closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n            }\n        }\n    }\n    disconnectedCallback() {\n        this.onScrollEnd();\n        if (hasLazyBuild(this.el)) {\n            /**\n             * The event listener and tabs caches need to\n             * be cleared otherwise this will create a memory\n             * leak where the IonTabs instance can never be\n             * garbage collected.\n             */\n            const { tabsElement, tabsLoadCallback } = this;\n            if (tabsElement !== null && tabsLoadCallback !== undefined) {\n                tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n            }\n            this.tabsElement = null;\n            this.tabsLoadCallback = undefined;\n        }\n    }\n    /**\n     * Rotating certain devices can update\n     * the safe area insets. As a result,\n     * the fullscreen feature on ion-content\n     * needs to be recalculated.\n     *\n     * We listen for \"resize\" because we\n     * do not care what the orientation of\n     * the device is. Other APIs\n     * such as ScreenOrientation or\n     * the deviceorientation event must have\n     * permission from the user first whereas\n     * the \"resize\" event does not.\n     *\n     * We also throttle the callback to minimize\n     * thrashing when quickly resizing a window.\n     */\n    onResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            this.resizeTimeout = null;\n        }\n        this.resizeTimeout = setTimeout(() => {\n            /**\n             * Resize should only happen\n             * if the content is visible.\n             * When the content is hidden\n             * then offsetParent will be null.\n             */\n            if (this.el.offsetParent === null) {\n                return;\n            }\n            this.resize();\n        }, 100);\n    }\n    shouldForceOverscroll() {\n        const { forceOverscroll } = this;\n        const mode = getIonMode(this);\n        return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n    }\n    resize() {\n        /**\n         * Only force update if the component is rendered in a browser context.\n         * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n         * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n         * `forceUpdate` will trigger another render, locking up the server.\n         *\n         * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n         */\n        {\n            if (this.fullscreen) {\n                readTask(() => this.readDimensions());\n            }\n            else if (this.cTop !== 0 || this.cBottom !== 0) {\n                this.cTop = this.cBottom = 0;\n                forceUpdate(this);\n            }\n        }\n    }\n    readDimensions() {\n        const page = getPageElement(this.el);\n        const top = Math.max(this.el.offsetTop, 0);\n        const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n        const dirty = top !== this.cTop || bottom !== this.cBottom;\n        if (dirty) {\n            this.cTop = top;\n            this.cBottom = bottom;\n            forceUpdate(this);\n        }\n    }\n    onScroll(ev) {\n        const timeStamp = Date.now();\n        const shouldStart = !this.isScrolling;\n        this.lastScroll = timeStamp;\n        if (shouldStart) {\n            this.onScrollStart();\n        }\n        if (!this.queued && this.scrollEvents) {\n            this.queued = true;\n            readTask((ts) => {\n                this.queued = false;\n                this.detail.event = ev;\n                updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n                this.ionScroll.emit(this.detail);\n            });\n        }\n    }\n    /**\n     * Get the element where the actual scrolling takes place.\n     * This element can be used to subscribe to `scroll` events or manually modify\n     * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n     *\n     * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n     * and `scrollToPoint()` to scroll the content into a certain point.\n     */\n    async getScrollElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.scrollEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.scrollEl);\n    }\n    /**\n     * Returns the background content element.\n     * @internal\n     */\n    async getBackgroundElement() {\n        if (!this.backgroundContentEl) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.backgroundContentEl);\n    }\n    /**\n     * Scroll to the top of the component.\n     *\n     * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n     */\n    scrollToTop(duration = 0) {\n        return this.scrollToPoint(undefined, 0, duration);\n    }\n    /**\n     * Scroll to the bottom of the component.\n     *\n     * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n     */\n    async scrollToBottom(duration = 0) {\n        const scrollEl = await this.getScrollElement();\n        const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n        return this.scrollToPoint(undefined, y, duration);\n    }\n    /**\n     * Scroll by a specified X/Y distance in the component.\n     *\n     * @param x The amount to scroll by on the horizontal axis.\n     * @param y The amount to scroll by on the vertical axis.\n     * @param duration The amount of time to take scrolling by that amount.\n     */\n    async scrollByPoint(x, y, duration) {\n        const scrollEl = await this.getScrollElement();\n        return this.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n    }\n    /**\n     * Scroll to a specified X/Y location in the component.\n     *\n     * @param x The point to scroll to on the horizontal axis.\n     * @param y The point to scroll to on the vertical axis.\n     * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n     */\n    async scrollToPoint(x, y, duration = 0) {\n        const el = await this.getScrollElement();\n        if (duration < 32) {\n            if (y != null) {\n                el.scrollTop = y;\n            }\n            if (x != null) {\n                el.scrollLeft = x;\n            }\n            return;\n        }\n        let resolve;\n        let startTime = 0;\n        const promise = new Promise((r) => (resolve = r));\n        const fromY = el.scrollTop;\n        const fromX = el.scrollLeft;\n        const deltaY = y != null ? y - fromY : 0;\n        const deltaX = x != null ? x - fromX : 0;\n        // scroll loop\n        const step = (timeStamp) => {\n            const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n            const easedT = Math.pow(linearTime, 3) + 1;\n            if (deltaY !== 0) {\n                el.scrollTop = Math.floor(easedT * deltaY + fromY);\n            }\n            if (deltaX !== 0) {\n                el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n            }\n            if (easedT < 1) {\n                // do not use DomController here\n                // must use nativeRaf in order to fire in the next frame\n                requestAnimationFrame(step);\n            }\n            else {\n                resolve();\n            }\n        };\n        // chill out for a frame first\n        requestAnimationFrame((ts) => {\n            startTime = ts;\n            step(ts);\n        });\n        return promise;\n    }\n    onScrollStart() {\n        this.isScrolling = true;\n        this.ionScrollStart.emit({\n            isScrolling: true,\n        });\n        if (this.watchDog) {\n            clearInterval(this.watchDog);\n        }\n        // watchdog\n        this.watchDog = setInterval(() => {\n            if (this.lastScroll < Date.now() - 120) {\n                this.onScrollEnd();\n            }\n        }, 100);\n    }\n    onScrollEnd() {\n        if (this.watchDog)\n            clearInterval(this.watchDog);\n        this.watchDog = null;\n        if (this.isScrolling) {\n            this.isScrolling = false;\n            this.ionScrollEnd.emit({\n                isScrolling: false,\n            });\n        }\n    }\n    render() {\n        const { fixedSlotPlacement, isMainContent, scrollX, scrollY, el } = this;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const mode = getIonMode(this);\n        const forceOverscroll = this.shouldForceOverscroll();\n        const transitionShadow = mode === 'ios';\n        this.resize();\n        return (h(Host, { key: 'f599a7da14969df38c20a6f650179d2a319fb58b', role: isMainContent ? 'main' : undefined, class: createColorClasses(this.color, {\n                [mode]: true,\n                'content-sizing': hostContext('ion-popover', this.el),\n                overscroll: forceOverscroll,\n                [`content-${rtl}`]: true,\n            }), style: {\n                '--offset-top': `${this.cTop}px`,\n                '--offset-bottom': `${this.cBottom}px`,\n            } }, h(\"div\", { key: '7c545c3c95ea05abd036d0462936ac153d2a8fc6', ref: (el) => (this.backgroundContentEl = el), id: \"background-content\", part: \"background\" }), fixedSlotPlacement === 'before' ? h(\"slot\", { name: \"fixed\" }) : null, h(\"div\", { key: 'a67ab7438466c99f74b7ba59964802c3ba86ac5b', class: {\n                'inner-scroll': true,\n                'scroll-x': scrollX,\n                'scroll-y': scrollY,\n                overscroll: (scrollX || scrollY) && forceOverscroll,\n            }, ref: (scrollEl) => (this.scrollEl = scrollEl), onScroll: this.scrollEvents ? (ev) => this.onScroll(ev) : undefined, part: \"scroll\" }, h(\"slot\", { key: '6d10fcac2e40b4dd6c20d91a8957ac07251d2fa7' })), transitionShadow ? (h(\"div\", { class: \"transition-effect\" }, h(\"div\", { class: \"transition-cover\" }), h(\"div\", { class: \"transition-shadow\" }))) : null, fixedSlotPlacement === 'after' ? h(\"slot\", { name: \"fixed\" }) : null));\n    }\n    get el() { return getElement(this); }\n};\nconst getParentElement = (el) => {\n    var _a;\n    if (el.parentElement) {\n        // normal element with a parent element\n        return el.parentElement;\n    }\n    if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n        // shadow dom's document fragment\n        return el.parentNode.host;\n    }\n    return null;\n};\nconst getPageElement = (el) => {\n    const tabs = el.closest('ion-tabs');\n    if (tabs) {\n        return tabs;\n    }\n    /**\n     * If we're in a popover, we need to use its wrapper so we can account for space\n     * between the popover and the edges of the screen. But if the popover contains\n     * its own page element, we should use that instead.\n     */\n    const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n    if (page) {\n        return page;\n    }\n    return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    const currentX = el.scrollLeft;\n    const currentY = el.scrollTop;\n    const timeDelta = timestamp - prevT;\n    if (shouldStart) {\n        // remember the start positions\n        detail.startTime = timestamp;\n        detail.startX = currentX;\n        detail.startY = currentY;\n        detail.velocityX = detail.velocityY = 0;\n    }\n    detail.currentTime = timestamp;\n    detail.currentX = detail.scrollLeft = currentX;\n    detail.currentY = detail.scrollTop = currentY;\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n};\nContent.style = IonContentStyle0;\n\nconst handleFooterFade = (scrollEl, baseEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n        /**\n         * Toolbar background will fade\n         * out over fadeDuration in pixels.\n         */\n        const fadeDuration = 10;\n        /**\n         * Begin fading out maxScroll - 30px\n         * from the bottom of the content.\n         * Also determine how close we are\n         * to starting the fade. If we are\n         * before the starting point, the\n         * scale value will get clamped to 0.\n         * If we are after the maxScroll (rubber\n         * band scrolling), the scale value will\n         * get clamped to 1.\n         */\n        const fadeStart = maxScroll - fadeDuration;\n        const distanceToStart = scrollTop - fadeStart;\n        const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\nconst IonFooterIosStyle0 = footerIosCss;\n\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonFooterMdStyle0 = footerMdCss;\n\nconst Footer = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.keyboardCtrl = null;\n        this.checkCollapsibleFooter = () => {\n            const mode = getIonMode(this);\n            if (mode !== 'ios') {\n                return;\n            }\n            const { collapse } = this;\n            const hasFade = collapse === 'fade';\n            this.destroyCollapsibleFooter();\n            if (hasFade) {\n                const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n                const contentEl = pageEl ? findIonContent(pageEl) : null;\n                if (!contentEl) {\n                    printIonContentErrorMsg(this.el);\n                    return;\n                }\n                this.setupFadeFooter(contentEl);\n            }\n        };\n        this.setupFadeFooter = async (contentEl) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleFooterFade(scrollEl, this.el);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleFooterFade(scrollEl, this.el);\n        };\n        this.keyboardVisible = false;\n        this.collapse = undefined;\n        this.translucent = false;\n    }\n    componentDidLoad() {\n        this.checkCollapsibleFooter();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleFooter();\n    }\n    async connectedCallback() {\n        this.keyboardCtrl = await createKeyboardController(async (keyboardOpen, waitForResize) => {\n            /**\n             * If the keyboard is hiding, then we need to wait\n             * for the webview to resize. Otherwise, the footer\n             * will flicker before the webview resizes.\n             */\n            if (keyboardOpen === false && waitForResize !== undefined) {\n                await waitForResize;\n            }\n            this.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n        });\n    }\n    disconnectedCallback() {\n        if (this.keyboardCtrl) {\n            this.keyboardCtrl.destroy();\n        }\n    }\n    destroyCollapsibleFooter() {\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n    }\n    render() {\n        const { translucent, collapse } = this;\n        const mode = getIonMode(this);\n        const tabs = this.el.closest('ion-tabs');\n        const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n        return (h(Host, { key: '5da19dc38ba73e1ddfd1bef3ebd485105d77c751', role: \"contentinfo\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`footer-${mode}`]: true,\n                [`footer-translucent`]: translucent,\n                [`footer-translucent-${mode}`]: translucent,\n                ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n                [`footer-collapse-${collapse}`]: collapse !== undefined,\n            } }, mode === 'ios' && translucent && h(\"div\", { key: 'fafad08090a33d8c4e8a5b63d61929dcb89aab47', class: \"footer-background\" }), h(\"slot\", { key: 'e0a443d346afa55e4317c0bc1263fdbe3c619559' })));\n    }\n    get el() { return getElement(this); }\n};\nFooter.style = {\n    ios: IonFooterIosStyle0,\n    md: IonFooterMdStyle0\n};\n\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = (tagName) => {\n    const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n    if (getCachedEl !== null) {\n        return getCachedEl;\n    }\n    const clonedEl = document.createElement(tagName);\n    clonedEl.classList.add('ion-cloned-element');\n    clonedEl.style.setProperty('display', 'none');\n    document.body.appendChild(clonedEl);\n    return clonedEl;\n};\nconst createHeaderIndex = (headerEl) => {\n    if (!headerEl) {\n        return;\n    }\n    const toolbars = headerEl.querySelectorAll('ion-toolbar');\n    return {\n        el: headerEl,\n        toolbars: Array.from(toolbars).map((toolbar) => {\n            const ionTitleEl = toolbar.querySelector('ion-title');\n            return {\n                el: toolbar,\n                background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n                ionTitleEl,\n                innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n                ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons')),\n            };\n        }),\n    };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n        // Native refresher should not cause titles to scale\n        const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n        if (nativeRefresher === null) {\n            writeTask(() => {\n                scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n            });\n        }\n    });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n    /**\n     * Fading in the backdrop opacity\n     * should happen after the large title\n     * has collapsed, so it is handled\n     * by handleHeaderFade()\n     */\n    if (headerEl.collapse === 'fade') {\n        return;\n    }\n    if (opacity === undefined) {\n        headerEl.style.removeProperty('--opacity-scale');\n    }\n    else {\n        headerEl.style.setProperty('--opacity-scale', opacity.toString());\n    }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n    if (!ev[0].isIntersecting) {\n        return;\n    }\n    /**\n     * There is a bug in Safari where overflow scrolling on a non-body element\n     * does not always reset the scrollTop position to 0 when letting go. It will\n     * set to 1 once the rubber band effect has ended. This causes the background to\n     * appear slightly on certain app setups.\n     *\n     * Additionally, we check if user is rubber banding (scrolling is negative)\n     * as this can mean they are using pull to refresh. Once the refresher starts,\n     * the content is transformed which can cause the intersection observer to erroneously\n     * fire here as well.\n     */\n    const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : ((1 - ev[0].intersectionRatio) * 100) / 75;\n    setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev, // TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n    writeTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n        const event = ev[0];\n        const intersection = event.intersectionRect;\n        const intersectionArea = intersection.width * intersection.height;\n        const rootArea = event.rootBounds.width * event.rootBounds.height;\n        const isPageHidden = intersectionArea === 0 && rootArea === 0;\n        const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n        const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n        const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n        if (isPageHidden || isPageTransitioning) {\n            return;\n        }\n        if (event.isIntersecting) {\n            setHeaderActive(mainHeaderIndex, false);\n            setHeaderActive(scrollHeaderIndex);\n        }\n        else {\n            /**\n             * There is a bug with IntersectionObserver on Safari\n             * where `event.isIntersecting === false` when cancelling\n             * a swipe to go back gesture. Checking the intersection\n             * x, y, width, and height provides a workaround. This bug\n             * does not happen when using Safari + Web Animations,\n             * only Safari + CSS Animations.\n             */\n            const hasValidIntersection = (intersection.x === 0 && intersection.y === 0) || (intersection.width !== 0 && intersection.height !== 0);\n            if (hasValidIntersection && scrollTop > 0) {\n                setHeaderActive(mainHeaderIndex);\n                setHeaderActive(scrollHeaderIndex, false);\n                setToolbarBackgroundOpacity(mainHeaderIndex.el);\n            }\n        }\n    });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n    const headerEl = headerIndex.el;\n    if (active) {\n        headerEl.classList.remove('header-collapse-condense-inactive');\n        headerEl.removeAttribute('aria-hidden');\n    }\n    else {\n        headerEl.classList.add('header-collapse-condense-inactive');\n        headerEl.setAttribute('aria-hidden', 'true');\n    }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n    toolbars.forEach((toolbar) => {\n        const ionTitle = toolbar.ionTitleEl;\n        const titleDiv = toolbar.innerTitleEl;\n        if (!ionTitle || ionTitle.size !== 'large') {\n            return;\n        }\n        titleDiv.style.transition = transition ? TRANSITION : '';\n        titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n    });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n    readTask(() => {\n        const scrollTop = scrollEl.scrollTop;\n        const baseElHeight = baseEl.clientHeight;\n        const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n        /**\n         * If we are using fade header with a condense\n         * header, then the toolbar backgrounds should\n         * not begin to fade in until the condense\n         * header has fully collapsed.\n         *\n         * Additionally, the main content should not\n         * overflow out of the container until the\n         * condense header has fully collapsed. When\n         * using just the condense header the content\n         * should overflow out of the container.\n         */\n        if (condenseHeader !== null && scrollTop < fadeStart) {\n            baseEl.style.setProperty('--opacity-scale', '0');\n            scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n            return;\n        }\n        const distanceToStart = scrollTop - fadeStart;\n        const fadeDuration = 10;\n        const scale = clamp(0, distanceToStart / fadeDuration, 1);\n        writeTask(() => {\n            scrollEl.style.removeProperty('clip-path');\n            baseEl.style.setProperty('--opacity-scale', scale.toString());\n        });\n    });\n};\n\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\nconst IonHeaderIosStyle0 = headerIosCss;\n\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonHeaderMdStyle0 = headerMdCss;\n\nconst Header = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.setupFadeHeader = async (contentEl, condenseHeader) => {\n            const scrollEl = (this.scrollEl = await getScrollElement(contentEl));\n            /**\n             * Handle fading of toolbars on scroll\n             */\n            this.contentScrollCallback = () => {\n                handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n            };\n            scrollEl.addEventListener('scroll', this.contentScrollCallback);\n            handleHeaderFade(this.scrollEl, this.el, condenseHeader);\n        };\n        this.collapse = undefined;\n        this.translucent = false;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.checkCollapsibleHeader();\n    }\n    componentDidUpdate() {\n        this.checkCollapsibleHeader();\n    }\n    disconnectedCallback() {\n        this.destroyCollapsibleHeader();\n    }\n    async checkCollapsibleHeader() {\n        const mode = getIonMode(this);\n        if (mode !== 'ios') {\n            return;\n        }\n        const { collapse } = this;\n        const hasCondense = collapse === 'condense';\n        const hasFade = collapse === 'fade';\n        this.destroyCollapsibleHeader();\n        if (hasCondense) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            // Cloned elements are always needed in iOS transition\n            writeTask(() => {\n                const title = cloneElement('ion-title');\n                title.size = 'large';\n                cloneElement('ion-back-button');\n            });\n            await this.setupCondenseHeader(contentEl, pageEl);\n        }\n        else if (hasFade) {\n            const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n            const contentEl = pageEl ? findIonContent(pageEl) : null;\n            if (!contentEl) {\n                printIonContentErrorMsg(this.el);\n                return;\n            }\n            const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n            await this.setupFadeHeader(contentEl, condenseHeader);\n        }\n    }\n    destroyCollapsibleHeader() {\n        if (this.intersectionObserver) {\n            this.intersectionObserver.disconnect();\n            this.intersectionObserver = undefined;\n        }\n        if (this.scrollEl && this.contentScrollCallback) {\n            this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n            this.contentScrollCallback = undefined;\n        }\n        if (this.collapsibleMainHeader) {\n            this.collapsibleMainHeader.classList.remove('header-collapse-main');\n            this.collapsibleMainHeader = undefined;\n        }\n    }\n    async setupCondenseHeader(contentEl, pageEl) {\n        if (!contentEl || !pageEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        if (typeof IntersectionObserver === 'undefined') {\n            return;\n        }\n        this.scrollEl = await getScrollElement(contentEl);\n        const headers = pageEl.querySelectorAll('ion-header');\n        this.collapsibleMainHeader = Array.from(headers).find((header) => header.collapse !== 'condense');\n        if (!this.collapsibleMainHeader) {\n            return;\n        }\n        const mainHeaderIndex = createHeaderIndex(this.collapsibleMainHeader);\n        const scrollHeaderIndex = createHeaderIndex(this.el);\n        if (!mainHeaderIndex || !scrollHeaderIndex) {\n            return;\n        }\n        setHeaderActive(mainHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n        /**\n         * Handle interaction between toolbar collapse and\n         * showing/hiding content in the primary ion-header\n         * as well as progressively showing/hiding the main header\n         * border as the top-most toolbar collapses or expands.\n         */\n        const toolbarIntersection = (ev) => {\n            handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, this.scrollEl);\n        };\n        this.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n            root: contentEl,\n            threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],\n        });\n        this.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n        /**\n         * Handle scaling of large iOS titles and\n         * showing/hiding border on last toolbar\n         * in primary header\n         */\n        this.contentScrollCallback = () => {\n            handleContentScroll(this.scrollEl, scrollHeaderIndex, contentEl);\n        };\n        this.scrollEl.addEventListener('scroll', this.contentScrollCallback);\n        writeTask(() => {\n            if (this.collapsibleMainHeader !== undefined) {\n                this.collapsibleMainHeader.classList.add('header-collapse-main');\n            }\n        });\n    }\n    render() {\n        const { translucent, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const collapse = this.collapse || 'none';\n        // banner role must be at top level, so remove role if inside a menu\n        const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n        return (h(Host, Object.assign({ key: 'c687314ef290793a9d633ad20cfc5eeb47621e31', role: roleType, class: {\n                [mode]: true,\n                // Used internally for styling\n                [`header-${mode}`]: true,\n                [`header-translucent`]: this.translucent,\n                [`header-collapse-${collapse}`]: true,\n                [`header-translucent-${mode}`]: this.translucent,\n            } }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", { key: 'b429996046082405a91e7c23f95516db0b736f12', class: \"header-background\" }), h(\"slot\", { key: 'e17a8965f8d3a33c1bfcb056c153d8242e5229fa' })));\n    }\n    get el() { return getElement(this); }\n};\nHeader.style = {\n    ios: IonHeaderIosStyle0,\n    md: IonHeaderMdStyle0\n};\n\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonRouterOutletStyle0 = routerOutletCss;\n\nconst RouterOutlet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.lockController = createLockController();\n        this.gestureOrAnimationInProgress = false;\n        this.mode = getIonMode(this);\n        this.delegate = undefined;\n        this.animated = true;\n        this.animation = undefined;\n        this.swipeHandler = undefined;\n    }\n    swipeHandlerChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeHandler !== undefined);\n        }\n    }\n    async connectedCallback() {\n        const onStart = () => {\n            this.gestureOrAnimationInProgress = true;\n            if (this.swipeHandler) {\n                this.swipeHandler.onStart();\n            }\n        };\n        this.gesture = (await import('./swipe-back-e5394307.js')).createSwipeBackGesture(this.el, () => !this.gestureOrAnimationInProgress && !!this.swipeHandler && this.swipeHandler.canStart(), () => onStart(), (step) => { var _a; return (_a = this.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step); }, (shouldComplete, step, dur) => {\n            if (this.ani) {\n                this.ani.onFinish(() => {\n                    this.gestureOrAnimationInProgress = false;\n                    if (this.swipeHandler) {\n                        this.swipeHandler.onEnd(shouldComplete);\n                    }\n                }, { oneTimeCallback: true });\n                // Account for rounding errors in JS\n                let newStepValue = shouldComplete ? -0.001 : 0.001;\n                /**\n                 * Animation will be reversed here, so need to\n                 * reverse the easing curve as well\n                 *\n                 * Additionally, we need to account for the time relative\n                 * to the new easing curve, as `stepValue` is going to be given\n                 * in terms of a linear curve.\n                 */\n                if (!shouldComplete) {\n                    this.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                    newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n                }\n                else {\n                    newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n                }\n                this.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n            }\n            else {\n                this.gestureOrAnimationInProgress = false;\n            }\n        });\n        this.swipeHandlerChanged();\n    }\n    componentWillLoad() {\n        this.ionNavWillLoad.emit();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /** @internal */\n    async commit(enteringEl, leavingEl, opts) {\n        const unlock = await this.lockController.lock();\n        let changed = false;\n        try {\n            changed = await this.transition(enteringEl, leavingEl, opts);\n        }\n        catch (e) {\n            console.error(e);\n        }\n        unlock();\n        return changed;\n    }\n    /** @internal */\n    async setRouteId(id, params, direction, animation) {\n        const changed = await this.setRoot(id, params, {\n            duration: direction === 'root' ? 0 : undefined,\n            direction: direction === 'back' ? 'back' : 'forward',\n            animationBuilder: animation,\n        });\n        return {\n            changed,\n            element: this.activeEl,\n        };\n    }\n    /** @internal */\n    async getRouteId() {\n        const active = this.activeEl;\n        return active\n            ? {\n                id: active.tagName,\n                element: active,\n                params: this.activeParams,\n            }\n            : undefined;\n    }\n    async setRoot(component, params, opts) {\n        if (this.activeComponent === component && shallowEqualStringMap(params, this.activeParams)) {\n            return false;\n        }\n        // attach entering view to DOM\n        const leavingEl = this.activeEl;\n        const enteringEl = await attachComponent(this.delegate, this.el, component, ['ion-page', 'ion-page-invisible'], params);\n        this.activeComponent = component;\n        this.activeEl = enteringEl;\n        this.activeParams = params;\n        // commit animation\n        await this.commit(enteringEl, leavingEl, opts);\n        await detachComponent(this.delegate, leavingEl);\n        return true;\n    }\n    async transition(enteringEl, leavingEl, opts = {}) {\n        if (leavingEl === enteringEl) {\n            return false;\n        }\n        // emit nav will change event\n        this.ionNavWillChange.emit();\n        const { el, mode } = this;\n        const animated = this.animated && config.getBoolean('animated', true);\n        const animationBuilder = opts.animationBuilder || this.animation || config.get('navAnimation');\n        await transition(Object.assign(Object.assign({ mode,\n            animated,\n            enteringEl,\n            leavingEl, baseEl: el,\n            /**\n             * We need to wait for all Stencil components\n             * to be ready only when using the lazy\n             * loaded bundle.\n             */\n            deepWait: hasLazyBuild(el), progressCallback: opts.progressAnimation\n                ? (ani) => {\n                    /**\n                     * Because this progress callback is called asynchronously\n                     * it is possible for the gesture to start and end before\n                     * the animation is ever set. In that scenario, we should\n                     * immediately call progressEnd so that the transition promise\n                     * resolves and the gesture does not get locked up.\n                     */\n                    if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                        this.gestureOrAnimationInProgress = true;\n                        ani.onFinish(() => {\n                            this.gestureOrAnimationInProgress = false;\n                            if (this.swipeHandler) {\n                                this.swipeHandler.onEnd(false);\n                            }\n                        }, { oneTimeCallback: true });\n                        /**\n                         * Playing animation to beginning\n                         * with a duration of 0 prevents\n                         * any flickering when the animation\n                         * is later cleaned up.\n                         */\n                        ani.progressEnd(0, 0, 0);\n                    }\n                    else {\n                        this.ani = ani;\n                    }\n                }\n                : undefined }, opts), { animationBuilder }));\n        // emit nav changed event\n        this.ionNavDidChange.emit();\n        return true;\n    }\n    render() {\n        return h(\"slot\", { key: '8279a453c66a766e6e383ff59842b4ae070c13a9' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeHandler\": [\"swipeHandlerChanged\"]\n    }; }\n};\nRouterOutlet.style = IonRouterOutletStyle0;\n\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\nconst IonTitleIosStyle0 = titleIosCss;\n\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\nconst IonTitleMdStyle0 = titleMdCss;\n\nconst ToolbarTitle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.color = undefined;\n        this.size = undefined;\n    }\n    sizeChanged() {\n        this.emitStyle();\n    }\n    connectedCallback() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const size = this.getSize();\n        this.ionStyle.emit({\n            [`title-${size}`]: true,\n        });\n    }\n    getSize() {\n        return this.size !== undefined ? this.size : 'default';\n    }\n    render() {\n        const mode = getIonMode(this);\n        const size = this.getSize();\n        return (h(Host, { key: '5a58dc437a6f4257244bbdd7e9a682a17d8c9a6b', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`title-${size}`]: true,\n                'title-rtl': document.dir === 'rtl',\n            }) }, h(\"div\", { key: '58682ea7b8f47a08adfad419b5c76b34784c6501', class: \"toolbar-title\" }, h(\"slot\", { key: '8a9248534e4c3076f5e2dfda38ef86069796851c' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"size\": [\"sizeChanged\"]\n    }; }\n};\nToolbarTitle.style = {\n    ios: IonTitleIosStyle0,\n    md: IonTitleMdStyle0\n};\n\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\nconst IonToolbarIosStyle0 = toolbarIosCss;\n\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\nconst IonToolbarMdStyle0 = toolbarMdCss;\n\nconst Toolbar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.childrenStyles = new Map();\n        this.color = undefined;\n    }\n    componentWillLoad() {\n        const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n        const firstButtons = buttons.find((button) => {\n            return button.slot === 'start';\n        });\n        if (firstButtons) {\n            firstButtons.classList.add('buttons-first-slot');\n        }\n        const buttonsReversed = buttons.reverse();\n        const lastButtons = buttonsReversed.find((button) => button.slot === 'end') ||\n            buttonsReversed.find((button) => button.slot === 'primary') ||\n            buttonsReversed.find((button) => button.slot === 'secondary');\n        if (lastButtons) {\n            lastButtons.classList.add('buttons-last-slot');\n        }\n    }\n    childrenStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.childrenStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            const childKey = `toolbar-${key}`;\n            const newValue = updatedStyles[key];\n            if (newValue !== childStyles[childKey]) {\n                hasStyleChange = true;\n            }\n            if (newValue) {\n                newStyles[childKey] = true;\n            }\n        });\n        if (hasStyleChange) {\n            this.childrenStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const childStyles = {};\n        this.childrenStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        return (h(Host, { key: '4bb3a55001408a3bdf033af76b9196cb96c07c09', class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n            })) }, h(\"div\", { key: '0891db157d6e028c6d03696f13fb510ea91b0296', class: \"toolbar-background\" }), h(\"div\", { key: '95fbc870d808f74af4bb18cd8f8ec8c7828a9e0b', class: \"toolbar-container\" }, h(\"slot\", { key: '84d4a9644660fe00ca085055ca8d12f3647531ad', name: \"start\" }), h(\"slot\", { key: 'fd361dc9c9979f59aed2fedcf94629506fae62b0', name: \"secondary\" }), h(\"div\", { key: '54d2b28616a4627c55766f66dc453707752758a6', class: \"toolbar-content\" }, h(\"slot\", { key: '8f65e0830cce7135640b90eb694e282cb7e5efd2' })), h(\"slot\", { key: 'c78be11a207c8674f222543646398636956087e6', name: \"primary\" }), h(\"slot\", { key: 'ab25e1601ccaa8cb0d81921b849bcb7402aa7826', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nToolbar.style = {\n    ios: IonToolbarIosStyle0,\n    md: IonToolbarMdStyle0\n};\n\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };\n"], "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "d", "createEvent", "e", "readTask", "j", "forceUpdate", "w", "writeTask", "shouldUseCloseWatcher", "p", "printIonWarning", "b", "getIonMode", "c", "config", "a", "isPlatform", "k", "hasLazyBuild", "componentOnReady", "clamp", "inheritAriaAttributes", "s", "shallowEqualStringMap", "isRTL", "createColorClasses", "hostContext", "find<PERSON><PERSON><PERSON><PERSON>nt", "printIonContentErrorMsg", "g", "getScrollElement", "createKeyboardController", "getTimeGivenProgression", "attachComponent", "detachComponent", "createLockController", "t", "transition", "appCss", "IonAppStyle0", "App", "constructor", "hostRef", "componentDidLoad", "_this", "rIC", "_asyncToGenerator", "isHybrid", "window", "getBoolean", "then", "module", "startTapClick", "startStatusTap", "needInputShims", "platform", "startInputShims", "hardwareBackButtonModule", "supportsHardwareBackButtonEvents", "startHardwareBackButton", "blockHardwareBackButton", "startKeyboardAssist", "focusVisible", "startFocusVisible", "setFocus", "elements", "_this2", "render", "mode", "key", "class", "el", "needsShimsIOS", "isAndroid<PERSON><PERSON><PERSON>eb", "callback", "requestIdleCallback", "setTimeout", "style", "buttonsIosCss", "IonButtonsIosStyle0", "buttonsMdCss", "IonButtonsMdStyle0", "Buttons", "collapse", "ios", "md", "contentCss", "IonContentStyle0", "Content", "ionScrollStart", "ionScroll", "ionScrollEnd", "watchDog", "isScrolling", "lastScroll", "queued", "cTop", "cBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resizeTimeout", "tabsElement", "detail", "scrollTop", "scrollLeft", "type", "event", "undefined", "startX", "startY", "startTime", "currentX", "currentY", "velocityX", "velocityY", "deltaX", "deltaY", "currentTime", "data", "color", "fullscreen", "fixedSlotPlacement", "forceOverscroll", "scrollX", "scrollY", "scrollEvents", "connectedCallback", "closest", "closestTabs", "tabsLoadCallback", "resize", "addEventListener", "disconnectedCallback", "onScrollEnd", "removeEventListener", "onResize", "clearTimeout", "offsetParent", "shouldForceOverscroll", "readDimensions", "page", "getPageElement", "top", "Math", "max", "offsetTop", "bottom", "offsetHeight", "dirty", "onScroll", "ev", "timeStamp", "Date", "now", "shouldStart", "onScrollStart", "ts", "updateScrollDetail", "scrollEl", "emit", "_this3", "Promise", "resolve", "getBackgroundElement", "_this4", "backgroundContentEl", "scrollToTop", "duration", "scrollToPoint", "scrollToBottom", "_this5", "y", "scrollHeight", "clientHeight", "scrollByPoint", "x", "_this6", "_this7", "promise", "fromY", "fromX", "step", "linearTime", "min", "easedT", "pow", "floor", "requestAnimationFrame", "clearInterval", "setInterval", "rtl", "transitionShadow", "role", "overscroll", "ref", "id", "part", "name", "getParentElement", "_a", "parentElement", "parentNode", "host", "tabs", "timestamp", "prevX", "prevY", "prevT", "<PERSON><PERSON><PERSON><PERSON>", "handleFooterFade", "baseEl", "maxScroll", "fadeDuration", "fadeStart", "distanceToStart", "scale", "setProperty", "toString", "footerIosCss", "IonFooterIosStyle0", "footerMdCss", "IonFooterMdStyle0", "Footer", "_this8", "keyboardCtrl", "checkCollap<PERSON><PERSON><PERSON>er", "hasFade", "destroyCollapsibleFooter", "pageEl", "contentEl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "contentScrollCallback", "_x", "apply", "arguments", "keyboardVisible", "translucent", "componentDidUpdate", "_this9", "_ref3", "keyboardOpen", "waitForResize", "_x2", "_x3", "destroy", "tabBar", "querySelector", "slot", "TRANSITION", "cloneElement", "tagName", "getCachedEl", "document", "clonedEl", "createElement", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "createHeaderIndex", "headerEl", "toolbars", "querySelectorAll", "Array", "from", "map", "toolbar", "ionTitleEl", "background", "shadowRoot", "innerTitleEl", "ionButtonsEl", "handleContentScroll", "scrollHeaderIndex", "nativeRefresher", "scaleLargeTitles", "setToolbarBackgroundOpacity", "opacity", "removeProperty", "handleToolbarBorderIntersection", "mainHeaderIndex", "isIntersecting", "intersectionRatio", "handleToolbarIntersection", "intersection", "intersectionRect", "intersectionArea", "width", "height", "rootArea", "rootBounds", "isPageHidden", "leftDiff", "abs", "left", "boundingClientRect", "rightDiff", "right", "isPageTransitioning", "setHeaderActive", "hasValidIntersection", "headerIndex", "active", "remove", "removeAttribute", "setAttribute", "for<PERSON>ach", "ionTitle", "titleDiv", "size", "transform", "handleHeaderFade", "condense<PERSON><PERSON>er", "baseElHeight", "headerIosCss", "IonHeaderIosStyle0", "headerMdCss", "IonHeaderMdStyle0", "Header", "_this10", "inheritedAttributes", "setupFadeHeader", "_ref4", "_x4", "_x5", "componentWillLoad", "checkCollapsible<PERSON><PERSON>er", "destroyCollapsibleHeader", "_this11", "hasCondense", "title", "setupCondenseHeader", "intersectionObserver", "disconnect", "collapsibleMainHeader", "_this12", "IntersectionObserver", "headers", "find", "header", "toolbarIntersection", "root", "threshold", "observe", "length", "roleType", "Object", "assign", "routerOutletCss", "IonRouterOutletStyle0", "RouterOutlet", "ionNavWillLoad", "ionNavWillChange", "ionNavDidChange", "lockController", "gestureOrAnimationInProgress", "delegate", "animated", "animation", "swi<PERSON><PERSON><PERSON><PERSON>", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesture", "enable", "_this13", "onStart", "createSwipeBackGesture", "canStart", "ani", "progressStep", "shouldComplete", "dur", "onFinish", "onEnd", "oneTimeCallback", "newStepValue", "easing", "progressEnd", "commit", "enteringEl", "leavingEl", "opts", "_this14", "unlock", "lock", "changed", "console", "error", "setRouteId", "params", "direction", "_this15", "setRoot", "animationBuilder", "element", "activeEl", "getRouteId", "_this16", "activeParams", "component", "_this17", "activeComponent", "_this18", "get", "deepWait", "progressCallback", "progressAnimation", "watchers", "titleIosCss", "IonTitleIosStyle0", "titleMdCss", "IonTitleMdStyle0", "ToolbarTitle", "ionStyle", "sizeChanged", "emitStyle", "getSize", "dir", "toolbarIosCss", "IonToolbarIosStyle0", "toolbarMdCss", "IonToolbarMdStyle0", "<PERSON><PERSON><PERSON>", "childrenStyles", "Map", "buttons", "firstButtons", "button", "buttonsReversed", "reverse", "lastButtons", "childrenStyle", "stopPropagation", "target", "updatedStyles", "newStyles", "childStyles", "hasStyleChange", "keys", "<PERSON><PERSON><PERSON>", "newValue", "set", "value", "ion_app", "ion_buttons", "ion_content", "ion_footer", "ion_header", "ion_router_outlet", "ion_title", "ion_toolbar"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}