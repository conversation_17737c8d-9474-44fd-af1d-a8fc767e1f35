from src.app.ocr import table, header, footer
from src.app.services import pre_processing
from src.app.utils import models_utils
from src.app.utils.document_model import DocumentModel
import uuid
from datetime import datetime
import json
from pathlib import Path
import logging
import traceback
from fastapi import WebSocketDisconnect
import asyncio
from mindee import Client, product
from typing import Tuple, Dict
from src.app.utils import constants

output_dir_txt_after_corr_path = Path("../../output_after_corr_txt")
output_dir_txt_before_corr_path = Path("../../output_before_corr_txt")


class MindeeTokenManager:
    def __init__(self):
        self.config_file = Path("mindee_config.json")
        self.api_tokens = [
            "80c67620bb7e0b518c8a2f19d56ea347",  # <PERSON><PERSON><PERSON>'s token (first token)
            "6e2290d3e9a1f2523c1ddcebd91c6737",  # <PERSON><PERSON>'s token (first token)
            "8565c99dedd5b0520a09b8905404ee73",  # <PERSON><PERSON>'s token (first token)
            # Add others tokens here ...
        ]
        self.models = [
            {"name": "InvoiceV4", "class": product.InvoiceV4},
            {"name": "FinancialDocumentV1", "class": product.FinancialDocumentV1}
        ]
        self.usage_data = self._load_usage_data()

    def _load_usage_data(self) -> Dict:
        """Load usage data from JSON file or create new if doesn't exist or is invalid"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    content = f.read().strip()
                    if content:  # Check if the file is not empty
                        return json.loads(content)
                    else:
                        logging.warning("mindee_config.json is empty. Initializing with default data.")
            except json.JSONDecodeError as e:
                logging.error(f"Error decoding JSON from mindee_config.json: {e}")
                logging.info("Initializing with default data.")
            except Exception as e:
                logging.error(f"Unexpected error reading mindee_config.json: {e}")
                logging.info("Initializing with default data.")
        else:
            logging.info("mindee_config.json not found. Creating new file with default data.")

        # Initialize usage data for each token and model
        usage_data = {
            token: {
                model["name"]: {
                    "count": 0,
                    "active": True
                } for model in self.models
            } for token in self.api_tokens
        }
        self._save_usage_data(usage_data)
        return usage_data

    def _save_usage_data(self, data: Dict) -> None:
        """Save usage data to JSON file"""
        with open(self.config_file, 'w') as f:
            json.dump(data, f, indent=4)

    def _increment_usage(self, token: str, model_name: str) -> None:
        """Increment usage count for specific token and model"""
        self.usage_data[token][model_name]["count"] += 1
        if self.usage_data[token][model_name]["count"] >= 249:
            self.usage_data[token][model_name]["active"] = False
        self._save_usage_data(self.usage_data)

    def get_available_token_and_model(self) -> Tuple[str, object]:
        """Get the next available token and model with auto-reset functionality and detailed logging"""
        exhausted_tokens = []
        exhausted_models = set()

        for token in self.api_tokens:
            token_exhausted = True
            for model in self.models:
                model_name = model["name"]
                if self.usage_data[token][model_name]["active"]:
                    return token, model["class"]
                else:
                    exhausted_models.add(model_name)

            if token_exhausted:
                masked_token = f"{token[:8]}...{token[-4:]}"
                exhausted_tokens.append(masked_token)

        # Log detailed information about exhausted tokens and models
        logging.warning("All tokens have reached their usage limit for at least one model.")
        logging.warning(f"Exhausted tokens: {', '.join(exhausted_tokens)}")
        logging.warning(f"Exhausted models: {', '.join(exhausted_models)}")

        # If no available tokens/models, check if it's a new day and reset if needed
        if self._should_reset_usage():
            logging.info("Performing automatic reset for new day.")
            self.reset_usage_counts()

            # Try again after reset
            for token in self.api_tokens:
                for model in self.models:
                    model_name = model["name"]
                    if self.usage_data[token][model_name]["active"]:
                        return token, model["class"]

        # If still no available tokens after reset, raise exception with detailed info
        raise Exception(f"All tokens and models have reached their usage limit! "
                        f"Exhausted tokens: {', '.join(exhausted_tokens)}. "
                        f"Exhausted models: {', '.join(exhausted_models)}. "
                        f"Please try again tomorrow.")

    def reset_usage_counts(self) -> None:
        """Reset all usage counts and active status"""
        for token in self.api_tokens:
            for model in self.models:
                self.usage_data[token][model["name"]] = {
                    "count": 0,
                    "active": True
                }
        self._save_usage_data(self.usage_data)

    def get_usage_statistics(self) -> Dict:
        """Get current usage statistics for all tokens and models"""
        stats = {
            "total_requests": 0,
            "tokens": {},
            "models": {model["name"]: 0 for model in self.models}
        }

        for token in self.api_tokens:
            token_stats = {
                "total_requests": 0,
                "models": {},
                "active_models": [],
                "inactive_models": []
            }

            for model in self.models:
                model_name = model["name"]
                model_data = self.usage_data[token][model_name]
                requests = model_data["count"]
                is_active = model_data["active"]

                token_stats["total_requests"] += requests
                token_stats["models"][model_name] = {
                    "requests": requests,
                    "remaining": 249 - requests if is_active else 0,
                    "active": is_active
                }

                if is_active:
                    token_stats["active_models"].append(model_name)
                else:
                    token_stats["inactive_models"].append(model_name)

                stats["models"][model_name] += requests

            stats["total_requests"] += token_stats["total_requests"]
            stats["tokens"][token] = token_stats

        return stats

    def print_usage_statistics(self):
        """Print current usage statistics in a formatted way with exhausted tokens/models highlighted"""
        stats = self.get_usage_statistics()

        logging.info("\n========== MINDEE API USAGE STATISTICS ==========")
        logging.info(f"Total Requests Across All Tokens: {stats['total_requests']}")
        logging.info("\n----- Per Model Statistics -----")
        for model_name, requests in stats["models"].items():
            logging.info(f"{model_name}: {requests} requests")

        logging.info("\n----- Per Token Statistics -----")
        exhausted_tokens = []
        exhausted_models = set()
        for token, token_stats in stats["tokens"].items():
            masked_token = f"{token[:8]}...{token[-4:]}"
            logging.info(f"\nToken: {masked_token}")
            logging.info(f"Total Requests: {token_stats['total_requests']}")
            logging.info("Models:")
            token_exhausted = True
            for model_name, model_stats in token_stats["models"].items():
                status = "🟢 ACTIVE" if model_stats["active"] else "🔴 INACTIVE"
                logging.info(f"  - {model_name}: {model_stats['requests']}/249 requests ({status})")
                if model_stats["active"]:
                    logging.info(f"    Remaining: {model_stats['remaining']} requests")
                    token_exhausted = False
                else:
                    exhausted_models.add(model_name)

            if token_exhausted:
                exhausted_tokens.append(masked_token)

        if exhausted_tokens:
            logging.info("\n⚠️ EXHAUSTED TOKENS:")
            for token in exhausted_tokens:
                logging.info(f"  - {token}")

        if exhausted_models:
            logging.info("\n⚠️ EXHAUSTED MODELS:")
            for model in exhausted_models:
                logging.info(f"  - {model}")


# Function to process a single model-image pair with a specific module
async def process_model_image_with_module(model, image_path, output_dir_path, module='default', last_run=False,
                                          isAPI=False,
                                          images_url_path=None, manager=None, current_progress=0, increment_per_image=0,
                                          job_id=None, user_id=None, src_app='winpluspharma'):
    try:
        # Generate a random UUID for the image filename
        if images_url_path is None:
            images_url_path = {"origin": None, "cropped": None, "filtered": None}

        if isAPI:
            random_id = str(uuid.uuid4())[:8]  # Extract the first 8 characters of the UUID
        else:
            random_id = "000"

        # # Image Processed Paths
        # header_file_name = 'BL_header_cropped_' + random_id + '_' + model + '_processed.jpg'
        # footer_file_name = 'BL_footer_cropped_' + random_id + '_' + model + '_processed.jpg'
        # image_table_path = 'BL_table_final_format_' + random_id + '_' + model + '.jpg'

        # Image Cropped and not Processed Paths
        header_file_name = 'BL_header_cropped_' + random_id + '_' + model + '__.jpg'
        footer_file_name = 'BL_footer_cropped_' + random_id + '_' + model + '__.jpg'
        image_table_path = 'BL_table_cropped_' + random_id + '_' + model + '__.jpg'

        image_header_path = str(output_dir_path / header_file_name)
        image_footer_path = str(output_dir_path / footer_file_name)
        image_table_path = str(output_dir_path / image_table_path)

        # Crop the image to Header, Footer, and Table parts in the directory
        pre_processing.process_image_crop(image_path, output_dir_path, model, random_id)

        # Notify the client about progress (Processing complete for current image)
        if manager:
            current_progress += increment_per_image * 0.25
            try:
                await manager.send_progress(job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

        # # Pre Processing the Image
        # pre_processing.apply_hide_columns(output_dir_path, model, random_id)

        # Notify progress after preprocessing
        if manager:
            current_progress += increment_per_image * 0.25
            try:
                await manager.send_progress(job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

        # Extract Data of Header part JSON
        ocr_header_all = header.OCR_All_HEADER(image_header_path, model, random_id, 'default')

        # Notify progress after header extraction
        if manager:
            current_progress += increment_per_image * 0.25
            try:
                await manager.send_progress(job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

        # Extract Data of Footer part JSON
        ocr_footer_all = footer.OCR_All_FOOTER(image_footer_path, model, random_id, 'default')

        # Notify progress after footer extraction
        if manager:
            current_progress += increment_per_image * 0.25
            try:
                await manager.send_progress(job_id, current_progress)
                await asyncio.sleep(0.01)
            except (WebSocketDisconnect, RuntimeError) as e:
                logging.error(f"Error sending progress update: {e}")

        # Extract Data of Table part JSON
        line_corrected, ocr_table_all, tnp_result, tess_without_corr = table.OCR_All_TABLE(image_table_path, model,
                                                                                           module)

        # Instantiate the DocumentModel
        documentModel = DocumentModel()

        # Populate the model with OCR results
        documentModel.header = ocr_header_all.header
        documentModel.table = ocr_table_all.table
        documentModel.footer = ocr_footer_all.footer

        # Set general infos
        documentModel.set_general_info(
            random_id=random_id,
            model_name=model,
            user_id=user_id,
            date_export=datetime.now(),  # Optionally pass a specific datetime
            images_url_path=images_url_path,
            src_app=src_app
        )

        logging.info('images_url_path origin: %s', images_url_path['origin'])
        logging.info('images_url_path cropped: %s', images_url_path['cropped'])
        logging.info('filtered: %s', images_url_path['filtered'])

        if isAPI:

            logging.info(
                "----------------------------- Tesseract Origin Output -------------------------------------------------")
            logging.info(
                "------------------------------------------------------------------------------------------------------")
            logging.info(tess_without_corr)
            logging.info(
                "------------------------ Corrected data ocr without structuring data isAPI -----------------------------------")
            logging.info(
                "--------------------------------------------------------------------------------------------------------")
            for line in line_corrected:
                logging.info(line)

            # Check if documentModel table is empty then call the Advanced Mindee API for the current image
            if len(documentModel.table) == 0:
                documentModel, current_progress = await advanced_mindee_process_ocr(image_path, model, isAPI=True,
                                                                                    user_id=user_id,
                                                                                    images_url_path=images_url_path,
                                                                                    manager=manager,
                                                                                    current_progress=current_progress,
                                                                                    increment_per_image=9999,
                                                                                    job_id=job_id,
                                                                                    src_app=src_app)

            return documentModel, current_progress
        else:

            # Merge the result of table and Header to JSON file (Old method - simple)
            final_result_documentModel = models_utils.store_and_save_documentModel_toJson(documentModel, model,
                                                                                          module)  ## just for table data

            # Format TAP
            result_BL_TAP = models_utils.Export_Format_TAP(documentModel)

            """ Logs / Debug """
            logging.info(f"Model: {model}, Module: {module}")
            logging.info(
                "-------------------------------------- BL OCR Result --------------------------------------------------")
            logging.info(
                "------------------------------------------------------------------------------------------------------")
            logging.info(final_result_documentModel)
            logging.info(
                "----------------------------------------- TAP Result -------------------------------------------------")
            logging.info(
                "------------------------------------------------------------------------------------------------------")
            # logging.info(result_BL_TAP)
            logging.info(json.dumps(result_BL_TAP, indent=4, ensure_ascii=False))
            logging.info(
                "----------------------------- Tesseract Origin Output -------------------------------------------------")
            logging.info(
                "------------------------------------------------------------------------------------------------------")
            logging.info(tess_without_corr)
            logging.info(
                "------------------------ Corrected data ocr without structuring data isNotAPI -----------------------------------")
            logging.info(
                "--------------------------------------------------------------------------------------------------------")
            for line in line_corrected:
                logging.info(line)

            # """ Store the corrected lines in a file """
            output_file_path_after_corr = output_dir_txt_after_corr_path / f"{module}_corrected_ocr.txt"
            output_file_path_after_corr.mkdir(exist_ok=True, parents=True)
            # with open(output_file_path_after_corr, 'a') as f:
            #     f.write(f"Processing model: {model}\n")
            #     for line in line_corrected:
            #         f.write(line + "\n")
            #     f.write("\n")

            """ Store the lines not corrected in a file """
            output_file_path_before_corr = output_dir_txt_before_corr_path / f"{module}_tesseract_ocr.txt"
            output_file_path_before_corr.mkdir(exist_ok=True, parents=True)

            # Create the result dictionary structure
            result_dict = {
                "general": {
                    "model_name": model,
                    "date_export": datetime.now().isoformat(),
                    "random_id": random_id
                },
                "header": {
                    "name_fournisseur": documentModel.header.name_fournisseur,
                    "num_bl": documentModel.header.num_bl,
                    "date_bl": documentModel.header.date_bl,
                    "ice_fournisseur": documentModel.header.ice_fournisseur,
                    "adresse_fournisseur": documentModel.header.adresse_fournisseur,
                    "info_client": documentModel.header.info_client,
                    "ice_client": documentModel.header.ice_client
                },
                "table": []
            }

            # Add table entries
            filtered_table = []
            for entry in documentModel.table:
                # Only exclude rows where designation is exactly an empty string ""
                # Keep rows where designation is None/null or has actual content
                if entry.designation != "":
                    table_item = {
                        "designation": entry.designation.strip() if entry.designation else entry.designation,
                        "quantity": str(entry.quantity) if entry.quantity is not None else entry.quantity,  # Convert to string
                        "pph": entry.pph,
                        "ppv": entry.ppv,
                        "total_ttc": entry.total_ttc
                    }
                    filtered_table.append(table_item)

            result_dict["table"] = filtered_table

            # Return the result_dict instead of implicitly returning None
            return result_dict

            # with open(output_file_path_before_corr, 'a') as f:
            #     f.write(f"Processing model: {model}\n")
            #     tess_without_corr = tess_without_corr.split('\n')
            #     for line in tess_without_corr:
            #         f.write(line + "\n")
            #     f.write("\n")

            # if last_run:
            #     """ Generate Docx Statistic for each output text (bafore and after correction) """
            #     main_matching_data_docx('after')  # param : ['after', 'before']
            #
            #     """ Generate Statistic as PDF File from docx corrected """
            #     generate_states_PDF('after')  # param : ['after', 'before']
            #
            #     """ Generate Matrix PDF with Statistic as PDF File """
            #     _matrix_generate_states_PDF('after')  # param : ['after', 'before']
            #
            #     """ Matching JSON Outputs - Generate Statistic as DOCX """
            #     # generate_states_Json_Docx()

            # # Notify progress after complete processing
            # if manager:
            #     await manager.send_progress(job_id, current_progress + increment_per_image * 0.2)

    except (IOError, ValueError, AttributeError) as e:
        logging.error(f"Error in process_model_image_with_module: {traceback.format_exc()}")
        return None


async def advanced_mindee_process_ocr(image_path, model, isAPI=False, user_id=None,
                                      images_url_path=None, manager=None,
                                      current_progress=0, increment_per_image=0, job_id=None, src_app='winpluspharma'):
    try:
        # Initialize token manager
        token_manager = MindeeTokenManager()

        # Get available token and model
        api_token, mindee_model = token_manager.get_available_token_and_model()

        # Log token and model selection
        logging.info("=" * 50)
        logging.info("MINDEE API REQUEST DETAILS")
        logging.info(f"Selected Token: {api_token[:8]}...{api_token[-4:]}")
        logging.info(f"Selected Model: {mindee_model.__name__}")

        # Print current usage statistics before processing
        logging.info("\n----- Current Usage Statistics (Before Processing) -----")
        token_manager.print_usage_statistics()

        # Initialize Mindee client with the selected token
        mindee_client = Client(api_key=api_token)
        input_doc = mindee_client.source_from_path(image_path)

        if isAPI:
            random_id = str(uuid.uuid4())[:8]
        else:
            random_id = "000"

        # Update progress
        if manager and increment_per_image != 9999:
            try:
                current_progress += increment_per_image * 0.25
                await manager.send_progress(job_id, current_progress)
            except Exception as e:
                logging.error(f"Error updating progress: {str(e)}")

        # Parse the document using the selected Mindee model
        result = mindee_client.parse(mindee_model, input_doc)

        # Increment usage count for this token and model
        token_manager._increment_usage(api_token, mindee_model.__name__)

        logging.info("\n----- Updated Usage Statistics (After Processing) -----")
        token_manager.print_usage_statistics()

        # Log processing success
        logging.info("=" * 50)
        logging.info("MINDEE API PROCESSING COMPLETE")
        logging.info(f"Document processed successfully with {mindee_model.__name__}")

        prediction = result.document.inference.prediction

        # Create DocumentModel instance
        document_model = DocumentModel()

        # Set general information
        document_model.set_general_info(
            random_id=random_id,
            model_name=model,
            user_id=user_id,
            date_export=datetime.now(),
            images_url_path=images_url_path,
            src_app=src_app
        )

        # Common fields for both models
        document_model.header.num_bl = prediction.invoice_number.value if hasattr(prediction,
                                                                                  'invoice_number') else None
        document_model.header.date_bl = prediction.date.value if hasattr(prediction, 'date') else None

        # Model-specific processing
        if mindee_model == product.InvoiceV4:
            # Process InvoiceV4 specific fields
            document_model.header.name_fournisseur = prediction.supplier_name.value if hasattr(prediction,
                                                                                               'supplier_name') else None
            document_model.header.ice_fournisseur = next((reg.value for reg in prediction.supplier_company_registrations
                                                          if reg.type == "ICE"), None) if hasattr(prediction,
                                                                                                  'supplier_company_registrations') else None
            document_model.header.adresse_fournisseur = prediction.supplier_address.value if hasattr(prediction,
                                                                                                     'supplier_address') else None
            document_model.header.info_client = prediction.customer_name.value if hasattr(prediction,
                                                                                          'customer_name') else None

            # Handle customer registration numbers for InvoiceV4
            if hasattr(prediction, 'customer_company_registrations'):
                customer_registrations = prediction.customer_company_registrations
                if customer_registrations:
                    ice_registration = next((reg for reg in customer_registrations if reg.type == "ICE"), None)
                    document_model.header.ice_client = ice_registration.value if ice_registration else \
                        customer_registrations[0].value
                else:
                    document_model.header.ice_client = None

        elif mindee_model == product.FinancialDocumentV1:
            # Process FinancialDocumentV1 specific fields
            document_model.header.name_fournisseur = prediction.supplier_name.value if hasattr(prediction,
                                                                                               'supplier_name') else None
            document_model.header.ice_fournisseur = next((reg.value for reg in prediction.supplier_company_registrations
                                                          if reg.type == "ICE"), None) if hasattr(prediction,
                                                                                                  'supplier_company_registrations') else None
            document_model.header.adresse_fournisseur = prediction.supplier_address.value if hasattr(prediction,
                                                                                                     'supplier_address') else None
            document_model.header.info_client = prediction.customer_name.value if hasattr(prediction,
                                                                                          'customer_name') else None

            # Handle customer registration numbers for FinancialDocumentV1
            if hasattr(prediction, 'customer_company_registrations'):
                customer_registrations = prediction.customer_company_registrations
                if customer_registrations:
                    ice_registration = next((reg for reg in customer_registrations if reg.type == "ICE"), None)
                    document_model.header.ice_client = ice_registration.value if ice_registration else \
                        customer_registrations[0].value
                else:
                    document_model.header.ice_client = None

        # Process line items (common for both models)
        if hasattr(prediction, 'line_items'):
            for item in prediction.line_items:
                table_entry = document_model.Table()
                # Clean the designation by removing short words from the beginning
                original_description = item.description if item.description else ""
                table_entry.designation = clean_designation(original_description)
                table_entry.quantity = item.quantity
                table_entry.code_produit = item.product_code

                # Calculate and validate PPH
                if item.quantity and item.total_amount:
                    try:
                        quantity = float(item.quantity) if item.quantity else 0
                        total_amount = float(item.total_amount) if item.total_amount else 0

                        # Adjust total_amount for SOPHACA and SOPHADIMS
                        if model in constants.list_models_prices_without_coma and total_amount:
                            total_amount /= 100

                        if quantity > 0:
                            calculated_pph = total_amount / quantity
                            table_entry.pph = round(calculated_pph, 2)

                            if item.unit_price:
                                try:
                                    unit_price = float(item.unit_price)
                                    # Adjust unit_price for SOPHACA and SOPHADIMS
                                    if model in constants.list_models_prices_without_coma:
                                        unit_price /= 100
                                    difference_percentage = abs(calculated_pph - unit_price) / unit_price * 100

                                    if difference_percentage > 5:
                                        table_entry.ppv = unit_price
                                    else:
                                        table_entry.ppv = None
                                except (ValueError, TypeError):
                                    table_entry.ppv = None
                    except (ValueError, TypeError) as e:
                        logging.warning(f"Error calculating PPH/PPV for item {item.description}: {str(e)}")
                        if item.unit_price:
                            try:
                                table_entry.pph = float(item.unit_price)
                                if model in constants.list_models_prices_without_coma:
                                    table_entry.pph /= 100
                            except (ValueError, TypeError):
                                table_entry.pph = None
                        else:
                            table_entry.pph = None
                        table_entry.ppv = None
                else:
                    # Handle case when quantity or total_amount is missing
                    if item.unit_price:
                        try:
                            table_entry.pph = float(item.unit_price)
                            if model in constants.list_models_prices_without_coma and table_entry.pph:
                                table_entry.pph /= 100
                        except (ValueError, TypeError):
                            table_entry.pph = None

                # Adjust total_ttc for SOPHACA and SOPHADIMS
                if item.total_amount:
                    try:
                        table_entry.total_ttc = float(item.total_amount)
                        if model in constants.list_models_prices_without_coma:
                            table_entry.total_ttc /= 100
                    except (ValueError, TypeError):
                        table_entry.total_ttc = None
                else:
                    table_entry.total_ttc = None

                document_model.table.append(table_entry)

        # Populate footer information (common for both models)
        document_model.footer.total_ttc_global = prediction.total_amount.value if hasattr(prediction,
                                                                                          'total_amount') else None
        document_model.footer.total_ttc = prediction.total_net.value if hasattr(prediction, 'total_net') else None

        # Log processing results
        logging.info(f"Mindee API Processing Results ({mindee_model.__name__}):")
        logging.info(f"Supplier Name: {document_model.header.name_fournisseur}")
        logging.info(f"Customer Name: {document_model.header.info_client}")
        logging.info(f"ICE Client: {document_model.header.ice_client}")
        logging.info(f"Number of line items: {len(document_model.table)}")

        if manager and increment_per_image != 9999:
            try:
                current_progress += increment_per_image * 0.75
                await manager.send_progress(job_id, current_progress)
            except Exception as e:
                logging.error(f"Error updating progress: {str(e)}")

        if isAPI:
            return document_model, current_progress
        else:
            logging.info('************----- End Process -----************')
            return None

    except Exception as e:
        logging.error("=" * 50)
        logging.error("MINDEE API PROCESSING FAILED")
        logging.error(f"Error details: {str(e)}")
        logging.error(traceback.format_exc())
        raise Exception(f"Failed to process document with Mindee API: {str(e)}")


# This helper function at the beginning of the file
def clean_designation(designation: str) -> str:
    """
    Remove words with less than 3 characters from the beginning of the designation.
    """
    if not designation:
        return ""

    words = designation.split()
    # Keep track of words to remove from the start
    start_index = 0

    # Check words from the beginning
    for i, word in enumerate(words):
        # Stop if we find a word with 3 or more characters
        if len(word.strip()) >= 2:
            start_index = i
            break
        start_index = i + 1

    # Join remaining words
    cleaned_designation = ' '.join(words[start_index:])
    return cleaned_designation.strip()
